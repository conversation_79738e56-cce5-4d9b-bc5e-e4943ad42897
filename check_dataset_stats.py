#!/usr/bin/env python3
"""
Script untuk menghitung statistik deskriptif dataset yang sebenarnya
untuk memverifikasi Tabel 4.1 di BAB 4
"""

import pandas as pd
import numpy as np

def calculate_dataset_statistics():
    """Calculate real dataset statistics"""
    
    # Load the main dataset
    df = pd.read_csv('dataset/processed/weekly_merged_dataset_with_gamification.csv')
    
    print("="*80)
    print("📊 REAL DATASET STATISTICS VERIFICATION")
    print("="*80)
    
    print(f"\n📈 Dataset Overview:")
    print(f"   • Total Observations: {len(df)}")
    print(f"   • Unique Participants: {df['identity'].nunique()}")
    print(f"   • Total Features: {len(df.columns)}")
    
    # Key variables for Tabel 4.1
    key_variables = [
        'total_distance_km',
        'avg_distance_km', 
        'activity_days',
        'total_cycles',
        'work_days',
        'consistency_score',
        'activity_points',
        'productivity_points',
        'achievement_rate',
        'gamification_balance'
    ]
    
    print(f"\n📊 STATISTIK DESKRIPTIF UNTUK TABEL 4.1:")
    print("="*80)
    
    stats_data = []
    
    for var in key_variables:
        if var in df.columns:
            mean_val = df[var].mean()
            std_val = df[var].std()
            min_val = df[var].min()
            max_val = df[var].max()
            n_val = df[var].count()
            
            stats_data.append({
                'Variable': var,
                'Mean': round(mean_val, 2),
                'Std': round(std_val, 2),
                'Min': round(min_val, 2),
                'Max': round(max_val, 2),
                'N': n_val
            })
            
            print(f"   {var}:")
            print(f"      Mean: {mean_val:.2f}")
            print(f"      Std:  {std_val:.2f}")
            print(f"      Min:  {min_val:.2f}")
            print(f"      Max:  {max_val:.2f}")
            print(f"      N:    {n_val}")
            print()
        else:
            print(f"   ❌ {var}: NOT FOUND in dataset")
    
    # Create comparison table
    print("\n📋 TABEL 4.1 VERIFICATION:")
    print("="*80)
    print("| Variable | Mean | Std | Min | Max | N |")
    print("|----------|------|-----|-----|-----|---|")
    
    for stat in stats_data:
        print(f"| {stat['Variable']} | {stat['Mean']} | {stat['Std']} | {stat['Min']} | {stat['Max']} | {stat['N']} |")
    
    # Check what's in current Tabel 4.1 vs reality
    print(f"\n🔍 COMPARISON WITH CURRENT TABEL 4.1:")
    print("="*80)
    
    current_table_values = {
        'Total Distance Km': {'mean': 7.7, 'std': 5.8, 'min': 0.9, 'max': 36},
        'Avg Distance Km': {'mean': 3.2, 'std': 2.1, 'min': 0.5, 'max': 12.8},
        'Activity Days': {'mean': 1.56, 'std': 0.78, 'min': 1, 'max': 7},
        'Total Cycles': {'mean': 2.81, 'std': 2.15, 'min': 1, 'max': 16},
        'Work Days': {'mean': 4.2, 'std': 1.8, 'min': 1, 'max': 7},
        'Consistency Score': {'mean': 0.61, 'std': 0.22, 'min': 0.35, 'max': 1},
        'Activity Points': {'mean': 82.97, 'std': 22.01, 'min': 15, 'max': 100},
        'Productivity Points': {'mean': 50.27, 'std': 28.82, 'min': 20, 'max': 100},
        'Achievement Rate': {'mean': 0.67, 'std': 0.2, 'min': 0.22, 'max': 1},
        'Gamification Balance': {'mean': 37.01, 'std': 26.85, 'min': 0, 'max': 80}
    }
    
    # Find corresponding real values
    real_stats = {}
    for stat in stats_data:
        var_name = stat['Variable']
        if var_name == 'total_distance_km':
            real_stats['Total Distance Km'] = stat
        elif var_name == 'avg_distance_km':
            real_stats['Avg Distance Km'] = stat
        elif var_name == 'activity_days':
            real_stats['Activity Days'] = stat
        elif var_name == 'total_cycles':
            real_stats['Total Cycles'] = stat
        elif var_name == 'work_days':
            real_stats['Work Days'] = stat
        elif var_name == 'consistency_score':
            real_stats['Consistency Score'] = stat
        elif var_name == 'activity_points':
            real_stats['Activity Points'] = stat
        elif var_name == 'productivity_points':
            real_stats['Productivity Points'] = stat
        elif var_name == 'achievement_rate':
            real_stats['Achievement Rate'] = stat
        elif var_name == 'gamification_balance':
            real_stats['Gamification Balance'] = stat
    
    # Compare values
    print("Variable | Current Table | Real Data | Match?")
    print("---------|---------------|-----------|-------")
    
    for var_name, current_vals in current_table_values.items():
        if var_name in real_stats:
            real_vals = real_stats[var_name]
            
            # Check if values match (with some tolerance)
            mean_match = abs(current_vals['mean'] - real_vals['Mean']) < 0.1
            std_match = abs(current_vals['std'] - real_vals['Std']) < 0.1
            min_match = abs(current_vals['min'] - real_vals['Min']) < 0.1
            max_match = abs(current_vals['max'] - real_vals['Max']) < 0.1
            
            overall_match = mean_match and std_match and min_match and max_match
            match_status = "✅ YES" if overall_match else "❌ NO"
            
            print(f"{var_name} | Mean: {current_vals['mean']}, Std: {current_vals['std']} | Mean: {real_vals['Mean']}, Std: {real_vals['Std']} | {match_status}")
        else:
            print(f"{var_name} | Available | NOT FOUND | ❌ NO")
    
    print(f"\n🎯 CONCLUSION:")
    print("="*80)
    
    # Count matches
    total_vars = len(current_table_values)
    matches = 0
    
    for var_name, current_vals in current_table_values.items():
        if var_name in real_stats:
            real_vals = real_stats[var_name]
            mean_match = abs(current_vals['mean'] - real_vals['Mean']) < 0.1
            std_match = abs(current_vals['std'] - real_vals['Std']) < 0.1
            if mean_match and std_match:
                matches += 1
    
    accuracy_percentage = (matches / total_vars) * 100
    
    if accuracy_percentage >= 80:
        print(f"✅ TABEL 4.1 ACCURATE: {matches}/{total_vars} variables match ({accuracy_percentage:.1f}%)")
        print("   Current table values are consistent with real dataset")
    elif accuracy_percentage >= 60:
        print(f"⚠️ TABEL 4.1 PARTIALLY ACCURATE: {matches}/{total_vars} variables match ({accuracy_percentage:.1f}%)")
        print("   Some values need updating")
    else:
        print(f"❌ TABEL 4.1 NEEDS UPDATE: Only {matches}/{total_vars} variables match ({accuracy_percentage:.1f}%)")
        print("   Table needs significant revision")
    
    return stats_data, real_stats

if __name__ == "__main__":
    calculate_dataset_statistics()
