# ============================================================
# FEATURE LISTS GENERATED FROM ABLATION STUDY
# ============================================================

# All features used in analysis
ALL_FEATURES = [
    'achievement_rate',
    'activity_days',
    'activity_points',
    'avg_distance_km',
    'avg_time_minutes',
    'consistency_score',
    'gamification_balance',
    'pomokit_title_count',
    'pomokit_title_length',
    'pomokit_unique_words',
    'productivity_points',
    'strava_title_count',
    'strava_title_length',
    'strava_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_distance_km',
    'total_time_minutes',
    'total_title_diversity',
    'work_days',
]

# Optimal feature set (recommended for production)
OPTIMAL_FEATURES = [
    'achievement_rate',
    'activity_days',
    'consistency_score',
    'gamification_balance',
    'pomokit_title_count',
    'pomokit_title_length',
    'pomokit_unique_words',
    'strava_title_count',
    'title_balance_ratio',
    'total_cycles',
    'total_title_diversity',
    'work_days',
]

# Important features (high impact when removed)
IMPORTANT_FEATURES = [
    'consistency_score',
    'title_balance_ratio',
]

# Noise features (improve model when removed)
NOISE_FEATURES = [
    'avg_distance_km',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]
# X_no_noise = X.drop(columns=NOISE_FEATURES)
