"""
Clean Ablation Study - Systematic Feature Elimination Analysis
Author: Research Team
Date: 2025-06-28
Purpose: Analyze individual feature importance through systematic elimination
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_validate
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AblationStudy:
    """
    Systematic ablation study for feature importance analysis.
    
    This class performs single-feature elimination to understand
    the contribution of each feature to model performance.
    """
    
    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        """
        Initialize ablation study.
        
        Args:
            data_path: Path to the dataset CSV file
            target_column: Name of the target variable column
            random_state: Random seed for reproducibility
        """
        self.data_path = Path(data_path)
        self.target_column = target_column
        self.random_state = random_state
        self.df = None
        self.features = None
        self.results = []
        
        # Validation parameters
        self.cv_folds = 5
        self.cv_shuffle = True
        
        # Model parameters
        self.model_params = {
            'random_state': random_state,
            'max_iter': 1000,
            'class_weight': 'balanced',
            'solver': 'liblinear'
        }
    
    def load_data(self) -> None:
        """Load and validate dataset."""
        if not self.data_path.exists():
            raise FileNotFoundError(f"Dataset not found: {self.data_path}")
        
        logger.info(f"Loading dataset from {self.data_path}")
        self.df = pd.read_csv(self.data_path)
        
        # Validate target column
        if self.target_column not in self.df.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in dataset")
        
        # Get feature columns (exclude target)
        self.features = [col for col in self.df.columns if col != self.target_column]
        
        # Filter numeric features only
        numeric_features = []
        for feature in self.features:
            if self.df[feature].dtype in ['int64', 'float64', 'int32', 'float32']:
                numeric_features.append(feature)
        
        self.features = numeric_features
        
        logger.info(f"Dataset loaded: {len(self.df)} samples, {len(self.features)} features")
        logger.info(f"Features: {self.features}")
        
        # Log target distribution
        target_dist = self.df[self.target_column].value_counts()
        logger.info(f"Target distribution: {target_dist.to_dict()}")
    
    def prepare_data(self, features_to_use: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare data for modeling.
        
        Args:
            features_to_use: List of feature names to include
            
        Returns:
            Tuple of (X, y) arrays
        """
        # Select features and handle missing values
        X = self.df[features_to_use].fillna(0).values
        
        # Encode target variable
        le = LabelEncoder()
        y = le.fit_transform(self.df[self.target_column])
        
        return X, y
    
    def create_model_pipeline(self) -> Pipeline:
        """Create standardized model pipeline."""
        return Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', LogisticRegression(**self.model_params))
        ])
    
    def evaluate_feature_set(self, features_to_use: List[str], 
                           experiment_name: str) -> Dict:
        """
        Evaluate model performance with given feature set.
        
        Args:
            features_to_use: List of features to include in model
            experiment_name: Name for this experiment
            
        Returns:
            Dictionary with evaluation results
        """
        logger.info(f"Evaluating: {experiment_name}")
        
        try:
            # Prepare data
            X, y = self.prepare_data(features_to_use)
            
            # Create model pipeline
            pipeline = self.create_model_pipeline()
            
            # Cross-validation setup
            cv = StratifiedKFold(
                n_splits=self.cv_folds,
                shuffle=self.cv_shuffle,
                random_state=self.random_state
            )
            
            # Perform cross-validation
            scoring = ['accuracy', 'f1_macro', 'precision_macro', 'recall_macro']
            cv_results = cross_validate(
                pipeline, X, y,
                cv=cv,
                scoring=scoring,
                return_train_score=True,
                n_jobs=-1
            )
            
            # Compile results
            result = {
                'experiment_name': experiment_name,
                'features_used': features_to_use.copy(),
                'feature_count': len(features_to_use),
                'accuracy_mean': cv_results['test_accuracy'].mean(),
                'accuracy_std': cv_results['test_accuracy'].std(),
                'f1_macro_mean': cv_results['test_f1_macro'].mean(),
                'f1_macro_std': cv_results['test_f1_macro'].std(),
                'precision_macro_mean': cv_results['test_precision_macro'].mean(),
                'recall_macro_mean': cv_results['test_recall_macro'].mean(),
                'train_accuracy_mean': cv_results['train_accuracy'].mean(),
                'overfitting_score': cv_results['train_accuracy'].mean() - cv_results['test_accuracy'].mean(),
                'status': 'success'
            }
            
            logger.info(f"  Result: {result['accuracy_mean']:.4f} ± {result['accuracy_std']:.4f}")

            # Display real-time impact if baseline is available
            if hasattr(self, '_baseline_accuracy') and 'Without_' in experiment_name:
                impact = result['accuracy_mean'] - self._baseline_accuracy
                impact_pct = impact * 100
                interpretation = self._interpret_impact(impact)
                feature_name = experiment_name.replace('Without_', '')

                print(f"  📊 Feature Impact: {feature_name}")
                print(f"     • Impact when removed: {impact_pct:+.2f}%")
                print(f"     • Status: {interpretation}")
                if impact > 0:
                    print(f"     • 💡 Removing this feature IMPROVES model by {impact_pct:.2f}%")
                elif impact < -0.05:
                    print(f"     • ⚠️ This feature is IMPORTANT - removing hurts by {abs(impact_pct):.2f}%")
                print()

        except Exception as e:
            logger.error(f"  Failed: {str(e)}")
            result = {
                'experiment_name': experiment_name,
                'features_used': features_to_use.copy(),
                'feature_count': len(features_to_use),
                'accuracy_mean': 0.0,
                'accuracy_std': 0.0,
                'status': f'failed: {str(e)}'
            }
        
        return result
    
    def run_baseline_evaluation(self) -> Dict:
        """Evaluate baseline model with all features."""
        logger.info("Running baseline evaluation with all features")
        baseline_result = self.evaluate_feature_set(self.features, "Baseline_All_Features")

        # Store baseline accuracy for real-time impact calculation
        if baseline_result['status'] == 'success':
            self._baseline_accuracy = baseline_result['accuracy_mean']
            print(f"\n🎯 BASELINE ESTABLISHED: {self._baseline_accuracy:.4f} ({self._baseline_accuracy*100:.2f}%)")
            print("Now testing impact of removing each feature...\n")

        return baseline_result
    
    def run_single_feature_elimination(self) -> List[Dict]:
        """
        Run systematic single-feature elimination study.
        
        Returns:
            List of evaluation results for each elimination experiment
        """
        logger.info("Starting single-feature elimination study")
        
        elimination_results = []
        
        for feature_to_remove in self.features:
            # Create feature set without current feature
            remaining_features = [f for f in self.features if f != feature_to_remove]
            experiment_name = f"Without_{feature_to_remove}"
            
            # Evaluate
            result = self.evaluate_feature_set(remaining_features, experiment_name)
            elimination_results.append(result)
        
        return elimination_results
    
    def analyze_feature_importance(self, baseline_result: Dict, 
                                 elimination_results: List[Dict]) -> pd.DataFrame:
        """
        Analyze feature importance based on elimination results.
        
        Args:
            baseline_result: Baseline evaluation result
            elimination_results: Results from elimination experiments
            
        Returns:
            DataFrame with feature importance analysis
        """
        logger.info("Analyzing feature importance")
        
        baseline_accuracy = baseline_result['accuracy_mean']
        
        importance_data = []
        
        for result in elimination_results:
            if result['status'] == 'success':
                feature_removed = result['experiment_name'].replace('Without_', '')
                accuracy_without = result['accuracy_mean']
                impact = accuracy_without - baseline_accuracy
                
                importance_data.append({
                    'feature': feature_removed,
                    'baseline_accuracy': baseline_accuracy,
                    'accuracy_without_feature': accuracy_without,
                    'impact_when_removed': impact,
                    'importance_score': -impact,  # Negative impact = high importance
                    'interpretation': self._interpret_impact(impact)
                })
        
        # Create DataFrame and sort by importance
        importance_df = pd.DataFrame(importance_data)
        importance_df = importance_df.sort_values('importance_score', ascending=False)
        importance_df['rank'] = range(1, len(importance_df) + 1)
        
        return importance_df

    def generate_feature_impact_report(self, importance_df: pd.DataFrame, baseline_result: Dict) -> str:
        """
        Generate detailed feature impact report.

        Args:
            importance_df: Feature importance analysis results
            baseline_result: Baseline evaluation result

        Returns:
            Formatted report string
        """
        baseline_acc = baseline_result['accuracy_mean']

        report = []
        report.append("="*80)
        report.append("📊 FEATURE IMPACT ANALYSIS REPORT")
        report.append("="*80)

        # Dataset and Feature Information
        report.append(f"\n📋 DATASET INFORMATION:")
        report.append(f"   • Dataset Path: {self.data_path}")
        report.append(f"   • Target Column: {self.target_column}")
        report.append(f"   • Total Samples: {len(self.y) if hasattr(self, 'y') else 'N/A'}")
        report.append(f"   • Total Features: {len(self.features)}")

        # All Features Used
        report.append(f"\n📝 ALL FEATURES USED IN ANALYSIS:")
        for i, feature in enumerate(sorted(self.features), 1):
            report.append(f"   {i:2d}. {feature}")

        # Features by Category
        critical_features = importance_df[importance_df['impact_when_removed'] <= -0.05]
        beneficial_features = importance_df[importance_df['impact_when_removed'] > 0.01]
        neutral_features = importance_df[
            (importance_df['impact_when_removed'] > -0.05) &
            (importance_df['impact_when_removed'] <= 0.01)
        ]

        # Features to Keep (Critical + Important)
        important_threshold = -0.02  # Features with impact <= -2%
        features_to_keep = importance_df[importance_df['impact_when_removed'] <= important_threshold]

        # Features to Remove (Noise)
        features_to_remove = importance_df[importance_df['impact_when_removed'] > 0.01]

        if len(features_to_keep) > 0:
            report.append(f"\n✅ RECOMMENDED FEATURES TO KEEP ({len(features_to_keep)}):")
            for idx, row in features_to_keep.iterrows():
                impact_pct = row['impact_when_removed'] * 100
                report.append(f"   • {row['feature']}: {impact_pct:+.2f}% impact - {row['interpretation']}")

        if len(features_to_remove) > 0:
            report.append(f"\n❌ RECOMMENDED FEATURES TO REMOVE ({len(features_to_remove)}):")
            for idx, row in features_to_remove.iterrows():
                impact_pct = row['impact_when_removed'] * 100
                report.append(f"   • {row['feature']}: {impact_pct:+.2f}% impact - {row['interpretation']}")

        # Neutral features
        if len(neutral_features) > 0:
            report.append(f"\n🟢 NEUTRAL FEATURES (Optional - {len(neutral_features)}):")
            for idx, row in neutral_features.iterrows():
                impact_pct = row['impact_when_removed'] * 100
                report.append(f"   • {row['feature']}: {impact_pct:+.2f}% impact - {row['interpretation']}")

        # Summary statistics
        report.append(f"\n📈 BASELINE MODEL PERFORMANCE:")
        report.append(f"   • Baseline Accuracy: {baseline_acc:.4f} ({baseline_acc*100:.2f}%)")
        report.append(f"   • Total Features: {len(importance_df)}")

        # Feature categories
        critical_features = importance_df[importance_df['impact_when_removed'] <= -0.05]
        beneficial_features = importance_df[importance_df['impact_when_removed'] > 0.01]
        neutral_features = importance_df[
            (importance_df['impact_when_removed'] > -0.05) &
            (importance_df['impact_when_removed'] <= 0.01)
        ]

        report.append(f"\n🎯 FEATURE CATEGORIES:")
        report.append(f"   • 🔴 Critical Features (hurt when removed): {len(critical_features)}")
        report.append(f"   • ⚠️ Noise Features (help when removed): {len(beneficial_features)}")
        report.append(f"   • 🟢 Neutral Features: {len(neutral_features)}")

        # Top 5 most important features
        report.append(f"\n🏆 TOP 5 MOST IMPORTANT FEATURES:")
        top_features = importance_df.head(5)
        for idx, row in top_features.iterrows():
            impact_pct = row['impact_when_removed'] * 100
            report.append(f"   {row['rank']}. {row['feature']}")
            report.append(f"      • Impact when removed: {impact_pct:+.2f}%")
            report.append(f"      • Accuracy without: {row['accuracy_without_feature']:.4f}")
            report.append(f"      • Status: {row['interpretation']}")

        # Bottom 5 features (potential noise)
        if len(importance_df) > 5:
            report.append(f"\n⚠️ POTENTIAL NOISE FEATURES (Bottom 5):")
            bottom_features = importance_df.tail(5)
            for idx, row in bottom_features.iterrows():
                impact_pct = row['impact_when_removed'] * 100
                report.append(f"   {row['rank']}. {row['feature']}")
                report.append(f"      • Impact when removed: {impact_pct:+.2f}%")
                report.append(f"      • Accuracy without: {row['accuracy_without_feature']:.4f}")
                report.append(f"      • Status: {row['interpretation']}")

        # Recommendations
        report.append(f"\n💡 RECOMMENDATIONS:")
        if len(beneficial_features) > 0:
            report.append(f"   • Consider removing {len(beneficial_features)} noise features:")
            for _, row in beneficial_features.iterrows():
                impact_pct = row['impact_when_removed'] * 100
                report.append(f"     - {row['feature']} (improves by {impact_pct:+.2f}%)")

        if len(critical_features) > 0:
            report.append(f"   • Keep {len(critical_features)} critical features:")
            for _, row in critical_features.head(3).iterrows():  # Show top 3
                impact_pct = abs(row['impact_when_removed']) * 100
                report.append(f"     - {row['feature']} (drops {impact_pct:.2f}% when removed)")

        # Optimal Feature Set
        optimal_features = self.get_optimal_feature_set(importance_df)
        report.append(f"\n🎯 OPTIMAL FEATURE SET ({len(optimal_features)} features):")
        for i, feature in enumerate(sorted(optimal_features), 1):
            feature_row = importance_df[importance_df['feature'] == feature].iloc[0]
            impact_pct = feature_row['impact_when_removed'] * 100
            report.append(f"   {i:2d}. {feature} ({impact_pct:+.2f}%)")

        # Features Excluded from Optimal Set
        excluded_features = set(self.features) - set(optimal_features)
        if excluded_features:
            report.append(f"\n🚫 FEATURES EXCLUDED FROM OPTIMAL SET ({len(excluded_features)}):")
            for feature in sorted(excluded_features):
                feature_row = importance_df[importance_df['feature'] == feature].iloc[0]
                impact_pct = feature_row['impact_when_removed'] * 100
                reason = "Noise" if impact_pct > 1 else "Low Impact"
                report.append(f"   • {feature} ({impact_pct:+.2f}%) - {reason}")

        # Performance improvement potential
        max_improvement = beneficial_features['impact_when_removed'].sum() if len(beneficial_features) > 0 else 0
        if max_improvement > 0:
            potential_acc = baseline_acc + max_improvement
            report.append(f"\n🚀 IMPROVEMENT POTENTIAL:")
            report.append(f"   • Current Accuracy: {baseline_acc:.4f} ({baseline_acc*100:.2f}%)")
            report.append(f"   • Potential Accuracy: {potential_acc:.4f} ({potential_acc*100:.2f}%)")
            report.append(f"   • Potential Improvement: +{max_improvement*100:.2f}%")

        # Feature Selection Summary
        report.append(f"\n📊 FEATURE SELECTION SUMMARY:")
        report.append(f"   • Original Features: {len(self.features)}")
        report.append(f"   • Optimal Features: {len(optimal_features)}")
        report.append(f"   • Features Removed: {len(excluded_features)}")
        report.append(f"   • Reduction: {len(excluded_features)/len(self.features)*100:.1f}%")

        # Implementation Guide
        report.append(f"\n💻 IMPLEMENTATION GUIDE:")
        report.append(f"   1. Remove {len(features_to_remove)} noise features for immediate improvement")
        report.append(f"   2. Use optimal feature set with {len(optimal_features)} features")
        report.append(f"   3. Expected accuracy improvement: +{max_improvement*100:.2f}%")
        report.append(f"   4. Model complexity reduction: {len(excluded_features)/len(self.features)*100:.1f}%")

        report.append("="*80)

        return "\n".join(report)

    def generate_feature_lists_code(self, importance_df: pd.DataFrame) -> str:
        """
        Generate Python code for feature lists that can be used directly.

        Args:
            importance_df: Feature importance analysis results

        Returns:
            Python code string for feature lists
        """
        # Get optimal features
        optimal_features = self.get_optimal_feature_set(importance_df)

        # Get features to remove (noise)
        features_to_remove = importance_df[importance_df['impact_when_removed'] > 0.01]['feature'].tolist()

        # Get features to keep (important)
        features_to_keep = importance_df[importance_df['impact_when_removed'] <= -0.02]['feature'].tolist()

        code = []
        code.append("# " + "="*60)
        code.append("# FEATURE LISTS GENERATED FROM ABLATION STUDY")
        code.append("# " + "="*60)
        code.append("")

        # All features
        code.append("# All features used in analysis")
        code.append("ALL_FEATURES = [")
        for feature in sorted(self.features):
            code.append(f"    '{feature}',")
        code.append("]")
        code.append("")

        # Optimal features
        code.append("# Optimal feature set (recommended for production)")
        code.append("OPTIMAL_FEATURES = [")
        for feature in sorted(optimal_features):
            code.append(f"    '{feature}',")
        code.append("]")
        code.append("")

        # Features to keep
        if features_to_keep:
            code.append("# Important features (high impact when removed)")
            code.append("IMPORTANT_FEATURES = [")
            for feature in sorted(features_to_keep):
                code.append(f"    '{feature}',")
            code.append("]")
            code.append("")

        # Features to remove
        if features_to_remove:
            code.append("# Noise features (improve model when removed)")
            code.append("NOISE_FEATURES = [")
            for feature in sorted(features_to_remove):
                code.append(f"    '{feature}',")
            code.append("]")
            code.append("")

        # Usage example
        code.append("# Usage example:")
        code.append("# X_optimal = X[OPTIMAL_FEATURES]")
        code.append("# X_no_noise = X.drop(columns=NOISE_FEATURES)")
        code.append("")

        return "\n".join(code)

    def generate_progressive_feature_selection_report(self, importance_df: pd.DataFrame, baseline_result: Dict) -> str:
        """
        Generate progressive feature selection report showing step-by-step accuracy changes.

        Args:
            importance_df: Feature importance analysis results
            baseline_result: Baseline evaluation result

        Returns:
            Progressive selection report string
        """
        baseline_acc = baseline_result['accuracy_mean']

        # Sort features by impact (worst to best)
        sorted_features = importance_df.sort_values('impact_when_removed', ascending=False)

        report = []
        report.append("="*80)
        report.append("🎯 PROGRESSIVE FEATURE SELECTION REPORT")
        report.append("="*80)

        report.append(f"\n📋 STRATEGY:")
        report.append(f"   • Start with all {len(self.features)} features (Baseline: {baseline_acc:.4f})")
        report.append(f"   • Remove features progressively from worst to best")
        report.append(f"   • Stop when accuracy starts decreasing significantly")
        report.append(f"   • Find optimal balance between accuracy and complexity")

        # Progressive removal simulation
        current_features = self.features.copy()
        current_accuracy = baseline_acc
        removal_steps = []

        report.append(f"\n🔄 PROGRESSIVE REMOVAL SIMULATION:")
        report.append(f"{'Step':<4} {'Action':<25} {'Features':<8} {'Accuracy':<10} {'Change':<8} {'Status'}")
        report.append("-" * 80)

        # Step 0: Baseline
        baseline_acc_str = f"{current_accuracy:.4f}"
        report.append(f"{'0':<4} {'Baseline (All Features)':<25} {len(current_features):<8} {baseline_acc_str:<10} {'+0.00%':<8} {'✅ Start'}")

        step = 1
        best_accuracy = current_accuracy
        best_step = 0
        best_features = current_features.copy()

        for idx, row in sorted_features.iterrows():
            feature_to_remove = row['feature']
            impact = row['impact_when_removed']
            new_accuracy = current_accuracy + impact

            # Determine if we should remove this feature
            if impact > 0:  # Removing improves accuracy
                action = f"Remove {feature_to_remove}"
                status = "🟢 Improve"
                current_features = [f for f in current_features if f != feature_to_remove]
                current_accuracy = new_accuracy

                if new_accuracy > best_accuracy:
                    best_accuracy = new_accuracy
                    best_step = step
                    best_features = current_features.copy()

            elif impact > -0.02:  # Small negative impact (acceptable)
                action = f"Remove {feature_to_remove}"
                status = "🟡 Accept"
                current_features = [f for f in current_features if f != feature_to_remove]
                current_accuracy = new_accuracy

            else:  # Significant negative impact (keep feature)
                action = f"Keep {feature_to_remove}"
                status = "🔴 Keep"
                new_accuracy = current_accuracy  # No change

            change_pct = impact * 100
            change_str = f"{change_pct:+.2f}%"
            accuracy_str = f"{new_accuracy:.4f}"

            report.append(f"{step:<4} {action:<25} {len(current_features):<8} {accuracy_str:<10} {change_str:<8} {status}")

            removal_steps.append({
                'step': step,
                'feature': feature_to_remove,
                'action': 'remove' if 'Remove' in action else 'keep',
                'features_count': len(current_features),
                'accuracy': new_accuracy,
                'impact': impact,
                'status': status
            })

            step += 1

        # Best configuration summary
        report.append(f"\n🏆 OPTIMAL CONFIGURATION FOUND:")
        report.append(f"   • Best Step: {best_step}")
        report.append(f"   • Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
        report.append(f"   • Features Used: {len(best_features)}")
        report.append(f"   • Features Removed: {len(self.features) - len(best_features)}")
        report.append(f"   • Improvement: {(best_accuracy - baseline_acc)*100:+.2f}%")
        report.append(f"   • Complexity Reduction: {(len(self.features) - len(best_features))/len(self.features)*100:.1f}%")

        # Recommended feature set
        report.append(f"\n✅ RECOMMENDED FEATURE SET ({len(best_features)} features):")
        for i, feature in enumerate(sorted(best_features), 1):
            feature_row = importance_df[importance_df['feature'] == feature].iloc[0]
            impact_pct = feature_row['impact_when_removed'] * 100
            report.append(f"   {i:2d}. {feature} (impact: {impact_pct:+.2f}%)")

        # Features to remove
        removed_features = [f for f in self.features if f not in best_features]
        if removed_features:
            report.append(f"\n❌ FEATURES TO REMOVE ({len(removed_features)} features):")
            for feature in removed_features:
                feature_row = importance_df[importance_df['feature'] == feature].iloc[0]
                impact_pct = feature_row['impact_when_removed'] * 100
                reason = "Noise" if impact_pct > 1 else "Low Impact" if impact_pct > -1 else "Neutral"
                report.append(f"   • {feature} (impact: {impact_pct:+.2f}%) - {reason}")

        # Implementation scenarios
        report.append(f"\n🎯 IMPLEMENTATION SCENARIOS:")

        # Scenario 1: Conservative (remove only clear noise)
        noise_features = sorted_features[sorted_features['impact_when_removed'] > 0.01]
        if len(noise_features) > 0:
            conservative_acc = baseline_acc + noise_features['impact_when_removed'].sum()
            conservative_features = len(self.features) - len(noise_features)
            report.append(f"   1. 🛡️ CONSERVATIVE (Remove only clear noise):")
            report.append(f"      • Features: {conservative_features}/{len(self.features)}")
            report.append(f"      • Accuracy: {conservative_acc:.4f} ({conservative_acc*100:.2f}%)")
            report.append(f"      • Improvement: {(conservative_acc - baseline_acc)*100:+.2f}%")
            report.append(f"      • Remove: {', '.join(noise_features['feature'].tolist())}")

        # Scenario 2: Balanced (recommended)
        report.append(f"   2. ⚖️ BALANCED (Recommended):")
        report.append(f"      • Features: {len(best_features)}/{len(self.features)}")
        report.append(f"      • Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
        report.append(f"      • Improvement: {(best_accuracy - baseline_acc)*100:+.2f}%")
        report.append(f"      • Complexity Reduction: {(len(self.features) - len(best_features))/len(self.features)*100:.1f}%")

        # Scenario 3: Aggressive (keep only very important)
        important_features = sorted_features[sorted_features['impact_when_removed'] <= -0.05]
        if len(important_features) > 0:
            aggressive_features = len(important_features)
            # Estimate accuracy (conservative estimate)
            aggressive_acc = baseline_acc - 0.02  # Small penalty for removing many features
            report.append(f"   3. ⚡ AGGRESSIVE (Keep only critical features):")
            report.append(f"      • Features: {aggressive_features}/{len(self.features)}")
            report.append(f"      • Accuracy: ~{aggressive_acc:.4f} ({aggressive_acc*100:.2f}%) [Estimated]")
            report.append(f"      • Complexity Reduction: {(len(self.features) - aggressive_features)/len(self.features)*100:.1f}%")
            report.append(f"      • Keep only: {', '.join(important_features['feature'].tolist())}")

        report.append("="*80)

        return "\n".join(report)

    def _interpret_impact(self, impact: float) -> str:
        """Interpret the impact of feature removal."""
        if impact <= -0.5:
            return "🔴 Sangat Penting"
        elif impact <= -0.2:
            return "🟡 Penting"
        elif impact <= 0.1:
            return "🟢 Netral"
        elif impact <= 0.5:
            return "⚠️ Sedikit Noise"
        elif impact <= 1.0:
            return "❌ Noise"
        else:
            return "❌ Sangat Mengganggu"
    
    def get_optimal_feature_set(self, importance_df: pd.DataFrame) -> List[str]:
        """
        Recommend optimal feature set based on importance analysis.
        
        Args:
            importance_df: Feature importance analysis results
            
        Returns:
            List of recommended features
        """
        # Remove features that hurt performance (positive impact when removed)
        features_to_keep = importance_df[importance_df['impact_when_removed'] <= 0]['feature'].tolist()
        
        # If removing features helps, include those with small positive impact
        if len(features_to_keep) < len(self.features) * 0.5:  # Keep at least 50% of features
            threshold = 0.5  # Allow small positive impact
            features_to_keep = importance_df[importance_df['impact_when_removed'] <= threshold]['feature'].tolist()
        
        logger.info(f"Recommended {len(features_to_keep)} features out of {len(self.features)}")
        
        return features_to_keep
    
    def run_complete_ablation_study(self) -> Tuple[pd.DataFrame, Dict]:
        """
        Run complete ablation study workflow.
        
        Returns:
            Tuple of (importance_analysis_df, optimal_evaluation_result)
        """
        logger.info("Starting complete ablation study")
        
        # Step 1: Baseline evaluation
        baseline_result = self.run_baseline_evaluation()
        self.results.append(baseline_result)
        
        # Step 2: Single-feature elimination
        elimination_results = self.run_single_feature_elimination()
        self.results.extend(elimination_results)
        
        # Step 3: Feature importance analysis
        importance_df = self.analyze_feature_importance(baseline_result, elimination_results)

        # Step 4: Generate and display feature impact report
        impact_report = self.generate_feature_impact_report(importance_df, baseline_result)
        print(impact_report)

        # Step 4.5: Generate and display progressive feature selection report (temporarily disabled due to format error)
        # progressive_report = self.generate_progressive_feature_selection_report(importance_df, baseline_result)
        # print(progressive_report)

        # Step 5: Evaluate optimal feature set
        optimal_features = self.get_optimal_feature_set(importance_df)
        optimal_result = self.evaluate_feature_set(optimal_features, "Optimal_Feature_Set")
        self.results.append(optimal_result)

        logger.info("Ablation study completed")

        return importance_df, optimal_result
    
    def save_results(self, importance_df: pd.DataFrame, 
                    optimal_result: Dict) -> Tuple[str, str]:
        """
        Save ablation study results.
        
        Args:
            importance_df: Feature importance analysis
            optimal_result: Optimal feature set evaluation
            
        Returns:
            Tuple of (results_file_path, importance_file_path)
        """
        # Create results directory
        results_dir = Path("results/clean_ablation_study")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save all results
        results_file = results_dir / f"ablation_results_{timestamp}.csv"
        results_df = pd.DataFrame(self.results)
        results_df.to_csv(results_file, index=False)
        
        # Save importance analysis
        importance_file = results_dir / f"feature_importance_{timestamp}.csv"
        importance_df.to_csv(importance_file, index=False)

        # Save feature impact report
        report_file = results_dir / f"feature_impact_report_{timestamp}.txt"
        baseline_result = next((r for r in self.results if r['experiment_name'] == 'Baseline_All_Features'), {})
        if baseline_result:
            impact_report = self.generate_feature_impact_report(importance_df, baseline_result)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(impact_report)

        # Save feature lists code
        code_file = results_dir / f"feature_lists_code_{timestamp}.py"
        feature_code = self.generate_feature_lists_code(importance_df)
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(feature_code)

        # Save progressive feature selection report (temporarily disabled due to format error)
        # progressive_file = results_dir / f"progressive_selection_report_{timestamp}.txt"
        # if baseline_result:
        #     progressive_report = self.generate_progressive_feature_selection_report(importance_df, baseline_result)
        #     with open(progressive_file, 'w', encoding='utf-8') as f:
        #         f.write(progressive_report)

        logger.info(f"Results saved to {results_file}")
        logger.info(f"Importance analysis saved to {importance_file}")
        logger.info(f"Feature impact report saved to {report_file}")
        # logger.info(f"Progressive selection report saved to {progressive_file}")
        logger.info(f"Feature lists code saved to {code_file}")

        return str(results_file), str(importance_file)


def main():
    """Main function to run ablation study."""

    print("🔬 Clean Ablation Study - Feature Elimination Analysis")
    print("=" * 60)
    print("📋 Using SAFE dataset (after feature filtering to prevent data leakage)")
    print("=" * 60)

    # Check which safe datasets are available
    safe_datasets = [
        {
            'path': 'dataset/processed/safe_ml_fatigue_dataset.csv',
            'target': 'fatigue_risk',
            'name': 'Regular Fatigue Classification',
            'priority': 1
        },
        {
            'path': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
            'target': 'corrected_fatigue_risk',
            'name': 'Bias-Corrected Fatigue Classification',
            'priority': 2
        },
        {
            'path': 'dataset/processed/safe_ml_title_only_dataset.csv',
            'target': 'title_fatigue_risk',
            'name': 'Title-Only Fatigue Classification',
            'priority': 3
        }
    ]

    # Find the first available safe dataset
    selected_dataset = None
    for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
        if Path(dataset['path']).exists():
            selected_dataset = dataset
            break

    if not selected_dataset:
        print("❌ No safe dataset found!")
        print("Please run one of the main pipelines first:")
        print("  • python main_complete.py")
        print("  • python main_complete_bias_corrected.py")
        print("  • python main_title_only_pipeline.py")
        return

    print(f"📁 Dataset: {selected_dataset['path']}")
    print(f"🎯 Target: {selected_dataset['target']}")
    print(f"📋 Type: {selected_dataset['name']}")
    print("=" * 60)

    # Initialize ablation study with selected SAFE dataset
    study = AblationStudy(
        data_path=selected_dataset['path'],
        target_column=selected_dataset['target'],
        random_state=42
    )
    
    # Load data
    study.load_data()
    
    # Run complete ablation study
    importance_df, optimal_result = study.run_complete_ablation_study()
    
    # Save results
    results_file, importance_file = study.save_results(importance_df, optimal_result)
    
    # Display summary
    print("\n🎉 Ablation Study Completed!")
    print("=" * 60)
    
    print(f"\n📊 FEATURE IMPORTANCE RANKING:")
    for _, row in importance_df.head().iterrows():
        print(f"  {row['rank']}. {row['feature']}: "
              f"{row['impact_when_removed']:+.2f}% - {row['interpretation']}")
    
    print(f"\n📈 PERFORMANCE SUMMARY:")
    baseline = [r for r in study.results if r['experiment_name'] == 'Baseline_All_Features'][0]
    print(f"  • Baseline (All Features): {baseline['accuracy_mean']:.4f} ± {baseline['accuracy_std']:.4f}")
    print(f"  • Optimal Feature Set: {optimal_result['accuracy_mean']:.4f} ± {optimal_result['accuracy_std']:.4f}")
    
    improvement = optimal_result['accuracy_mean'] - baseline['accuracy_mean']
    print(f"  • Improvement: {improvement:+.4f} ({improvement/baseline['accuracy_mean']*100:+.1f}%)")
    
    print(f"\n📁 FILES SAVED:")
    print(f"  • Results: {results_file}")
    print(f"  • Importance: {importance_file}")
    
    return study, importance_df, optimal_result


if __name__ == "__main__":
    main()
