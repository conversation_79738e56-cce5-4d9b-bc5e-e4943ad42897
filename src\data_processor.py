"""
Data Processing Module
Clean code implementation for processing raw activity and productivity data
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Tuple
import logging

from constants import DataConstants

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Clean data processing class following SOLID principles
    """
    
    def __init__(self, raw_data_path: str = DataConstants.DEFAULT_RAW_PATH):
        """
        Initialize data processor

        Args:
            raw_data_path: Path to raw data directory
        """
        self.raw_data_path = Path(raw_data_path)
        self.processed_data_path = Path(DataConstants.DEFAULT_PROCESSED_PATH)
        self.processed_data_path.mkdir(parents=True, exist_ok=True)
        
    def load_raw_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load raw datasets from original files
        
        Returns:
            Tuple of (strava_data, pomokit_data)
        """
        try:
            # Load original datasets
            strava_path = self.raw_data_path / "strava.csv"
            pomokit_path = self.raw_data_path / "pomokit.csv"
            
            logger.info(f"Loading Strava data from {strava_path}")
            strava_data = pd.read_csv(strava_path)
            
            logger.info(f"Loading Pomokit data from {pomokit_path}")
            pomokit_data = pd.read_csv(pomokit_path)
            
            logger.info(f"Loaded {len(strava_data)} Strava records and {len(pomokit_data)} Pomokit records")
            
            return strava_data, pomokit_data
            
        except FileNotFoundError as e:
            logger.error(f"Data file not found: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def clean_strava_data(self, strava_data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize Strava activity data

        Args:
            strava_data: Raw Strava DataFrame

        Returns:
            Cleaned Strava DataFrame
        """
        logger.info("Cleaning Strava data...")

        # Create copy to avoid modifying original
        df = strava_data.copy()

        # Standardize column names
        df.columns = df.columns.str.lower().str.replace(' ', '_')

        # Convert date columns
        if 'date_time_wib' in df.columns:
            df['date'] = pd.to_datetime(df['date_time_wib'])
        elif 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif 'activity_date' in df.columns:
            df['date'] = pd.to_datetime(df['activity_date'])

        # Clean title column
        if 'title' in df.columns:
            df['title'] = df['title'].astype(str).str.strip()
            df['title'] = df['title'].replace('nan', '')
            logger.info("Cleaned title column for Strava data")

        # Clean numeric columns - adjust for actual Strava columns
        numeric_columns = ['distance', 'moving_time']
        for col in numeric_columns:
            if col in df.columns:
                # Handle distance with 'km' suffix
                if col == 'distance' and df[col].dtype == 'object':
                    df[col] = df[col].str.replace(' km', '').str.replace(',', '.')
                # Handle time format
                elif col == 'moving_time' and df[col].dtype == 'object':
                    # Convert time format like "41m 21s" to minutes
                    df[col] = self._convert_time_to_minutes(df[col])
                    continue

                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove invalid records
        initial_count = len(df)
        df = df.dropna(subset=['date'])
        df = df[df['distance'] > 0] if 'distance' in df.columns else df

        logger.info(f"Cleaned Strava data: {initial_count} → {len(df)} records")

        return df
    
    def clean_pomokit_data(self, pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and standardize Pomokit productivity data

        Args:
            pomokit_data: Raw Pomokit DataFrame

        Returns:
            Cleaned Pomokit DataFrame
        """
        logger.info("Cleaning Pomokit data...")

        # Create copy to avoid modifying original
        df = pomokit_data.copy()

        # Standardize column names
        df.columns = df.columns.str.lower().str.replace(' ', '_')

        # Convert date columns
        if 'date_time_wib' in df.columns:
            df['date'] = pd.to_datetime(df['date_time_wib'])
        elif 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif 'session_date' in df.columns:
            df['date'] = pd.to_datetime(df['session_date'])

        # Clean title column
        if 'title' in df.columns:
            df['title'] = df['title'].astype(str).str.strip()
            df['title'] = df['title'].replace('nan', '')
            logger.info("Cleaned title column for Pomokit data")

        # Clean numeric columns - adjust for actual Pomokit columns
        numeric_columns = ['cycle']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove invalid records
        initial_count = len(df)
        df = df.dropna(subset=['date'])

        logger.info(f"Cleaned Pomokit data: {initial_count} → {len(df)} records")

        return df
    
    def create_weekly_aggregation(self, strava_data: pd.DataFrame, 
                                pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """
        Create weekly aggregated dataset from daily data
        
        Args:
            strava_data: Cleaned Strava data
            pomokit_data: Cleaned Pomokit data
            
        Returns:
            Weekly aggregated DataFrame
        """
        logger.info("Creating weekly aggregation...")
        
        # Aggregate Strava data by week
        strava_weekly = self._aggregate_strava_weekly(strava_data)
        
        # Aggregate Pomokit data by week
        pomokit_weekly = self._aggregate_pomokit_weekly(pomokit_data)
        
        # Merge weekly data
        weekly_data = self._merge_weekly_data(strava_weekly, pomokit_weekly)
        
        logger.info(f"Created weekly dataset with {len(weekly_data)} observations")
        
        return weekly_data

    def _convert_time_to_minutes(self, time_series: pd.Series) -> pd.Series:
        """Convert time format like '41m 21s' to total minutes"""
        def parse_time(time_str):
            if pd.isna(time_str):
                return 0
            try:
                time_str = str(time_str).lower()
                minutes = 0
                if 'h' in time_str:
                    hours = int(time_str.split('h')[0])
                    minutes += hours * 60
                    time_str = time_str.split('h')[1] if 'h' in time_str else time_str
                if 'm' in time_str:
                    mins = int(time_str.split('m')[0])
                    minutes += mins
                if 's' in time_str and 'm' in time_str:
                    # Handle seconds if present
                    pass
                return minutes
            except:
                return 0

        return time_series.apply(parse_time)

    def _aggregate_strava_weekly(self, strava_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate Strava data by week"""

        # Group by phonenumber and year_week
        grouping_cols = ['phonenumber', 'year_week']

        # Use actual column names from Strava data
        agg_dict = {}
        if 'distance' in strava_data.columns:
            agg_dict['distance'] = ['sum', 'mean', 'count']
        if 'moving_time' in strava_data.columns:
            agg_dict['moving_time'] = ['sum', 'mean']
        if 'title' in strava_data.columns:
            agg_dict['title'] = lambda x: ' | '.join(x.dropna().astype(str))

        weekly_strava = strava_data.groupby(grouping_cols).agg(agg_dict).reset_index()

        # Flatten column names
        weekly_strava.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0]
                               for col in weekly_strava.columns]

        # Rename columns for clarity
        rename_dict = {
            'distance_sum': 'total_distance_km',
            'distance_mean': 'avg_distance_km',
            'distance_count': 'activity_days',
            'moving_time_sum': 'total_time_minutes',
            'moving_time_mean': 'avg_time_minutes',
            'title_<lambda>': 'strava_activities_titles'
        }

        weekly_strava = weekly_strava.rename(columns=rename_dict)

        return weekly_strava
    
    def _aggregate_pomokit_weekly(self, pomokit_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate Pomokit data by week"""

        grouping_cols = ['phonenumber', 'year_week']

        # Use actual column names from Pomokit data
        agg_dict = {}
        if 'cycle' in pomokit_data.columns:
            agg_dict['cycle'] = ['sum', 'mean', 'count']
        if 'title' in pomokit_data.columns:
            agg_dict['title'] = lambda x: ' | '.join(x.dropna().astype(str))

        weekly_pomokit = pomokit_data.groupby(grouping_cols).agg(agg_dict).reset_index()

        # Flatten column names
        weekly_pomokit.columns = [f"{col[0]}_{col[1]}" if col[1] else col[0]
                                for col in weekly_pomokit.columns]

        # Rename columns for clarity
        rename_dict = {
            'cycle_sum': 'total_cycles',
            'cycle_mean': 'avg_cycles',
            'cycle_count': 'work_days',
            'title_<lambda>': 'pomokit_activities_titles'
        }

        weekly_pomokit = weekly_pomokit.rename(columns=rename_dict)

        return weekly_pomokit

    def add_title_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add advanced title-based features to the dataset

        Args:
            data: Weekly aggregated data with title columns

        Returns:
            Data with additional title-based features
        """
        logger.info("Adding title-based features...")

        df = data.copy()

        # Process Strava titles
        if 'strava_activities_titles' in df.columns:
            df['strava_title_count'] = df['strava_activities_titles'].apply(
                lambda x: len(str(x).split(' | ')) if pd.notna(x) and str(x) != '' else 0
            )
            df['strava_title_length'] = df['strava_activities_titles'].apply(
                lambda x: len(str(x)) if pd.notna(x) else 0
            )
            df['strava_unique_words'] = df['strava_activities_titles'].apply(
                lambda x: len(set(str(x).lower().split())) if pd.notna(x) and str(x) != '' else 0
            )

        # Process Pomokit titles
        if 'pomokit_activities_titles' in df.columns:
            df['pomokit_title_count'] = df['pomokit_activities_titles'].apply(
                lambda x: len(str(x).split(' | ')) if pd.notna(x) and str(x) != '' else 0
            )
            df['pomokit_title_length'] = df['pomokit_activities_titles'].apply(
                lambda x: len(str(x)) if pd.notna(x) else 0
            )
            df['pomokit_unique_words'] = df['pomokit_activities_titles'].apply(
                lambda x: len(set(str(x).lower().split())) if pd.notna(x) and str(x) != '' else 0
            )

        # Combined title features
        if 'strava_activities_titles' in df.columns and 'pomokit_activities_titles' in df.columns:
            df['combined_titles'] = df['strava_activities_titles'].astype(str) + ' | ' + df['pomokit_activities_titles'].astype(str)
            df['total_title_diversity'] = df['strava_unique_words'] + df['pomokit_unique_words']
            df['title_balance_ratio'] = np.where(
                df['pomokit_title_length'] == 0,
                np.inf,
                df['strava_title_length'] / df['pomokit_title_length']
            )

        logger.info("Title-based features added successfully")

        return df



    def _merge_weekly_data(self, strava_weekly: pd.DataFrame,
                          pomokit_weekly: pd.DataFrame) -> pd.DataFrame:
        """Merge weekly Strava and Pomokit data"""
        
        # Merge on phonenumber and year_week
        merged_data = pd.merge(
            strava_weekly,
            pomokit_weekly,
            on=['phonenumber', 'year_week'],
            how='inner'
        )
        
        # Calculate additional metrics
        merged_data = self._calculate_derived_metrics(merged_data)
        
        return merged_data
    
    def _calculate_derived_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate derived metrics and consistency scores"""
        
        df = data.copy()
        
        # Calculate consistency score (original formula)
        # Simple average of activity days and work days
        physical_consistency = (df['activity_days'].clip(upper=2)) / 2
        work_consistency = (df['work_days'].clip(upper=5)) / 5

        df['consistency_score'] = (physical_consistency + work_consistency) / 2
        
        # Calculate efficiency metrics
        df['weekly_efficiency'] = np.where(
            df['work_days'] == 0,
            np.nan,  # atau 0 jika ingin aman
            df['total_cycles'] / df['work_days']
        )
        
        return df
    
    def add_gamification_variables(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add gamification variables to the dataset
        
        Args:
            data: Weekly aggregated data
            
        Returns:
            Data with gamification variables added
        """
        logger.info("Adding gamification variables...")
        
        df = data.copy()
        
        # Activity points (max points for weekly distance target)
        df['activity_points'] = np.minimum(
            (df['total_distance_km'] / DataConstants.WEEKLY_DISTANCE_TARGET) * DataConstants.MAX_ACTIVITY_POINTS,
            DataConstants.MAX_ACTIVITY_POINTS
        )

        # Productivity points (max points for weekly cycles target)
        df['productivity_points'] = np.minimum(
            (df['total_cycles'] / DataConstants.WEEKLY_CYCLES_TARGET) * DataConstants.MAX_PRODUCTIVITY_POINTS,
            DataConstants.MAX_PRODUCTIVITY_POINTS
        )

        # Achievement rate (percentage of maximum possible points)
        # Calculate directly from activity and productivity points
        total_points = df['activity_points'] + df['productivity_points']
        df['achievement_rate'] = total_points / DataConstants.MAX_TOTAL_GAMIFICATION_POINTS
        
        # Gamification balance (how balanced between activity and productivity)
        df['gamification_balance'] = abs(df['activity_points'] - df['productivity_points'])
        
        logger.info("Gamification variables added successfully")
        
        return df

    def generate_user_identities(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate anonymized user identities based on unique phone numbers

        Args:
            data: DataFrame with phonenumber column

        Returns:
            DataFrame with added identity column
        """
        logger.info("Generating anonymized user identities...")

        df = data.copy()

        # Get unique phone numbers and create mapping
        unique_phones = sorted(df['phonenumber'].unique())
        phone_to_identity = {phone: f"user-{i+1}" for i, phone in enumerate(unique_phones)}

        # Add identity column
        df['identity'] = df['phonenumber'].map(phone_to_identity)

        logger.info(f"Generated identities for {len(unique_phones)} unique users")

        return df

    def finalize_dataset(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Finalize dataset by removing phonenumber and reordering columns

        Args:
            data: DataFrame with phonenumber and identity columns

        Returns:
            DataFrame with identity as first column and phonenumber removed
        """
        logger.info("Finalizing dataset: removing phonenumber and reordering columns...")

        df = data.copy()

        # Remove phonenumber column
        if 'phonenumber' in df.columns:
            df = df.drop('phonenumber', axis=1)
            logger.info("Removed phonenumber column")

        # Reorder columns to put identity first
        if 'identity' in df.columns:
            cols = df.columns.tolist()
            cols.remove('identity')
            cols.insert(0, 'identity')
            df = df[cols]
            logger.info("Moved identity column to first position")

        logger.info(f"Final dataset shape: {df.shape}")

        return df

    def save_processed_data(self, data: pd.DataFrame, filename: str) -> None:
        """
        Save processed data to file
        
        Args:
            data: Processed DataFrame
            filename: Output filename
        """
        output_path = self.processed_data_path / filename
        data.to_csv(output_path, index=False)
        logger.info(f"Saved processed data to {output_path}")
    
    def process_all(self) -> pd.DataFrame:
        """
        Execute complete data processing pipeline

        Returns:
            Final processed dataset
        """
        logger.info("Starting complete data processing pipeline...")

        # Load raw data
        strava_data, pomokit_data = self.load_raw_data()

        # Clean data
        strava_clean = self.clean_strava_data(strava_data)
        pomokit_clean = self.clean_pomokit_data(pomokit_data)

        # Create weekly aggregation
        weekly_data = self.create_weekly_aggregation(strava_clean, pomokit_clean)

        # Add title-based features
        weekly_data = self.add_title_features(weekly_data)

        # Add gamification variables
        final_data = self.add_gamification_variables(weekly_data)

        # Generate anonymized user identities
        final_data = self.generate_user_identities(final_data)

        # Remove phonenumber column and reorder columns
        final_data = self.finalize_dataset(final_data)

        # Save processed data
        self.save_processed_data(final_data, DataConstants.OUTPUT_FILENAME)

        logger.info("Data processing pipeline completed successfully")

        return final_data


def main():
    """Main function for testing data processor"""
    processor = DataProcessor()
    final_dataset = processor.process_all()
    print(f"Final dataset shape: {final_dataset.shape}")
    print(f"Columns: {list(final_dataset.columns)}")


if __name__ == "__main__":
    main()
