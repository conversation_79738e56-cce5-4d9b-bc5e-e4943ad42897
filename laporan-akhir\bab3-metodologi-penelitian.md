# BAB III

# METODOLOGI PENELITIAN

## 3.1 <PERSON>ain <PERSON>

### 3.1.1 <PERSON><PERSON> dan <PERSON>n Penelitian

Penelitian ini menggunakan desain kuantitatif dengan pendekatan cross-sectional yang diperkuat dengan elemen longitudinal untuk menganalisis pola aktivitas mahasiswa dalam periode waktu tertentu [41], [42]. Pendekatan observational study diterapkan untuk mengumpulkan data behavioral dari aktivitas sehari-hari mahasiswa tanpa melakukan intervensi langsung terhadap subjek penelitian [42].

Metode penelitian yang digunakan adalah predictive research yang bertujuan untuk mengembangkan model klasifikasi risiko fatigue berdasarkan pola aktivitas kardiovaskular dan produktivitas akademik mahasiswa. Penelitian ini menggunakan pendekatan supervised machine learning untuk mengklasifikasikan mahasiswa ke dalam kategori risiko fatigue (low, medium, high) berdasarkan behavioral patterns yang terekam dalam data digital.

### 3.1.2 Paradigma Penelitian

Penelitian ini mengadopsi paradigma positivis dengan pendekatan empiris untuk menganalisis data kuantitatif yang diperoleh dari platform digital [45]. Paradigma positivis dipilih karena sesuai dengan tujuan penelitian yang ingin mengidentifikasi pola objektif dan hubungan kausal untuk klasifikasi risiko fatigue berdasarkan behavioral data mahasiswa [45].

Pendekatan mixed-methods juga diterapkan melalui integrasi analisis kuantitatif (data metrik aktivitas dan produktivitas) dengan analisis kualitatif (text-based feature extraction pada judul aktivitas) [43], [44]. Kombinasi ini memberikan perspektif yang lebih komprehensif dalam memahami fenomena fatigue pada mahasiswa dengan memanfaatkan kekuatan dari kedua pendekatan metodologi [43].

**Critical Finding:** Penelitian ini mengungkapkan pentingnya **data leakage prevention** dalam behavioral prediction, dimana target variable harus dibuat secara independen dari input features untuk menghindari circular dependency yang menyebabkan artificial performance.

![Alur Metodologi Penelitian](laporan-akhir/figures/gambar_3_1_alur_metodologi_penelitian.png)

**Gambar 3.1** Alur Metodologi Penelitian - 8 Tahapan Sistematis

### 3.1.3 Alur Metodologi Penelitian

Penelitian ini mengikuti alur metodologi yang sistematis dan terstruktur untuk memastikan validitas dan reliabilitas hasil. Alur metodologi terdiri dari 8 tahapan utama yang dilaksanakan secara berurutan:

**1. Data Collection (Pengumpulan Data)**

-   Pengumpulan data aktivitas kardiovaskular dari platform Strava
-   Pengumpulan data produktivitas akademik dari platform Pomokit
-   Implementasi protokol ethical clearance dan informed consent

**2. Data Preprocessing (Pra-pemrosesan Data)**

-   Data cleaning dan validation untuk menangani missing values dan outliers
-   Data integration dan harmonization dari kedua platform
-   Format standardization dan temporal alignment

**3. Feature Engineering (Rekayasa Fitur)**

-   Pembuatan derived variables dan aggregated metrics
-   Text-based feature extraction dari activity titles
-   Gamification variables dan consistency metrics

**4. Labeling Strategy (Strategi Pelabelan)**

-   Independent external labeling menggunakan temporal patterns
-   Expert simulation berdasarkan domain knowledge
-   Multiple labeling approaches untuk validation

**5. Feature Selection (Seleksi Fitur)**

-   RFE (Recursive Feature Elimination) analysis
-   SHAP-based feature importance evaluation
-   Data leakage prevention dan feature filtering

**6. Training Model (Pelatihan Model)**

-   Multiple algorithm implementation (Random Forest, SVM, Neural Networks)
-   Hyperparameter optimization dengan cross-validation
-   Pipeline comparison dan stability analysis

**7. Evaluation Model (Evaluasi Model)**

-   Comprehensive metrics evaluation (accuracy, F1-score, precision, recall)
-   Cross-validation dengan stratified k-fold
-   Statistical significance testing dan model comparison

**8. Results & Analysis (Hasil dan Analisis)**

-   Feature importance analysis dan interpretability
-   Business actionability assessment
-   Production deployment recommendations

**Ringkasan Alur Metodologi:**

Alur metodologi penelitian ini dirancang secara sistematis dan berurutan untuk memastikan validitas hasil dan mencegah data leakage. Setiap tahapan memiliki output spesifik yang menjadi input untuk tahapan selanjutnya, dengan emphasis khusus pada independent labeling dan feature selection untuk menghasilkan model yang robust dan dapat diimplementasikan dalam production environment.

**Key Innovation:** Penelitian ini menggunakan 8-stage sequential methodology dengan focus pada data leakage prevention dan SHAP-based interpretability untuk menghasilkan actionable insights dalam fatigue prediction berbasis behavioral data.

## 3.2 Populasi dan Sampel

### 3.2.1 Populasi Target

Populasi target penelitian adalah mahasiswa aktif di Indonesia yang menggunakan teknologi digital untuk monitoring aktivitas fisik dan produktivitas akademik. Populasi ini dipilih karena representatif terhadap generasi digital native yang familiar dengan penggunaan aplikasi mobile dan wearable technology untuk self-monitoring.

Karakteristik populasi target meliputi mahasiswa berusia 18-25 tahun, memiliki akses terhadap smartphone dan internet, aktif dalam kegiatan akademik, dan memiliki motivasi untuk monitoring aktivitas fisik dan produktivitas. Populasi ini mencerminkan segmen mahasiswa yang dapat memanfaatkan teknologi digital health monitoring dalam kehidupan sehari-hari.

### 3.2.2 Teknik Sampling

Teknik sampling yang digunakan adalah purposive sampling dengan kriteria inklusi yang spesifik untuk memastikan kualitas dan relevansi data. Kriteria inklusi meliputi: (1) mahasiswa aktif yang terdaftar di perguruan tinggi, (2) pengguna aktif aplikasi Strava untuk tracking aktivitas fisik, (3) pengguna aktif aplikasi Pomokit untuk monitoring produktivitas, (4) memiliki data aktivitas minimal dalam periode 4 minggu berturut-turut.

Kriteria eksklusi meliputi: (1) mahasiswa dengan kondisi kesehatan kronis yang dapat mempengaruhi aktivitas fisik, (2) pengguna yang tidak konsisten dalam penggunaan aplikasi, (3) data yang tidak lengkap atau mengandung anomali signifikan. Teknik sampling ini dipilih untuk memastikan homogenitas sampel dan validitas data yang dikumpulkan.

### 3.2.3 Ukuran Sampel

Penentuan ukuran sampel menggunakan pendekatan power analysis untuk machine learning studies dengan mempertimbangkan kompleksitas model dan jumlah features yang digunakan. Berdasarkan rule of thumb untuk supervised learning, minimal diperlukan 10-20 observasi per feature untuk memastikan generalizability model.

Dengan jumlah features sebanyak 18 variabel utama, ukuran sampel minimal yang diperlukan adalah 300 observasi. Dalam penelitian ini, data yang terkumpul mencakup 300 observasi mingguan dari mahasiswa yang memenuhi kriteria inklusi, yang memberikan statistical power yang adequate untuk analisis machine learning dan validasi hasil.

## 3.3 Variabel Penelitian

### 3.3.1 Variabel Independen

Variabel independen dalam penelitian ini terdiri dari dua kategori utama: variabel aktivitas kardiovaskular dan variabel produktivitas akademik. Variabel aktivitas kardiovaskular meliputi total distance (total_distance_km), average distance (avg_distance_km), activity days (activity_days), total time (total_time_minutes), average time (avg_time_minutes), dan activity points (activity_points).

Variabel produktivitas akademik mencakup total cycles (total_cycles), work days (work_days), consistency score (consistency_score), productivity points (productivity_points), achievement rate (achievement_rate), dan gamification balance (gamification_balance). Variabel tambahan meliputi title-based features yang diekstrak melalui text-based feature extraction pada judul aktivitas.

### 3.3.2 Variabel Dependen

**Target Variable: Fatigue Risk Classification**

Variabel dependen utama adalah fatigue risk classification yang dikategorikan menjadi tiga tingkat: low risk, medium risk, dan high risk. **CRITICAL ISSUE DISCOVERED:** Implementasi awal menggunakan composite scoring dari input features yang menyebabkan data leakage dengan artificial performance 96.67%.

**Multiple Labeling Approaches Implemented:**

1. **Regular Fatigue Classification (main4.py):**

    - Composite scoring dari input features
    - **RESULT:** Data leakage detected (96.67% artificial accuracy)
    - **STATUS:** ❌ Invalid untuk production

2. **Bias-Corrected Classification (main5.py):**

    - Adjusted composite scoring method
    - **RESULT:** 69-71% accuracy (more realistic)
    - **STATUS:** ✅ Valid but still potential leakage risk

3. **Title-Only Classification (main6.py, main7.py):**

    - Berdasarkan text analysis dari activity titles
    - **RESULT:** 65-68% accuracy dengan interpretability
    - **STATUS:** ✅ Valid dengan reduced feature set

4. **External Independent Labels (main_leak_free.py):**
    - **Temporal patterns analysis** (activity consistency over time)
    - **Expert simulation** berdasarkan domain knowledge
    - **Domain-specific rules** independent dari input features
    - **RESULT:** 60-61% accuracy (realistic untuk behavioral prediction)
    - **STATUS:** ✅ Leak-free dan valid untuk production

**Target Recreation Testing:**

-   **Method:** Attempt to recreate target using input features
-   **Threshold:** >70% recreation success indicates data leakage
-   **Result:** 80% success rate pada regular classification → **LEAKAGE CONFIRMED**
-   **Solution:** External independent labeling methodology

### 3.3.3 Variabel Kontrol

Variabel kontrol yang dipertimbangkan meliputi demographic factors (usia, jenis kelamin), academic factors (tingkat semester, program studi), dan temporal factors (minggu dalam semester, musim). Meskipun tidak semua variabel kontrol tersedia dalam dataset, analisis dilakukan dengan mempertimbangkan potential confounding factors.

Kontrol terhadap data quality dilakukan melalui preprocessing yang ketat, outlier detection, dan missing value handling. Temporal alignment juga dilakukan untuk memastikan konsistensi data dari kedua platform yang digunakan dalam penelitian.

## 3.4 Instrumen Penelitian

### 3.4.1 Platform Strava untuk Data Aktivitas Kardiovaskular

Platform Strava digunakan sebagai instrumen utama untuk mengumpulkan data aktivitas kardiovaskular mahasiswa. Strava merupakan aplikasi fitness tracking yang memiliki akurasi tinggi dalam recording aktivitas fisik menggunakan GPS dan sensor smartphone atau wearable devices. Data yang dikumpulkan meliputi jenis aktivitas, durasi, jarak, kecepatan, dan deskripsi aktivitas.

Validitas dan reliabilitas data Strava telah divalidasi dalam berbagai penelitian sebelumnya, menunjukkan korelasi yang tinggi dengan gold standard measurement tools [21]. Platform ini dipilih karena popularitasnya di kalangan mahasiswa, user interface yang user-friendly, dan API yang memungkinkan ekstraksi data untuk penelitian [40].

### 3.4.2 Platform Pomokit untuk Data Produktivitas

Platform Pomokit digunakan untuk mengumpulkan data produktivitas akademik mahasiswa melalui implementasi teknik Pomodoro. Aplikasi ini merekam jumlah siklus kerja yang diselesaikan, durasi fokus, konsistensi penggunaan, dan deskripsi aktivitas produktif yang dilakukan mahasiswa.

Pomokit menyediakan gamification elements seperti points, achievements, dan progress tracking yang memotivasi pengguna untuk konsisten dalam penggunaan. Data yang dikumpulkan mencakup quantitative metrics (jumlah siklus, durasi) dan qualitative data (deskripsi aktivitas) yang dapat dianalisis menggunakan text-based feature extraction.

### 3.4.3 Validitas dan Reliabilitas Instrumen

Validitas instrumen dievaluasi melalui content validity (kesesuaian dengan tujuan penelitian), construct validity (kemampuan mengukur konstruk yang dimaksud), dan criterion validity (korelasi dengan gold standard). Kedua platform yang digunakan telah menunjukkan validitas yang baik dalam penelitian-penelitian sebelumnya.

Reliabilitas instrumen dievaluasi melalui test-retest reliability dan internal consistency. Analisis dilakukan terhadap konsistensi data dalam periode waktu tertentu dan koherensi antar variabel yang terkait. Hasil menunjukkan reliabilitas yang acceptable untuk keperluan penelitian.

## 3.5 Tahapan 1: Data Collection (Pengumpulan Data)

### 3.5.1 Prosedur Pengumpulan Data

Tahapan pertama dalam alur metodologi adalah pengumpulan data dari dua platform utama: Strava untuk data aktivitas kardiovaskular dan Pomokit untuk data produktivitas akademik. Pengumpulan data dilakukan melalui API (Application Programming Interface) dari kedua platform dengan persetujuan pengguna yang telah menandatangani informed consent.

Proses dimulai dengan recruitment partisipan melalui social media dan komunitas mahasiswa, diikuti dengan instalasi aplikasi dan setup akun. Data dikumpulkan secara otomatis dalam periode 8 minggu untuk memastikan sufficient data points untuk analisis machine learning. Protokol pengumpulan data mencakup daily automatic sync, weekly data validation, dan monthly data backup.

Quality control dilakukan melalui automated anomaly detection dan manual verification untuk data yang mencurigakan. Partisipan juga diberikan reminder dan support untuk memastikan konsistensi penggunaan aplikasi selama periode penelitian.

### 3.5.2 Periode Pengumpulan Data

Periode pengumpulan data dilaksanakan selama 8 minggu berturut-turut untuk menangkap variabilitas temporal dalam aktivitas mahasiswa. Periode ini dipilih berdasarkan pertimbangan academic calendar, seasonal effects, dan kebutuhan minimum data points untuk machine learning analysis yang memerlukan minimal 300 observasi mingguan.

Data dikumpulkan pada level harian dan diagregasi menjadi weekly summaries untuk analisis. Pendekatan ini memungkinkan analisis trend jangka pendek sekaligus pattern jangka menengah yang relevan dengan siklus akademik mahasiswa. Temporal alignment dilakukan untuk memastikan konsistensi timeframe antara data dari kedua platform.

### 3.5.3 Ethical Considerations

Penelitian ini telah mendapatkan ethical clearance dari komite etik institusi dengan mempertimbangkan aspek privacy, confidentiality, dan informed consent [37]. Partisipan diberikan informasi lengkap tentang tujuan penelitian, prosedur pengumpulan data, dan hak-hak mereka sebagai subjek penelitian.

Data privacy dijaga melalui anonymization, encryption, dan secure storage. Partisipan memiliki hak untuk withdraw dari penelitian kapan saja tanpa konsekuensi. Data yang dikumpulkan hanya digunakan untuk keperluan penelitian dan tidak dibagikan kepada pihak ketiga tanpa persetujuan eksplisit.

## 3.6 Tahapan 2: Data Preprocessing (Pra-pemrosesan Data)

### 3.6.1 Data Cleaning dan Validation

Tahapan kedua dalam alur metodologi adalah data preprocessing yang dimulai dengan data cleaning untuk menangani missing values, outliers, dan inconsistencies. Missing values ditangani menggunakan kombinasi deletion dan imputation berdasarkan karakteristik data dan pattern missingness. Untuk missing values yang ekstensif (>50% dari observasi), dilakukan deletion untuk menghindari bias. Untuk missing values yang minimal (<10%), dilakukan imputation menggunakan mean untuk numerical variables dan mode untuk categorical variables.

Outlier detection dilakukan menggunakan statistical methods dan domain knowledge untuk mengidentifikasi nilai yang tidak realistis. Metode IQR (Interquartile Range) digunakan untuk mendeteksi outliers dengan threshold 1.5 × IQR di atas Q3 atau di bawah Q1. Z-score analysis juga diterapkan dengan threshold ±3 untuk mengidentifikasi extreme values. Domain knowledge diterapkan untuk memvalidasi outliers, misalnya durasi aktivitas >8 jam atau jarak >100km dianggap tidak realistis untuk aktivitas harian mahasiswa.

Validation rules diterapkan untuk memastikan logical consistency antar variabel. Konsistensi temporal divalidasi dengan memastikan timestamp yang valid dan sequential. Konsistensi unit divalidasi dengan memastikan semua measurements menggunakan unit yang konsisten (km untuk jarak, minutes untuk waktu). Data integrity checks dilakukan untuk memastikan tidak ada negative values untuk metrics yang seharusnya positif seperti distance dan duration.

### 3.6.2 Data Integration dan Harmonization

Integrasi data dari kedua platform dilakukan melalui temporal alignment berdasarkan timestamp dan user identification. Harmonization process mencakup standardization of data formats, unit conversion, dan creation of unified feature space. Data dari Strava dan Pomokit digabungkan berdasarkan weekly aggregation untuk memastikan konsistensi temporal.

Quality assurance dilakukan melalui cross-validation antara data dari kedua platform untuk mengidentifikasi inconsistencies. Proses ini menghasilkan dataset terintegrasi yang siap untuk tahapan feature engineering selanjutnya.

### 3.6.3 Format Standardization

Standardisasi format data dilakukan untuk memastikan konsistensi dalam representasi data. Ini meliputi konversi format waktu dari "41m 21s" menjadi minutes, standardisasi nama kolom dengan lowercase dan underscore, serta konversi tipe data yang sesuai (datetime, numeric, string).

Proses standardisasi juga mencakup handling special characters, normalisasi text fields, dan creation of consistent user identities untuk anonymization purposes.

## 3.7 Tahapan 3: Feature Engineering (Rekayasa Fitur)

### 3.7.1 Weekly Aggregation Features

Tahapan ketiga dalam alur metodologi adalah feature engineering yang dimulai dengan pembuatan weekly aggregation features dari data harian. Agregasi mingguan dipilih untuk menangkap pola behavioral yang konsisten sambil mengurangi noise dari variabilitas harian yang dapat disebabkan oleh faktor situasional.

Untuk data aktivitas kardiovaskular dari Strava, dilakukan agregasi dengan menghitung total distance per minggu (total_distance_km), rata-rata distance per aktivitas (avg_distance_km), total waktu aktivitas per minggu (total_time_minutes), rata-rata waktu per aktivitas (avg_time_minutes), dan jumlah hari dengan aktivitas fisik (activity_days). Agregasi ini memberikan gambaran komprehensif tentang volume, intensitas, dan konsistensi aktivitas fisik mahasiswa.

Untuk data produktivitas akademik dari Pomokit, dilakukan agregasi dengan menghitung total siklus kerja per minggu (total_cycles), rata-rata siklus per hari kerja (avg_cycles), dan jumlah hari kerja aktif (work_days). Agregasi ini menangkap pola produktivitas dan konsistensi dalam kegiatan akademik mahasiswa.

Proses agregasi menggunakan temporal alignment berdasarkan ISO week numbering untuk memastikan konsistensi periode antara kedua platform. Handling dilakukan untuk minggu dengan data parsial di awal atau akhir periode pengumpulan data. Statistical summaries dihitung untuk setiap minggu, termasuk measures of central tendency (mean, median) dan measures of variability (standard deviation, range) untuk memberikan gambaran distribusi aktivitas dalam setiap minggu.

### 3.7.2 Text-Based Feature Extraction

Feature extraction dari teks dilakukan pada judul aktivitas dari kedua platform menggunakan rule-based keyword matching approach. Text processing dimulai dengan basic preprocessing yang meliputi lowercasing untuk normalisasi case, removal of special characters yang tidak relevan, dan string standardization untuk memastikan konsistensi dalam keyword matching.

Language pattern detection dilakukan untuk mengidentifikasi dominasi bahasa Indonesia atau Inggris dalam activity titles. Simple handling diterapkan untuk abbreviations dan domain-specific terms yang umum digunakan dalam deskripsi aktivitas fitness dan produktivitas. Stemming tidak diterapkan untuk mempertahankan semantic meaning dari keywords yang spesifik.

Features yang diekstrak dibagi menjadi dua kategori: structural features dan semantic features. Structural features meliputi jumlah aktivitas per platform (strava_title_count, pomokit_title_count), total panjang karakter dari semua judul (strava_title_length, pomokit_title_length), dan jumlah kata unik yang digunakan (strava_unique_words, pomokit_unique_words). Features ini memberikan gambaran tentang volume dan diversity dalam deskripsi aktivitas.

Semantic features diekstrak menggunakan dictionary-based approach dengan predefined keyword lists yang telah dikurasi berdasarkan domain knowledge dan literature review. Keywords dikategorikan menjadi beberapa domain: stress indicators (stress_count), workload indicators (workload_count), negative emotion indicators (negative_emotion_count), recovery indicators (recovery_count), time pressure indicators (time_pressure_count), dan exhaustion indicators (exhaustion_count).

Emotional intensity indicators juga diekstrak dari text patterns, meliputi jumlah exclamation marks sebagai indikator emotional intensity, jumlah question marks sebagai indikator uncertainty, dan capitalization ratio sebagai indikator emphasis atau stress. Features ini memberikan additional layer dalam memahami emotional state mahasiswa melalui cara mereka mendeskripsikan aktivitas.

### 3.7.3 Gamification Variables

Gamification features dibuat untuk menangkap aspek motivational dan achievement-oriented dari aktivitas mahasiswa. Features ini dirancang berdasarkan gamification theory yang menunjukkan bahwa point-based systems dan achievement metrics dapat mempengaruhi behavioral patterns dan psychological states.

Activity points dihitung berdasarkan target jarak mingguan 6 kilometer, yang merupakan rekomendasi minimum aktivitas fisik untuk mahasiswa. Formula yang digunakan adalah (total_distance_km / 6) × 100 dengan maximum cap 100 points untuk mencegah outlier effects. Points ini mencerminkan seberapa baik mahasiswa memenuhi target aktivitas fisik mingguan.

Productivity points dihitung berdasarkan target 5 siklus kerja per minggu, yang setara dengan sekitar 2.5 jam focused work time. Formula yang digunakan adalah (total_cycles / 5) × 100 dengan maximum cap 100 points. Points ini mencerminkan konsistensi dan volume dalam kegiatan produktif akademik.

Achievement rate dihitung sebagai persentase pencapaian dari maksimal possible points, memberikan normalized measure yang memungkinkan comparison across individuals dengan baseline yang berbeda. Gamification balance dihitung sebagai rasio antara activity points dan productivity points, memberikan insight tentang balance antara aktivitas fisik dan akademik.

Derived metrics juga dihitung untuk menangkap consistency patterns. Consistency score dihitung berdasarkan regularity dalam aktivitas fisik dan produktivitas, dengan formula yang menggabungkan physical consistency (activity_days/7) dan work consistency (work_days/5). Weekly efficiency dihitung sebagai rasio total cycles terhadap work days, memberikan measure tentang produktivitas per hari kerja.

## 3.8 Tahapan 4: Labeling Strategy (Strategi Pelabelan)

### 3.8.1 Independent External Labeling

Tahapan keempat dalam alur metodologi adalah labeling strategy yang menggunakan pendekatan independent external labeling untuk mencegah data leakage. Pendekatan ini fundamental untuk memastikan bahwa target variable tidak derived dari input features yang akan digunakan untuk training model, sehingga menghindari circular dependency yang dapat menyebabkan artificial performance.

Temporal patterns analysis dilakukan dengan menganalisis consistency patterns dalam aktivitas mahasiswa sepanjang waktu. Analisis ini mengidentifikasi pola-pola yang mengindikasikan fatigue berdasarkan perubahan temporal dalam behavioral metrics. Misalnya, penurunan konsistensi aktivitas fisik yang berkelanjutan atau fluktuasi ekstrem dalam produktivitas dapat mengindikasikan fatigue risk.

Expert simulation dilakukan dengan mengimplementasikan decision rules yang berdasarkan domain knowledge dari literature tentang fatigue indicators. Rules ini dikembangkan berdasarkan established research tentang behavioral markers of fatigue, seperti decreased physical activity, irregular work patterns, dan reduced consistency in daily routines. Expert simulation tidak menggunakan raw input features, melainkan menggunakan meta-patterns yang dapat diobservasi secara independen.

Domain knowledge rules diimplementasikan berdasarkan understanding tentang student behavior dan fatigue manifestation dalam academic context. Rules ini mencakup indicators seperti extreme imbalance antara physical activity dan academic work, prolonged periods of low activity, atau patterns yang konsisten dengan academic stress cycles. Rules dikembangkan untuk capture fatigue indicators yang tidak directly dependent pada specific feature values.

### 3.8.2 Multiple Labeling Approaches

Penelitian ini mengimplementasikan multiple labeling approaches untuk validation dan comparison:

1. **Regular Fatigue Classification:** Composite scoring dari input features (detected data leakage)
2. **Bias-Corrected Classification:** Adjusted composite scoring method (69-71% accuracy)
3. **Title-Only Classification:** Text analysis dari activity titles (65-68% accuracy)
4. **External Independent Labels:** Temporal patterns dan expert simulation (60-61% accuracy)

### 3.8.3 Target Recreation Testing

Target recreation testing merupakan metodologi validation yang dikembangkan untuk mendeteksi data leakage dengan mencoba mereproduksi target variable menggunakan input features. Konsep dasar dari testing ini adalah bahwa jika input features dapat memprediksi target variable dengan accuracy yang sangat tinggi, maka kemungkinan besar terdapat information leakage dari target creation process ke input features.

Prosedur target recreation testing dilakukan dengan langkah-langkah sebagai berikut: (1) menggunakan input features sebagai predictor variables, (2) menggunakan target variable sebagai outcome variable, (3) melatih machine learning model untuk memprediksi target menggunakan input features, (4) mengevaluasi accuracy dari prediksi tersebut, (5) membandingkan accuracy dengan threshold yang telah ditetapkan.

Threshold >70% recreation success rate ditetapkan sebagai indikator data leakage berdasarkan pertimbangan bahwa behavioral prediction dalam domain yang complex seperti fatigue seharusnya tidak mencapai accuracy yang terlalu tinggi. Accuracy >70% dalam mereproduksi target mengindikasikan bahwa target variable kemungkinan besar dibuat menggunakan informasi yang sama atau highly correlated dengan input features.

Testing ini menggunakan robust cross-validation approach untuk memastikan bahwa hasil tidak bias oleh specific train-test split. Stratified k-fold cross-validation dengan k=5 digunakan untuk memberikan estimate yang stable tentang recreation accuracy. Multiple algorithms juga digunakan untuk testing, termasuk tree-based models yang sensitive terhadap feature-target relationships dan linear models yang dapat mendeteksi linear dependencies.

Interpretasi hasil target recreation testing dilakukan dengan mempertimbangkan domain context dan expected performance untuk behavioral prediction. Accuracy 60-70% dianggap reasonable untuk behavioral prediction, accuracy 70-85% dianggap suspicious dan memerlukan investigation lebih lanjut, sedangkan accuracy >85% dianggap strong indicator of data leakage yang memerlukan immediate corrective action.

Validation method ini menjadi standard practice dalam penelitian ini untuk memastikan independence antara features dan target. Setiap pipeline dan labeling approach di-test menggunakan metodologi ini untuk memverifikasi absence of circular dependency. Results dari target recreation testing digunakan sebagai primary criterion untuk menentukan validity dari different labeling approaches.

## 3.9 Tahapan 5: Feature Selection (Seleksi Fitur)

### 3.9.1 Recursive Feature Elimination (RFE) Analysis

Tahapan kelima adalah feature selection menggunakan Recursive Feature Elimination (RFE) analysis untuk menentukan subset fitur yang optimal. RFE merupakan metode backward elimination yang secara iteratif menghilangkan fitur-fitur yang kurang penting berdasarkan skor kepentingan yang diberikan oleh algoritma machine learning.

Proses RFE dimulai dengan melatih model menggunakan seluruh fitur, kemudian menghitung importance score untuk setiap fitur. Fitur dengan skor terendah dieliminasi, dan proses diulang hingga mencapai jumlah fitur target yang diinginkan. Penelitian ini menggunakan empat algoritma berbeda untuk RFE: Logistic Regression, Random Forest, Gradient Boosting, dan Support Vector Machine.

Setiap algoritma memberikan perspektif yang berbeda dalam menilai kepentingan fitur. Logistic Regression memberikan linear importance berdasarkan koefisien, Random Forest menggunakan impurity-based importance, Gradient Boosting menghitung gain-based importance, dan SVM menggunakan weight-based importance. Kombinasi keempat algoritma ini memberikan robust evaluation terhadap kepentingan fitur.

Testing dilakukan dengan berbagai jumlah fitur target: 3, 5, 7, 10, 15, dan 20 fitur. Setiap kombinasi algoritma dan jumlah fitur dievaluasi menggunakan 5-fold stratified cross-validation untuk memastikan stabilitas dan generalizability. Metrik evaluasi yang digunakan meliputi accuracy, F1-score macro, precision macro, dan recall macro.

### 3.9.2 SHAP-Based Feature Importance Analysis

SHAP (SHapley Additive exPlanations) analysis digunakan sebagai primary methodology untuk interpretable feature selection. SHAP memberikan individual feature contributions yang dapat ditranslasi menjadi actionable insights untuk implementasi bisnis, menggantikan traditional correlation analysis yang hanya memberikan aggregate relationships.

SHAP analysis berdasarkan pada game theory, khususnya Shapley values, yang memberikan fair attribution terhadap kontribusi setiap fitur dalam prediksi model. Setiap fitur mendapatkan SHAP value yang menunjukkan seberapa besar kontribusinya terhadap perubahan output model dari baseline prediction.

Implementasi SHAP menggunakan permutation-based feature importance yang menghitung dampak penghilangan setiap fitur terhadap performa model. Proses ini dilakukan dengan cara: (1) menghitung baseline accuracy dengan semua fitur, (2) menghilangkan satu fitur dan menghitung accuracy tanpa fitur tersebut, (3) menghitung selisih accuracy sebagai importance score fitur, (4) mengulangi proses untuk semua fitur.

SHAP analysis memberikan beberapa keunggulan: (1) model-agnostic approach yang compatible dengan berbagai algoritma, (2) individual contributions yang granular dibandingkan aggregate correlation, (3) interaction effects yang dapat menangkap hubungan kompleks antar fitur, (4) interpretability yang memungkinkan explanation untuk setiap prediksi, (5) business actionability yang dapat ditranslasi ke strategi intervensi praktis.

### 3.9.3 Data Leakage Prevention dalam Feature Selection

Feature filtering merupakan komponen kritis dalam tahapan feature selection untuk mencegah data leakage. Data leakage terjadi ketika informasi dari target variable secara tidak sengaja "bocor" ke dalam input features, menyebabkan artificial performance yang tidak realistis.

Identifikasi fitur berbahaya dilakukan dengan mengkategorikan semua fitur berdasarkan sumber pembuatannya. Fitur yang dibuat menggunakan informasi dari target variable atau proses yang sama dengan target creation diklasifikasikan sebagai "dangerous features" dan harus dihilangkan. Fitur yang dibuat secara independen dari target variable diklasifikasikan sebagai "safe features" dan dapat digunakan untuk training model.

Proses filtering dilakukan dalam beberapa tahap: (1) identifikasi semua fitur yang digunakan dalam proses labeling atau target creation, (2) removal fitur-fitur tersebut dari dataset training, (3) validasi independence melalui correlation analysis, (4) statistical independence testing untuk memastikan tidak ada circular dependency.

Validation independence dilakukan melalui correlation analysis dengan threshold yang ketat. Fitur dengan correlation >0.5 terhadap target diklasifikasikan sebagai high-risk dan dihilangkan. Fitur dengan correlation 0.3-0.5 diklasifikasikan sebagai suspicious dan perlu review manual. Fitur dengan correlation <0.3 diklasifikasikan sebagai safe dan dapat digunakan untuk training.

Target recreation testing dilakukan sebagai validation method tambahan. Testing ini mencoba mereproduksi target variable menggunakan input features dengan threshold >70% recreation success rate sebagai indikator data leakage. Jika model dapat mereproduksi target dengan accuracy >70%, maka terdapat indikasi strong circular dependency yang memerlukan additional filtering.

### 3.9.4 Multi-Level Feature Filtering Strategy

Penelitian ini mengimplementasikan multi-level feature filtering strategy untuk memastikan robustness dalam data leakage prevention. Strategy ini terdiri dari tiga level filtering dengan tingkat konservatisme yang berbeda.

**Ultra-Safe Filtering:** Level pertama menggunakan extremely conservative approach dengan menghilangkan semua fitur yang memiliki kemungkinan terkait dengan target creation. Filtering ini menghasilkan dataset dengan jumlah fitur minimal (sekitar 11 fitur) tetapi dengan confidence tinggi terhadap independence. Approach ini cocok untuk production deployment yang memerlukan certainty tinggi.

**Conservative Filtering:** Level kedua menggunakan balanced approach antara safety dan feature richness. Filtering ini menghilangkan fitur yang jelas terkait dengan target creation tetapi mempertahankan fitur yang memiliki ambiguitas rendah. Hasil filtering menghasilkan dataset dengan jumlah fitur moderate (sekitar 29 fitur) dengan balance antara performance dan safety.

**Standard Filtering:** Level ketiga menggunakan standard approach yang menghilangkan hanya fitur yang secara eksplisit digunakan dalam target creation. Filtering ini menghasilkan dataset dengan jumlah fitur maksimal tetapi dengan risk moderate terhadap potential leakage. Approach ini cocok untuk exploratory analysis dan research purposes.

Setiap level filtering dievaluasi secara terpisah untuk memahami trade-off antara safety dan performance. Validation dilakukan melalui multiple metrics: correlation analysis, target recreation testing, cross-validation stability, dan business interpretability. Hasil evaluasi digunakan untuk menentukan optimal filtering level untuk production deployment.

## 3.10 Tahapan 6: Training Model (Pelatihan Model)

### 3.10.1 Multiple Algorithm Implementation

Tahapan keenam adalah training model menggunakan multiple algorithms untuk comprehensive comparison dan robust evaluation. Pemilihan multiple algorithms dilakukan untuk memastikan bahwa hasil penelitian tidak bias terhadap specific algorithmic assumptions dan untuk mengidentifikasi algorithm yang paling sesuai dengan karakteristik data behavioral.

Random Forest dipilih sebagai representative dari ensemble tree-based methods karena kemampuannya dalam handling mixed data types, robustness terhadap outliers, dan ability untuk capture non-linear relationships dan feature interactions. Algorithm ini juga memberikan built-in feature importance measures yang dapat digunakan untuk interpretability analysis. Random Forest particularly effective untuk behavioral data yang sering mengandung complex interactions antar variables.

Support Vector Machine (SVM) dipilih sebagai representative dari kernel-based methods karena kemampuannya dalam handling high-dimensional data dan non-linear relationships melalui kernel functions. SVM dengan linear kernel digunakan untuk compatibility dengan RFE analysis yang memerlukan feature importance attributes. Algorithm ini memiliki strong theoretical foundation dan good generalization properties.

Logistic Regression dipilih sebagai representative dari linear methods karena interpretability yang tinggi dan stability dalam training. Algorithm ini memberikan probabilistic output yang meaningful dan coefficients yang dapat diinterpretasi secara langsung. Logistic Regression juga robust terhadap overfitting dan memberikan baseline performance yang good untuk comparison dengan more complex algorithms.

Gradient Boosting dipilih sebagai representative dari boosting ensemble methods untuk menangkap complex patterns melalui sequential learning approach. Algorithm ini dapat learn from mistakes of previous weak learners dan gradually improve prediction accuracy. Gradient Boosting effective untuk capturing subtle patterns dalam behavioral data yang mungkin tidak terdeteksi oleh single algorithms.

### 3.10.2 Pipeline Implementation

Model training dilakukan melalui comprehensive pipeline yang mencakup data preprocessing, class imbalance handling, dan classification. Pipeline approach digunakan untuk memastikan consistency dalam data processing dan untuk mencegah data leakage antara training dan validation sets.

StandardScaler diterapkan untuk feature normalization karena features memiliki scales yang berbeda (misalnya distance dalam kilometers vs. activity count). Normalization penting terutama untuk algorithms yang sensitive terhadap feature scales seperti SVM dan Neural Networks. Z-score normalization digunakan dengan mean=0 dan standard deviation=1 untuk setiap feature.

SMOTE (Synthetic Minority Oversampling Technique) diterapkan untuk handling class imbalance dalam target variable. Class imbalance dapat menyebabkan bias dalam model training dimana majority class mendominasi learning process. SMOTE generates synthetic examples untuk minority classes dengan interpolating between existing minority class examples, sehingga meningkatkan representation tanpa simply duplicating existing examples.

Pipeline integration memastikan bahwa preprocessing steps diterapkan secara konsisten pada training dan validation data. Fit operations (seperti calculating mean dan standard deviation untuk scaling) hanya dilakukan pada training data, sedangkan transform operations diterapkan pada both training dan validation data. Approach ini mencegah information leakage dari validation set ke training process.

Cross-validation dengan stratified k-fold (k=5) diterapkan untuk evaluasi yang robust. Stratification memastikan bahwa distribusi target classes maintained across all folds, yang penting untuk imbalanced datasets. Multiple random seeds digunakan untuk stability analysis dengan coefficient of variation <2% sebagai threshold untuk production deployment, memastikan bahwa model performance consistent across different random initializations.

### 3.10.3 Hyperparameter Optimization

Hyperparameter tuning dilakukan untuk setiap algorithm dengan parameter-specific optimization. Untuk Random Forest: n_estimators, max_depth, min_samples_split. Untuk SVM: C, gamma, kernel type. Untuk Neural Networks: hidden layer sizes, activation functions, learning rate.

Bayesian Optimization juga diterapkan untuk efficient hyperparameter search pada space yang besar. Early stopping dan regularization techniques diterapkan untuk mencegah overfitting dengan monitoring train-test gap <5%.

## 3.11 Tahapan 7: Evaluation Model (Evaluasi Model)

### 3.11.1 Comprehensive Metrics Evaluation

Tahapan ketujuh adalah evaluasi model menggunakan comprehensive metrics yang sesuai untuk multi-class classification tasks. Primary metrics meliputi accuracy, precision, recall, dan F1-score untuk setiap kelas fatigue risk (low, medium, high).

Macro-averaged dan micro-averaged metrics dihitung untuk memberikan gambaran overall performance. Secondary metrics meliputi AUC-ROC, confusion matrix, dan classification report. Statistical significance testing dilakukan untuk membandingkan performa antar model.

### 3.11.2 Cross-Validation Strategy

Stratified k-fold cross-validation dengan k=5 diterapkan untuk evaluasi yang robust dengan mempertahankan distribusi kelas yang seimbang di setiap fold. Time-series considerations diterapkan untuk mempertimbangkan temporal nature dari data.

Nested cross-validation dilakukan untuk unbiased evaluation ketika hyperparameter tuning dilakukan. Outer loop untuk model evaluation dan inner loop untuk hyperparameter selection memastikan tidak ada information leakage dalam evaluasi.

### 3.11.3 Model Comparison dan Selection

Model comparison dilakukan menggunakan statistical tests seperti paired t-test dan Wilcoxon signed-rank test untuk menentukan significant differences antar model. Effect size juga dihitung untuk memahami practical significance dari perbedaan performa.

Model selection dilakukan berdasarkan kombinasi performa, interpretability, dan computational efficiency. Ensemble methods juga dievaluasi untuk menggabungkan strengths dari individual models.

## 3.12 Tahapan 8: Results & Analysis (Hasil dan Analisis)

### 3.12.1 Feature Importance Analysis

Tahapan kedelapan dan terakhir adalah results & analysis yang menghasilkan feature importance analysis sebagai output utama penelitian. Analysis ini dirancang untuk memberikan actionable insights yang dapat diimplementasikan dalam practical applications untuk fatigue monitoring dan intervention.

Feature importance analysis dilakukan menggunakan multiple approaches untuk memastikan robustness dan comprehensiveness. SHAP (SHapley Additive exPlanations) values digunakan sebagai primary method untuk memberikan individual feature contributions yang dapat diinterpretasi secara granular. SHAP analysis memberikan contribution score untuk setiap feature dalam setiap prediction, memungkinkan understanding tentang bagaimana setiap feature mempengaruhi specific outcomes.

Permutation importance analysis dilakukan sebagai complementary method untuk validate SHAP results. Permutation importance mengukur decrease dalam model performance ketika values dari specific feature di-randomize, memberikan measure tentang seberapa dependent model terhadap each feature. Method ini model-agnostic dan dapat applied ke any algorithm.

Ablation-style analysis dilakukan dengan systematically removing features dan measuring impact terhadap model performance. Analysis ini memberikan understanding tentang which features are essential untuk maintaining acceptable performance dan which features are redundant atau minimally contributive. Results digunakan untuk identifying minimal feature set yang dapat maintain performance while reducing complexity.

Output yang dihasilkan meliputi optimal feature set dengan justification untuk setiap feature inclusion, feature importance rankings berdasarkan multiple metrics, feature impact analysis yang menunjukkan effect size dari each feature, dan comprehensive interpretability report yang menjelaskan practical implications dari feature importance results.

### 3.12.2 Business Actionability Assessment

Feature importance results ditranslasi ke business actionable insights dengan mengidentifikasi specific intervention strategies berdasarkan feature contributions. Translation process melibatkan mapping dari statistical importance measures ke practical actions yang dapat diimplementasikan dalam real-world fatigue monitoring systems.

Primary monitoring focus ditetapkan pada physical activity metrics berdasarkan combined SHAP importance yang tinggi. Intervention strategies untuk physical activity meliputi: (1) automated reminders ketika weekly distance falls below threshold, (2) personalized activity recommendations berdasarkan historical patterns, (3) progressive goal setting untuk gradually increasing activity levels, (4) social support mechanisms untuk encouraging consistent physical activity.

Secondary monitoring meliputi time investment patterns dan work pattern balance. Intervention strategies untuk productivity patterns meliputi: (1) workload balancing recommendations ketika work intensity too high atau too low, (2) break scheduling suggestions berdasarkan optimal work-rest cycles, (3) time management coaching untuk improving efficiency, (4) stress management interventions ketika work patterns indicate excessive pressure.

Multi-level threshold-based warning system dikembangkan dengan personalized interventions berdasarkan feature importance rankings. Level 1 warnings triggered ketika single high-importance feature shows concerning patterns, Level 2 warnings triggered ketika multiple medium-importance features show concerning patterns, dan Level 3 warnings triggered ketika combination of features indicates high fatigue risk.

Personalization dilakukan berdasarkan individual baseline patterns dan historical data. Intervention recommendations adapted berdasarkan user preferences, past response to interventions, dan contextual factors seperti academic calendar dan seasonal variations. Feedback loops implemented untuk continuously improving intervention effectiveness berdasarkan user outcomes dan engagement.

### 3.12.3 Production Deployment Recommendations

Berdasarkan comprehensive analysis dari multiple pipeline comparison, SHAP Title-Only Analysis approach direkomendasikan untuk production deployment. Recommendation ini berdasarkan optimal balance antara performance (68.33% accuracy dan 67.43% F1-score), interpretability, dan practical implementability.

Pipeline selection criteria meliputi: (1) performance metrics yang realistic untuk behavioral prediction domain, (2) absence of overfitting indicators seperti large train-test gap, (3) interpretability yang memungkinkan actionable insights, (4) robustness across different validation approaches, (5) computational efficiency untuk real-time applications, (6) data requirements yang feasible untuk practical implementation.

Implementation strategy dikembangkan dengan hierarchical monitoring approach. Primary monitoring focus pada physical activity metrics yang menunjukkan highest feature importance dan strongest relationship dengan fatigue outcomes. Secondary monitoring pada time investment patterns yang memberikan complementary information tentang work-life balance. Tertiary monitoring pada work pattern balance untuk capturing academic stress indicators.

Alert system architecture dikembangkan dengan multi-level threshold-based warnings yang escalate berdasarkan severity dan persistence of concerning patterns. Early warning system implemented untuk detecting gradual changes yang may indicate developing fatigue before reaching critical levels. Personalized interventions delivered berdasarkan individual risk profiles dan historical response patterns.

Quality assurance framework established untuk continuous monitoring of model performance dalam production environment. Performance degradation detection implemented untuk identifying when model retraining necessary. Data drift monitoring established untuk detecting changes dalam input data distribution yang may affect model validity. User feedback integration implemented untuk continuously improving intervention effectiveness dan user satisfaction.

## 3.13 Multiple Machine Learning Pipeline Comparison

![Arsitektur ML Pipeline](laporan-akhir/figures/gambar_3_2_ml_pipeline_comparison.png)

**Gambar 3.2** Multiple Pipeline Comparison Architecture

### 3.13.1 Pipeline Comparison Framework

**6 Pipeline Implementation untuk Comprehensive Analysis:**

**Tabel 3.1** Pipeline Comparison Overview

| Pipeline          | Method             | Focus                 | Data Leakage Risk        | Status              |
| ----------------- | ------------------ | --------------------- | ------------------------ | ------------------- |
| main3.py          | Standard Ablation  | Baseline referensi    | LOW                      | ✅ Valid            |
| main4.py          | RFE Regular        | Feature optimization  | HIGH (96.67% artificial) | ❌ Invalid          |
| main5.py          | RFE Bias-Corrected | Bias mitigation       | MEDIUM                   | ⚠️ Caution          |
| main6.py          | RFE Title-Only     | Text-based prediction | MEDIUM (35% overfitting) | ⚠️ Caution          |
| main7.py          | SHAP Title-Only    | Interpretable ML      | LOW                      | ✅ Best Performance |
| main_leak_free.py | External Labels    | Leak-free methodology | NONE                     | ✅ Production Ready |

**Key Findings dari Pipeline Comparison:**

-   **Data leakage detection** pada main4.py (96.67% → 60-68% setelah correction)
-   **SHAP analysis superiority** pada main7.py (68.33% accuracy dengan interpretability)
-   **External labeling necessity** untuk production deployment (main_leak_free.py)

### 3.8.1 Algoritma yang Digunakan

Penelitian ini mengimplementasikan empat algoritma machine learning utama untuk klasifikasi fatigue: Random Forest, Support Vector Machine (SVM), Logistic Regression, dan Gradient Boosting. Random Forest dipilih karena kemampuannya dalam handling mixed data types, robustness terhadap outliers, dan interpretability melalui feature importance.

Support Vector Machine digunakan untuk kemampuannya dalam handling high-dimensional data dengan linear kernel untuk compatibility dengan RFE analysis. Logistic Regression diterapkan sebagai baseline linear method dengan interpretability yang tinggi. Gradient Boosting digunakan untuk menangkap complex patterns melalui sequential ensemble learning approach.

### 3.8.2 Hyperparameter Optimization

Hyperparameter tuning dilakukan menggunakan Grid Search dan Random Search dengan cross-validation untuk setiap algoritma. Untuk Random Forest, parameter yang dioptimasi meliputi n_estimators, max_depth, min_samples_split, dan min_samples_leaf. Untuk SVM, optimasi dilakukan pada parameter C dan kernel type (linear kernel untuk RFE compatibility).

Untuk Logistic Regression, optimasi dilakukan pada parameter C (regularization strength), solver type, dan max_iter untuk convergence. Untuk Gradient Boosting, parameter yang dioptimasi meliputi n_estimators, learning_rate, max_depth, dan min_samples_split. Bayesian Optimization juga diterapkan untuk efficient hyperparameter search pada space yang besar.

### 3.8.3 SHAP Analysis sebagai Primary Methodology

**SHAP (SHapley Additive exPlanations) Implementation:**

Penelitian ini menggunakan SHAP analysis sebagai primary methodology untuk interpretable machine learning, menggantikan traditional correlation analysis. SHAP memberikan individual feature contributions yang actionable untuk business implementation.

**SHAP Analysis Framework:**

-   **Permutation-based Feature Importance:** Individual contribution analysis untuk setiap feature
-   **Game Theory Foundation:** Shapley values untuk fair attribution of feature contributions
-   **Model-Agnostic Approach:** Compatible dengan multiple algorithms (LR, RF, GB, SVM)
-   **Business Actionability:** Feature importance yang dapat ditranslasi ke business actions

**Key Advantages over Traditional Methods:**

-   **Individual Contributions:** Granular analysis vs aggregate correlation
-   **Interaction Effects:** Capture feature interactions yang tidak terdeteksi correlation
-   **Interpretability:** Clear explanation untuk setiap prediction
-   **Production Ready:** Actionable insights untuk real-time monitoring

### 3.8.4 Model Training dan Validation

Model training dilakukan menggunakan stratified train-test split dengan rasio 80:20 untuk mempertahankan distribusi kelas yang seimbang. Cross-validation dengan k=5 folds diterapkan untuk evaluasi yang robust dan mengurangi variance dalam estimasi performa [36].

**Stability Analysis Implementation:**

-   **Multiple Runs:** 6 consecutive runs untuk assess fluktuasi accuracy
-   **Random Seed Control:** Numpy random seed setting untuk reproducibility
-   **Coefficient of Variation:** <2% acceptable untuk production deployment
-   **Overfitting Detection:** Train-test gap monitoring dengan <5% threshold

## 3.9 Ablation Study

### 3.9.1 Desain Systematic Feature Removal

Ablation study dirancang untuk mengevaluasi kontribusi individual dan kombinatorial dari setiap feature terhadap performa model. Pendekatan systematic feature removal dilakukan dengan menghilangkan satu feature pada satu waktu (individual ablation) dan kombinasi features (group ablation).

Features dikelompokkan berdasarkan kategori: physical activity features, productivity features, gamification features, dan text-based features. Ablation dilakukan pada level individual features dan group level untuk memahami relative importance dan interaction effects.

### 3.9.2 Evaluasi Impact Feature Removal

Evaluasi dampak feature removal dilakukan dengan membandingkan performa model sebelum dan sesudah penghilangan features. Metrics yang digunakan meliputi accuracy, precision, recall, F1-score, dan AUC-ROC untuk memberikan gambaran comprehensive tentang performa model.

Statistical significance testing dilakukan untuk menentukan apakah penurunan performa akibat feature removal signifikan secara statistik. Effect size juga dihitung untuk memahami magnitude dari dampak feature removal terhadap performa model.

### 3.9.3 Identification of Minimal Feature Set

Berdasarkan hasil ablation study, dilakukan identifikasi minimal feature set yang masih dapat mempertahankan performa model yang acceptable. Threshold performa ditetapkan pada 95% dari performa model lengkap untuk menentukan features yang essential.

Forward selection dan backward elimination juga diterapkan untuk validasi hasil ablation study. Analisis dilakukan untuk mengidentifikasi redundant features dan optimal feature combinations yang dapat meningkatkan efficiency tanpa mengorbankan accuracy.

## 3.10 Data Leakage Prevention (CRITICAL DISCOVERY)

### 3.10.1 Data Leakage Detection Framework

**CRITICAL FINDING:** Penelitian ini mengungkapkan serious data leakage pada implementasi awal yang menyebabkan artificial performance 96.67%. Data leakage terjadi karena target variable dibuat menggunakan input features yang sama untuk model training.

**Target Recreation Testing Method:**

-   **Procedure:** Attempt to recreate target variable menggunakan input features
-   **Threshold:** >70% recreation success rate indicates data leakage
-   **Result:** 80% success rate pada regular fatigue classification
-   **Conclusion:** **LEAKAGE CONFIRMED** - Circular dependency detected

**Evidence of Data Leakage:**

1. **Artificial Performance:** 96.67% accuracy (terlalu tinggi untuk behavioral prediction)
2. **Tree-based Model Dominance:** Random Forest >94% vs Linear Models ~66%
3. **Target Recreation Success:** 80% accuracy dalam mereproduksi target
4. **Feature-Target Correlation:** High correlation antara composite features dan target

### 3.10.2 Data Leakage Prevention Strategies (IMPLEMENTED)

**1. External Independent Labeling (main_leak_free.py):**

-   **Temporal Patterns Analysis:** Activity consistency patterns over time
-   **Expert Simulation:** Domain knowledge-based rules independent dari input features
-   **Domain-Specific Indicators:** Fatigue indicators yang tidak derived dari input features
-   **Result:** 60-61% accuracy (realistic untuk behavioral prediction)

**2. Strict Feature Filtering:**

-   **Removal:** Semua features yang digunakan dalam target creation
-   **Validation:** Feature independence testing melalui correlation analysis
-   **Safe Features:** Only features yang tidak contribute ke target creation

**3. Target Recreation Testing:**

-   **Continuous Monitoring:** Regular testing untuk detect potential leakage
-   **Threshold Monitoring:** Alert system jika recreation success >70%
-   **Pipeline Validation:** Setiap pipeline di-test untuk leakage risk

### 3.10.3 Bias Correction Techniques

Bias correction techniques diterapkan pada berbagai level: data level, algorithm level, dan post-processing level [25]. Pada data level, resampling dan reweighting dilakukan untuk mengatasi sampling bias. Pada algorithm level, fairness constraints dan regularization diterapkan.

Post-processing bias correction dilakukan melalui calibration dan threshold adjustment untuk memastikan fairness across different groups. Ensemble methods juga diterapkan untuk mengurangi bias individual models melalui model averaging.

## 3.11 Evaluasi Model

### 3.11.1 Metrics Evaluasi

Evaluasi model dilakukan menggunakan comprehensive metrics yang sesuai untuk classification tasks. Primary metrics meliputi accuracy, precision, recall, dan F1-score untuk setiap kelas fatigue risk. Macro-averaged dan micro-averaged metrics dihitung untuk memberikan gambaran overall performance.

Secondary metrics meliputi AUC-ROC, AUC-PR, confusion matrix, dan classification report. Metrics khusus untuk imbalanced datasets seperti balanced accuracy dan Matthews Correlation Coefficient juga dihitung. Statistical significance testing dilakukan untuk membandingkan performa antar model.

### 3.11.2 Cross-Validation Strategy

Stratified k-fold cross-validation dengan k=5 diterapkan untuk evaluasi yang robust [23]. Stratification memastikan distribusi kelas yang seimbang di setiap fold. Time-series cross-validation juga diterapkan untuk mempertimbangkan temporal nature dari data.

Nested cross-validation dilakukan untuk unbiased evaluation ketika hyperparameter tuning dilakukan. Outer loop untuk model evaluation dan inner loop untuk hyperparameter selection memastikan tidak ada information leakage dalam evaluasi.

### 3.11.3 Model Comparison dan Selection

Model comparison dilakukan menggunakan statistical tests seperti paired t-test dan Wilcoxon signed-rank test untuk menentukan significant differences antar model. Effect size juga dihitung untuk memahami practical significance dari perbedaan performa.

Model selection dilakukan berdasarkan kombinasi performa, interpretability, dan computational efficiency. Ensemble methods juga dievaluasi untuk menggabungkan strengths dari individual models. Final model dipilih berdasarkan comprehensive evaluation yang mempertimbangkan semua aspek tersebut.

## 3.12 Implementasi dan Tools

### 3.12.1 Programming Languages dan Libraries

Implementasi dilakukan menggunakan Python sebagai bahasa pemrograman utama dengan ecosystem libraries yang rich untuk machine learning dan data analysis. Libraries utama yang digunakan meliputi pandas untuk data manipulation, scikit-learn untuk machine learning, dan numpy untuk numerical operations dan basic text processing.

Visualization dilakukan menggunakan matplotlib, seaborn, dan plotly untuk creating informative plots dan dashboards. Statistical analysis menggunakan scipy dan statsmodels. Deep learning implementations menggunakan TensorFlow/Keras untuk neural network models.

### 3.12.2 Development Environment

Development environment menggunakan Jupyter Notebooks untuk exploratory data analysis dan prototyping, dengan migration ke Python scripts untuk production code. Version control menggunakan Git untuk tracking changes dan collaboration.

Cloud computing resources digunakan untuk computationally intensive tasks seperti hyperparameter optimization dan large-scale cross-validation. Containerization menggunakan Docker untuk ensuring reproducibility across different environments.

### 3.12.3 Reproducibility dan Documentation

Reproducibility dijaga melalui systematic documentation, version control, dan environment management. Random seeds ditetapkan untuk semua stochastic processes. Detailed logging dilakukan untuk tracking experiments dan results.

Code documentation menggunakan docstrings dan comments yang comprehensive. Experimental protocols didokumentasikan dalam detail untuk memungkinkan replication. Data provenance tracking dilakukan untuk ensuring traceability dari raw data hingga final results.

## 3.13 Metodologi Validation dan Critical Discoveries

### 3.13.1 Critical Findings dari Implementasi

**1. Data Leakage sebagai Major Threat:**

-   **Discovery:** 96.67% artificial performance pada main4.py karena circular dependency
-   **Root Cause:** Target variable dibuat menggunakan input features yang sama
-   **Impact:** Mengubah fundamental approach dari composite scoring ke external labeling
-   **Solution:** Target recreation testing sebagai standard validation method

**2. SHAP Analysis Superiority:**

-   **Finding:** SHAP analysis (main7.py) memberikan best balance antara performance (68.33%) dan interpretability
-   **Advantage:** Individual feature contributions yang actionable untuk business implementation
-   **Implementation:** Permutation-based importance menggantikan traditional correlation analysis
-   **Business Value:** Physical activity dominance (23.84% combined SHAP importance)

**3. Title-Only Analysis Viability:**

-   **Result:** 65-68% accuracy menggunakan text features saja
-   **Practical Value:** Alternative approach ketika full quantitative data tidak tersedia
-   **Limitation:** Overfitting risk pada complex models (35% pada main6.py Random Forest)
-   **Recommendation:** Use Logistic Regression untuk title-only analysis

### 3.13.2 Sequential Methodology Framework Validation

**Validated Sequential Process:**

1. **✅ Data Collection & Preprocessing:** Standard approach validated
2. **✅ Independent Labeling (CRITICAL):** Must precede feature engineering untuk prevent leakage
3. **✅ Leak-Free Feature Engineering:** Essential untuk valid results
4. **✅ Multiple Pipeline Comparison:** Provides comprehensive robustness validation

**Performance Benchmarks Established:**

-   **Realistic Behavioral Prediction:** 60-68% accuracy range (post-leakage correction)
-   **Data Leakage Detection Threshold:** >70% target recreation success indicates leakage
-   **Model Stability Requirement:** <2% coefficient of variation acceptable untuk production
-   **Overfitting Prevention Threshold:** <5% train-test gap untuk production deployment

### 3.13.3 Production Deployment Framework

**Recommended Pipeline:** main7.py (SHAP Title-Only Analysis)

-   **Performance:** 68.33% accuracy dengan 67.43% F1-score
-   **Interpretability:** Individual SHAP feature contributions dengan business actionability
-   **Stability:** No overfitting detected (<5% train-test gap)
-   **Business Value:** Clear intervention strategies berdasarkan feature importance

**Implementation Strategy:**

-   **Primary Monitoring:** Physical activity metrics (avg_distance_km + total_distance_km = 23.84%)
-   **Secondary Monitoring:** Time investment patterns (total_time_minutes = 12.33%)
-   **Tertiary Monitoring:** Work pattern balance (total_cycles + work_days = 17.34%)
-   **Alert System:** Multi-level threshold-based warnings dengan personalized interventions

**Quality Assurance Framework:**

-   **Data Leakage Prevention:** Mandatory target recreation testing untuk setiap pipeline
-   **Feature Independence Validation:** Statistical independence testing
-   **Model Stability Monitoring:** Multiple runs dengan coefficient of variation tracking
-   **Business Actionability Assessment:** SHAP feature importance translation ke practical actions

### 3.13.4 Lessons Learned dan Best Practices

**Critical Lessons:**

1. **Data Leakage Prevention is Paramount:** Target recreation testing harus menjadi standard practice
2. **SHAP > Correlation:** Individual feature contributions lebih actionable daripada aggregate correlations
3. **External Labeling Necessity:** Independent target creation essential untuk valid behavioral prediction
4. **Multiple Pipeline Validation:** Comprehensive comparison prevents methodological bias

**Best Practices Established:**

-   **Sequential Methodology:** Labeling → Feature Engineering → Modeling (tidak parallel)
-   **Stability Testing:** Multiple runs dengan random seed control
-   **Interpretability Focus:** SHAP analysis untuk production-ready insights
-   **Business Translation:** Feature importance harus translatable ke actionable interventions

**Future Research Directions:**

-   **Real Expert Labeling:** Replace simulated labels dengan actual expert assessments
-   **Longitudinal Validation:** Extended temporal validation untuk seasonal effects
-   **Real-time Implementation:** Live monitoring system dengan streaming data processing
-   **Advanced Feature Engineering:** Physiological data integration untuk enhanced prediction
