experiment_name,features_used,feature_count,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
Baseline_All_Features,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",20,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7208333333333333,0.050833333333333286,success
Without_total_title_diversity,"['total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7141666666666666,0.044166666666666576,success
Without_total_time_minutes,"['total_title_diversity', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6733333333333333,0.03741657386773943,0.6722121527011408,0.03949680165879707,0.6608011073303266,0.7170022131791247,0.7224999999999999,0.04916666666666658,success
Without_productivity_points,"['total_title_diversity', 'total_time_minutes', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6733333333333333,0.04898979485566354,0.6713968057866009,0.04879946329573834,0.6605216459168071,0.7170022131791248,0.7183333333333333,0.04499999999999993,success
Without_total_cycles,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04876246279442595,0.6698337077437524,0.04743100914618995,0.6596078592204118,0.7147033626044121,0.7150000000000001,0.04500000000000004,success
Without_work_days,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04876246279442595,0.6698337077437524,0.04743100914618995,0.6596078592204118,0.7147033626044121,0.7150000000000001,0.04500000000000004,success
Without_avg_distance_km,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.69,0.04898979485566354,0.689252124000776,0.04471062528706952,0.6748514179965792,0.7372789795578402,0.72,0.030000000000000027,success
Without_pomokit_title_length,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6633333333333333,0.043969686527576365,0.6648387110528267,0.0405201692966133,0.6528484651706213,0.7095059613050618,0.7133333333333334,0.050000000000000044,success
Without_avg_time_minutes,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6766666666666666,0.0454606056566195,0.675906448741262,0.044966214926264896,0.6652495568947181,0.7193010637538374,0.7233333333333334,0.046666666666666745,success
Without_consistency_score,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.64,0.047842333648024406,0.6282506066728877,0.051753496067656786,0.6269147047078081,0.6626508174484187,0.6858333333333333,0.04583333333333328,success
Without_gamification_balance,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6533333333333333,0.050990195135927834,0.6573181976842751,0.0371085558002691,0.6413245687900859,0.7115126960329359,0.6975,0.04416666666666669,success
Without_pomokit_title_count,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04876246279442595,0.6698337077437524,0.04743100914618995,0.6596078592204118,0.7147033626044121,0.7150000000000001,0.04500000000000004,success
Without_strava_unique_words,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6733333333333333,0.04027681991198188,0.6727250297559326,0.03641299027815391,0.6608694050573846,0.71736036743533,0.7208333333333333,0.04749999999999999,success
Without_title_balance_ratio,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6399999999999999,0.06200358412579426,0.6301863781143201,0.054318868240137784,0.6199185696336331,0.6780585897527427,0.6941666666666666,0.054166666666666696,success
Without_strava_title_length,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6766666666666666,0.0442216638714053,0.6756877926363762,0.04328734831324993,0.6623061991908231,0.7197799909569025,0.7141666666666666,0.03749999999999998,success
Without_total_distance_km,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.6799999999999999,0.03858612300930073,0.6810458862694011,0.034354996329524747,0.6684874731354806,0.728704100330787,0.7191666666666667,0.039166666666666794,success
Without_pomokit_unique_words,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'strava_title_count', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7116666666666667,0.04166666666666663,success
Without_strava_title_count,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'achievement_rate', 'activity_points', 'activity_days']",19,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7208333333333333,0.050833333333333286,success
Without_achievement_rate,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'activity_points', 'activity_days']",19,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7191666666666667,0.04916666666666669,success
Without_activity_points,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_days']",19,0.6733333333333333,0.04784233364802438,0.6721069983531868,0.04778428099288617,0.6623733733113528,0.7164025130291998,0.72,0.046666666666666634,success
Without_activity_days,"['total_title_diversity', 'total_time_minutes', 'productivity_points', 'total_cycles', 'work_days', 'avg_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'gamification_balance', 'pomokit_title_count', 'strava_unique_words', 'title_balance_ratio', 'strava_title_length', 'total_distance_km', 'pomokit_unique_words', 'strava_title_count', 'achievement_rate', 'activity_points']",19,0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7208333333333333,0.050833333333333286,success
Optimal_Feature_Set,"['title_balance_ratio', 'consistency_score', 'gamification_balance', 'pomokit_title_length', 'activity_days', 'strava_title_count', 'total_cycles', 'total_title_diversity', 'pomokit_title_count', 'achievement_rate', 'pomokit_unique_words', 'work_days']",12,0.6466666666666667,0.07257180352359079,0.6561441917301388,0.0612024811968703,0.6368501607114468,0.7130524023702434,0.7,0.05333333333333323,success
