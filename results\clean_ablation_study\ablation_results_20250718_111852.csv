experiment_name,features_used,feature_count,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
Baseline_All_Features,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",18,0.5933333333333334,0.08537498983243798,0.6009553637983639,0.08480883567399895,0.6138408521162144,0.6202963714221321,0.6491666666666667,0.05583333333333329,success
Without_achievement_rate,"['strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333333,0.08137703743822466,0.6011990915887201,0.08142536241969418,0.6152689913414552,0.6221641875140861,0.6508333333333333,0.057499999999999996,success
Without_strava_title_count,"['achievement_rate', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.08537498983243798,0.6009553637983639,0.08480883567399895,0.6138408521162144,0.6202963714221321,0.6491666666666667,0.05583333333333329,success
Without_title_balance_ratio,"['achievement_rate', 'strava_title_count', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.6033333333333333,0.0819213715162967,0.6129853902498136,0.08286522656259804,0.6289555181660444,0.6341553172993335,0.6424999999999998,0.03916666666666657,success
Without_total_title_diversity,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.08537498983243798,0.6009553637983639,0.08480883567399895,0.6138408521162144,0.6202963714221321,0.65,0.05666666666666664,success
Without_productivity_points,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.59,0.08069145624606801,0.5981685210115211,0.08088441006994827,0.6117711558436196,0.6179975208474195,0.65,0.06000000000000005,success
Without_pomokit_title_count,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.6,0.08299933065325822,0.6089856479267255,0.0815945392284279,0.6215032380394699,0.6298201809459416,0.6483333333333333,0.04833333333333334,success
Without_avg_distance_km,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333333,0.07423685817106697,0.6015845621628698,0.07478681356131436,0.6161701835469952,0.6204648411088574,0.6383333333333333,0.04500000000000004,success
Without_gamification_balance,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.069602043392737,0.6035995464523639,0.0698050598147249,0.6176711371093522,0.6280072603754145,0.6491666666666667,0.05583333333333329,success
Without_work_days,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.6,0.08299933065325822,0.6089856479267255,0.0815945392284279,0.6215032380394699,0.6298201809459416,0.6483333333333333,0.04833333333333334,success
Without_pomokit_unique_words,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.08537498983243798,0.6009553637983639,0.08480883567399895,0.6138408521162144,0.6202963714221321,0.6491666666666667,0.05583333333333329,success
Without_strava_unique_words,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5966666666666666,0.0784573486395988,0.6052981541900497,0.07752294141182262,0.6180307223640558,0.6253033742232526,0.6466666666666667,0.050000000000000155,success
Without_avg_time_minutes,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5966666666666667,0.08259674462242579,0.604607493639173,0.08196019443984102,0.6180616795979115,0.6250582761840369,0.6425000000000001,0.04583333333333339,success
Without_activity_points,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5966666666666667,0.08589399151150083,0.6033802972919383,0.08583132184293041,0.616487365023597,0.6244630380887987,0.6533333333333334,0.056666666666666754,success
Without_consistency_score,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'total_time_minutes', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.069602043392737,0.6018450854764356,0.0667839907547802,0.6142549506317622,0.6145209923049679,0.6433333333333333,0.04999999999999993,success
Without_total_time_minutes,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'activity_days', 'total_cycles', 'total_distance_km']",17,0.6,0.08299933065325822,0.6080224136301579,0.08242185044786962,0.6217918383280703,0.627280498406259,0.6483333333333333,0.04833333333333334,success
Without_activity_days,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'total_cycles', 'total_distance_km']",17,0.5933333333333334,0.08537498983243798,0.6009553637983639,0.08480883567399895,0.6138408521162144,0.6202963714221321,0.6491666666666667,0.05583333333333329,success
Without_total_cycles,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_distance_km']",17,0.6,0.08299933065325822,0.6089856479267255,0.0815945392284279,0.6215032380394699,0.6298201809459416,0.6483333333333333,0.04833333333333334,success
Without_total_distance_km,"['achievement_rate', 'strava_title_count', 'title_balance_ratio', 'total_title_diversity', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'work_days', 'pomokit_unique_words', 'strava_unique_words', 'avg_time_minutes', 'activity_points', 'consistency_score', 'total_time_minutes', 'activity_days', 'total_cycles']",17,0.59,0.0771722460186015,0.5967212023282221,0.0757795004305033,0.6122884614044035,0.6174022827521812,0.6475,0.057499999999999996,success
Optimal_Feature_Set,"['total_distance_km', 'productivity_points', 'avg_distance_km', 'achievement_rate', 'strava_title_count', 'gamification_balance', 'pomokit_unique_words', 'activity_days', 'consistency_score', 'total_title_diversity']",10,0.59,0.06200358412579421,0.598199192603546,0.06205832390553683,0.6052935976008487,0.6392044173991436,0.6016666666666667,0.011666666666666714,success
