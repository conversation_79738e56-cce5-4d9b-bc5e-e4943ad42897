"""
Feature Contribution Analyzer
Sistem untuk menganalisis dan memvisualisasikan kontribusi fitur terhadap prediksi

FITUR UTAMA:
1. Individual Feature Impact Analysis
2. Feature Interaction Analysis  
3. Model Interpretability Visualizations
4. Feature Stability Analysis
5. Contribution Ranking dengan Multiple Metrics

VISUALISASI:
- Feature Importance Bar Charts
- SHAP Summary Plots
- Feature Correlation Heatmaps
- Model Performance Comparison
- Feature Stability Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# SHAP imports (with error handling)
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

logger = logging.getLogger(__name__)

class FeatureContributionAnalyzer:
    """
    Analyzer untuk kontribusi dan dampak fitur terhadap prediksi
    """
    
    def __init__(self, feature_selector_results: Dict):
        """
        Initialize dengan hasil dari ComprehensiveFeatureSelector
        
        Args:
            feature_selector_results: Hasil dari ComprehensiveFeatureSelector
        """
        self.results = feature_selector_results
        self.visualizations = {}
        
        logger.info("🎨 Feature Contribution Analyzer initialized")
    
    def analyze_individual_feature_impact(self) -> Dict:
        """Analisis dampak individual setiap fitur"""
        logger.info("🔍 Analyzing individual feature impact...")
        
        impact_analysis = {}
        
        # Dari hasil permutation importance
        if 'permutation_results' in self.results:
            for model_key, perm_result in self.results['permutation_results'].items():
                model_impacts = []
                
                for feature_data in perm_result['feature_importance']:
                    # Kategorisasi dampak berdasarkan importance score
                    importance = feature_data['importance']
                    
                    if importance > 0.05:
                        impact_level = "HIGH"
                        impact_description = "Fitur sangat penting untuk prediksi"
                    elif importance > 0.02:
                        impact_level = "MEDIUM"
                        impact_description = "Fitur cukup penting untuk prediksi"
                    elif importance > 0.01:
                        impact_level = "LOW"
                        impact_description = "Fitur memiliki dampak kecil"
                    else:
                        impact_level = "MINIMAL"
                        impact_description = "Fitur memiliki dampak minimal"
                    
                    model_impacts.append({
                        'feature': feature_data['feature'],
                        'importance_score': importance,
                        'impact_level': impact_level,
                        'impact_description': impact_description,
                        'rank': feature_data['rank']
                    })
                
                impact_analysis[model_key] = {
                    'model_name': perm_result['model_name'],
                    'feature_impacts': model_impacts
                }
        
        return impact_analysis
    
    def create_feature_importance_visualization(self, save_path: Optional[str] = None) -> str:
        """Buat visualisasi feature importance"""
        logger.info("📊 Creating feature importance visualization...")
        
        # Setup plot
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Feature Importance Analysis', fontsize=16, fontweight='bold')
        
        # 1. Consensus Ranking (Top 10)
        ax1 = axes[0, 0]
        if 'consensus_ranking' in self.results:
            top_features = self.results['consensus_ranking'][:10]
            features = [item['feature'] for item in top_features]
            scores = [item['consensus_score'] for item in top_features]
            
            bars = ax1.barh(range(len(features)), scores, color='skyblue')
            ax1.set_yticks(range(len(features)))
            ax1.set_yticklabels(features)
            ax1.set_xlabel('Consensus Score')
            ax1.set_title('Top 10 Features - Consensus Ranking')
            ax1.invert_yaxis()
            
            # Add value labels
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax1.text(width + 0.001, bar.get_y() + bar.get_height()/2, 
                        f'{width:.3f}', ha='left', va='center', fontsize=8)
        
        # 2. RFE Selection Frequency
        ax2 = axes[0, 1]
        if 'consensus_ranking' in self.results:
            top_features = self.results['consensus_ranking'][:10]
            features = [item['feature'] for item in top_features]
            rfe_scores = [item['rfe_score'] for item in top_features]
            
            bars = ax2.barh(range(len(features)), rfe_scores, color='lightcoral')
            ax2.set_yticks(range(len(features)))
            ax2.set_yticklabels(features)
            ax2.set_xlabel('RFE Selection Rate')
            ax2.set_title('RFE Selection Frequency')
            ax2.invert_yaxis()
            
            # Add value labels
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax2.text(width + 0.01, bar.get_y() + bar.get_height()/2, 
                        f'{width:.2f}', ha='left', va='center', fontsize=8)
        
        # 3. Model Performance by Feature Count
        ax3 = axes[1, 0]
        if 'model_performance' in self.results:
            feature_counts = []
            best_scores = []
            
            for n_features, perf_data in self.results['model_performance'].items():
                feature_counts.append(n_features)
                best_cv = max([s['cv_mean'] for s in perf_data['model_scores'].values()])
                best_scores.append(best_cv)
            
            ax3.plot(feature_counts, best_scores, marker='o', linewidth=2, markersize=8)
            ax3.set_xlabel('Number of Features')
            ax3.set_ylabel('Best CV Accuracy')
            ax3.set_title('Model Performance vs Feature Count')
            ax3.grid(True, alpha=0.3)
            
            # Mark optimal point
            if best_scores:
                max_idx = np.argmax(best_scores)
                ax3.scatter(feature_counts[max_idx], best_scores[max_idx], 
                           color='red', s=100, zorder=5)
                ax3.annotate(f'Optimal: {feature_counts[max_idx]} features', 
                           xy=(feature_counts[max_idx], best_scores[max_idx]),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # 4. Method Comparison (Average Ranks)
        ax4 = axes[1, 1]
        if 'consensus_ranking' in self.results:
            top_features = self.results['consensus_ranking'][:8]
            features = [item['feature'][:15] + '...' if len(item['feature']) > 15 else item['feature'] 
                       for item in top_features]
            
            perm_ranks = [item['perm_avg_rank'] for item in top_features]
            shap_ranks = [item['shap_avg_rank'] if item['shap_avg_rank'] < 100 else None 
                         for item in top_features]
            stat_ranks = [item['stat_rank'] for item in top_features]
            
            x = np.arange(len(features))
            width = 0.25
            
            ax4.bar(x - width, perm_ranks, width, label='Permutation', alpha=0.8)
            if any(r is not None for r in shap_ranks):
                shap_ranks_clean = [r if r is not None else 0 for r in shap_ranks]
                ax4.bar(x, shap_ranks_clean, width, label='SHAP', alpha=0.8)
            ax4.bar(x + width, stat_ranks, width, label='Statistical', alpha=0.8)
            
            ax4.set_xlabel('Features')
            ax4.set_ylabel('Average Rank')
            ax4.set_title('Method Comparison (Lower = Better)')
            ax4.set_xticks(x)
            ax4.set_xticklabels(features, rotation=45, ha='right')
            ax4.legend()
            ax4.invert_yaxis()
        
        plt.tight_layout()
        
        # Save plot
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"results/comprehensive_feature_analysis/feature_importance_visualization_{timestamp}.png"
        
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ Visualization saved: {save_path}")
        return save_path
    
    def create_feature_correlation_analysis(self, data: pd.DataFrame, save_path: Optional[str] = None) -> str:
        """Analisis korelasi antar fitur"""
        logger.info("🔗 Creating feature correlation analysis...")
        
        # Get top features from consensus ranking
        if 'consensus_ranking' in self.results:
            top_features = [item['feature'] for item in self.results['consensus_ranking'][:15]]
            
            # Filter data to top features only
            correlation_data = data[top_features]
            
            # Calculate correlation matrix
            corr_matrix = correlation_data.corr()
            
            # Create visualization
            plt.figure(figsize=(12, 10))
            
            # Create heatmap
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
            sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                       square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')
            
            plt.title('Feature Correlation Matrix (Top 15 Features)', fontsize=14, fontweight='bold')
            plt.tight_layout()
            
            # Save plot
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = f"results/comprehensive_feature_analysis/feature_correlation_{timestamp}.png"
            
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"✅ Correlation analysis saved: {save_path}")
            return save_path
        
        return ""
    
    def generate_contribution_summary(self) -> str:
        """Generate summary of feature contributions"""
        summary = []
        summary.append("🎯 FEATURE CONTRIBUTION SUMMARY")
        summary.append("=" * 50)
        summary.append("")
        
        # Individual impact analysis
        impact_analysis = self.analyze_individual_feature_impact()
        
        if impact_analysis:
            summary.append("📊 INDIVIDUAL FEATURE IMPACT:")
            
            # Aggregate impact across models
            feature_impacts = {}
            for model_key, model_data in impact_analysis.items():
                for feature_impact in model_data['feature_impacts']:
                    feature = feature_impact['feature']
                    if feature not in feature_impacts:
                        feature_impacts[feature] = []
                    feature_impacts[feature].append(feature_impact['impact_level'])
            
            # Determine overall impact for each feature
            for feature, impacts in feature_impacts.items():
                impact_counts = {level: impacts.count(level) for level in set(impacts)}
                most_common_impact = max(impact_counts, key=impact_counts.get)
                
                summary.append(f"   • {feature}: {most_common_impact} impact")
            
            summary.append("")
        
        # Top contributing features
        if 'consensus_ranking' in self.results:
            summary.append("🏆 TOP CONTRIBUTING FEATURES:")
            for i, item in enumerate(self.results['consensus_ranking'][:5]):
                summary.append(f"   {i+1}. {item['feature']}")
                summary.append(f"      Score: {item['consensus_score']:.4f}")
                summary.append(f"      Consistency: {'High' if item['rfe_score'] > 0.5 else 'Medium' if item['rfe_score'] > 0.2 else 'Low'}")
                summary.append("")
        
        return "\n".join(summary)
