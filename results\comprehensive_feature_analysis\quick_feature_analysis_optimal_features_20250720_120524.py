"""
Optimal Feature Set - Generated by Comprehensive Feature Selector
Generated: 2025-07-20 12:05:24
Expected Performance: 0.6458 accuracy
"""

# Optimal feature set (15 features)
OPTIMAL_FEATURES = ['total_cycles', 'pomokit_title_count', 'consistency_score', 'title_balance_ratio', 'total_distance_km', 'work_days', 'avg_distance_km', 'strava_unique_words', 'total_time_minutes', 'activity_points', 'strava_title_count', 'activity_days', 'achievement_rate', 'pomokit_unique_words', 'gamification_balance']

# Feature importance ranking (top 10)
TOP_FEATURES_RANKING = {
    "total_cycles": {"rank": 1, "score": 0.5990},
    "pomokit_title_count": {"rank": 2, "score": 0.5357},
    "consistency_score": {"rank": 3, "score": 0.4385},
    "title_balance_ratio": {"rank": 4, "score": 0.3972},
    "total_distance_km": {"rank": 5, "score": 0.3886},
    "work_days": {"rank": 6, "score": 0.3678},
    "avg_distance_km": {"rank": 7, "score": 0.3418},
    "strava_unique_words": {"rank": 8, "score": 0.2713},
    "total_time_minutes": {"rank": 9, "score": 0.2704},
    "activity_points": {"rank": 10, "score": 0.2082},
}
