algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,5,"['total_title_diversity', 'total_cycles', 'consistency_score', 'pomokit_title_count', 'work_days']",0.5633333333333332,0.07408703590297623,0.5711423523329918,0.06913856365818408,0.576076952946148,0.666729135432284,0.5775,0.014166666666666772,success
logistic_regression,Logistic Regression,10,"['total_title_diversity', 'total_cycles', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.6366666666666667,0.05312459150169741,0.6446530693169716,0.050517141143240865,0.6241330134542475,0.6972614883034673,0.6849999999999999,0.04833333333333323,success
logistic_regression,Logistic Regression,15,"['total_title_diversity', 'total_cycles', 'total_distance_km', 'pomokit_title_length', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.6733333333333333,0.03590109871423001,0.6734512427377599,0.03750121731781225,0.6620607660313542,0.7164025130291998,0.7191666666666666,0.04583333333333328,success
logistic_regression,Logistic Regression,20,"['total_title_diversity', 'strava_title_count', 'total_cycles', 'total_distance_km', 'strava_unique_words', 'pomokit_title_length', 'avg_time_minutes', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'activity_days', 'activity_points', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.67,0.04642796092394703,0.6694431088340094,0.04530387752889265,0.6596632283838166,0.7141036624544872,0.7208333333333333,0.050833333333333286,success
random_forest,Random Forest,5,"['strava_title_count', 'total_cycles', 'consistency_score', 'work_days', 'productivity_points']",0.9633333333333333,0.012472191289246475,0.9529914755951925,0.02086904622106374,0.9420036569017078,0.9675995335665502,0.9741666666666667,0.010833333333333472,success
random_forest,Random Forest,10,"['strava_title_count', 'total_cycles', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'activity_days', 'work_days', 'productivity_points', 'gamification_balance']",0.9633333333333333,0.012472191289246475,0.9529914755951925,0.02086904622106374,0.9420036569017078,0.9675995335665502,0.9941666666666666,0.03083333333333338,success
random_forest,Random Forest,15,"['strava_title_count', 'total_cycles', 'strava_unique_words', 'pomokit_title_length', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'activity_days', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.9666666666666666,0.018257418583505512,0.9549970353391728,0.028704373194431212,0.9442171136653895,0.9693076081007115,1.0,0.03333333333333344,success
random_forest,Random Forest,20,"['total_title_diversity', 'strava_title_count', 'total_cycles', 'total_distance_km', 'strava_unique_words', 'pomokit_title_length', 'avg_time_minutes', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'activity_days', 'activity_points', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.9666666666666666,0.018257418583505512,0.9530990099928717,0.03216792549808688,0.9479187574671446,0.9620826491516146,1.0,0.03333333333333344,success
gradient_boosting,Gradient Boosting,5,"['total_cycles', 'consistency_score', 'pomokit_title_count', 'work_days', 'productivity_points']",0.96,0.013333333333333336,0.9503796355364129,0.013386483816037745,0.9412589584328714,0.9653006829918374,0.9866666666666667,0.026666666666666727,success
gradient_boosting,Gradient Boosting,10,"['total_cycles', 'total_distance_km', 'pomokit_title_length', 'consistency_score', 'pomokit_title_count', 'total_time_minutes', 'work_days', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.9566666666666667,0.016996731711975938,0.9452693644992662,0.03072831367571378,0.9403500656225907,0.9541074700744865,1.0,0.043333333333333335,success
gradient_boosting,Gradient Boosting,15,"['total_cycles', 'total_distance_km', 'pomokit_title_length', 'avg_time_minutes', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'activity_days', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.9566666666666667,0.016996731711975938,0.9424580974471718,0.03191337198604688,0.9392654095379347,0.9480729873158659,1.0,0.043333333333333335,success
gradient_boosting,Gradient Boosting,20,"['total_title_diversity', 'strava_title_count', 'total_cycles', 'total_distance_km', 'strava_unique_words', 'pomokit_title_length', 'avg_time_minutes', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'activity_days', 'activity_points', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.9566666666666667,0.016996731711975938,0.9424580974471718,0.03191337198604688,0.9392654095379347,0.9480729873158659,1.0,0.043333333333333335,success
svm,Support Vector Machine,5,"['total_cycles', 'consistency_score', 'pomokit_title_count', 'work_days', 'productivity_points']",0.5266666666666666,0.022607766610417548,0.47896540722499215,0.04678422678129376,0.4352090957888059,0.6635015825420623,0.5591666666666667,0.032500000000000084,success
svm,Support Vector Machine,10,"['total_title_diversity', 'total_cycles', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.6466666666666666,0.04876246279442598,0.6441030463676182,0.05924252614586478,0.6460673989374179,0.745535565550558,0.6716666666666666,0.025000000000000022,success
svm,Support Vector Machine,15,"['total_title_diversity', 'total_cycles', 'total_distance_km', 'pomokit_title_length', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.6900000000000001,0.037416573867739396,0.6874903247042041,0.023317693774628133,0.6785977949365729,0.7474697175221914,0.7191666666666666,0.029166666666666563,success
svm,Support Vector Machine,20,"['total_title_diversity', 'strava_title_count', 'total_cycles', 'total_distance_km', 'strava_unique_words', 'pomokit_title_length', 'avg_time_minutes', 'achievement_rate', 'consistency_score', 'pomokit_title_count', 'strava_title_length', 'avg_distance_km', 'activity_days', 'activity_points', 'total_time_minutes', 'work_days', 'pomokit_unique_words', 'productivity_points', 'gamification_balance', 'title_balance_ratio']",0.6766666666666666,0.04784233364802442,0.6813029296201433,0.02813625136572808,0.6653521068595322,0.7417803003260274,0.7383333333333333,0.06166666666666665,success
