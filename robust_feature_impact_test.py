#!/usr/bin/env python3
"""
Robust Feature Impact Test
Test avg_distance_km impact dengan multiple random seeds untuk hasil yang lebih reliable
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import LabelEncoder
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def robust_feature_impact_test(n_trials=10):
    """Test feature impact dengan multiple random seeds"""
    
    print("🔬 ROBUST FEATURE IMPACT TEST")
    print("="*80)
    print(f"Testing avg_distance_km impact with {n_trials} different random seeds")
    print("="*80)
    
    # Load dataset
    df = pd.read_csv('dataset/processed/fatigue_risk_classified_dataset.csv')
    
    # Prepare data
    safe_features = [
        'achievement_rate', 'activity_days', 'activity_points', 
        'avg_distance_km', 'avg_time_minutes', 'consistency_score',
        'gamification_balance', 'pomokit_title_count', 'pomokit_title_length',
        'pomokit_unique_words', 'productivity_points', 'strava_title_count',
        'strava_title_length', 'strava_unique_words', 'title_balance_ratio',
        'total_cycles', 'total_distance_km', 'total_time_minutes',
        'total_title_diversity', 'work_days'
    ]
    
    X_all = df[safe_features]
    X_without = df[[f for f in safe_features if f != 'avg_distance_km']]
    y = df['fatigue_risk']
    
    # Encode target
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)
    
    print(f"📊 Dataset: {X_all.shape[0]} samples, {X_all.shape[1]} features")
    print(f"🎯 Target distribution: {pd.Series(y).value_counts().to_dict()}")
    
    # Test with multiple random seeds
    baseline_scores = []
    without_scores = []
    improvements = []
    
    print(f"\n🧪 RUNNING {n_trials} TRIALS:")
    print("-" * 60)
    
    for trial in range(n_trials):
        random_seed = 42 + trial  # Different seed each trial
        
        # Test baseline
        model = LogisticRegression(random_state=random_seed, max_iter=1000)
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_seed)
        
        baseline_cv = cross_val_score(model, X_all, y_encoded, cv=cv, scoring='accuracy')
        without_cv = cross_val_score(model, X_without, y_encoded, cv=cv, scoring='accuracy')
        
        baseline_mean = baseline_cv.mean()
        without_mean = without_cv.mean()
        improvement = without_mean - baseline_mean
        
        baseline_scores.append(baseline_mean)
        without_scores.append(without_mean)
        improvements.append(improvement)
        
        print(f"Trial {trial+1:2d}: Baseline={baseline_mean:.4f}, Without={without_mean:.4f}, Δ={improvement:+.4f}")
    
    # Calculate statistics
    baseline_avg = np.mean(baseline_scores)
    without_avg = np.mean(without_scores)
    improvement_avg = np.mean(improvements)
    improvement_std = np.std(improvements)
    
    # Count positive improvements
    positive_improvements = sum(1 for imp in improvements if imp > 0)
    negative_improvements = sum(1 for imp in improvements if imp < 0)
    
    print(f"\n📊 ROBUST RESULTS SUMMARY:")
    print("="*80)
    print(f"🎯 Average Baseline:        {baseline_avg:.4f} ({baseline_avg*100:.2f}%)")
    print(f"🎯 Average Without:         {without_avg:.4f} ({without_avg*100:.2f}%)")
    print(f"📈 Average Improvement:     {improvement_avg:+.4f} ({improvement_avg*100:+.2f}%)")
    print(f"📊 Improvement Std Dev:     {improvement_std:.4f}")
    print(f"✅ Positive Improvements:   {positive_improvements}/{n_trials} ({positive_improvements/n_trials*100:.1f}%)")
    print(f"❌ Negative Improvements:   {negative_improvements}/{n_trials} ({negative_improvements/n_trials*100:.1f}%)")
    
    # Statistical significance
    from scipy import stats
    t_stat, p_value = stats.ttest_1samp(improvements, 0)
    
    print(f"\n🔬 STATISTICAL ANALYSIS:")
    print(f"   • T-statistic: {t_stat:.4f}")
    print(f"   • P-value: {p_value:.4f}")
    print(f"   • Significant (p < 0.05): {'✅ YES' if p_value < 0.05 else '❌ NO'}")
    
    # Confidence interval
    confidence_interval = stats.t.interval(0.95, len(improvements)-1, 
                                         loc=improvement_avg, 
                                         scale=stats.sem(improvements))
    
    print(f"   • 95% Confidence Interval: [{confidence_interval[0]:+.4f}, {confidence_interval[1]:+.4f}]")
    
    # Final recommendation
    print(f"\n🎯 ROBUST RECOMMENDATION:")
    print("="*80)
    
    if improvement_avg > 0.01 and positive_improvements >= n_trials * 0.7:
        print(f"✅ STRONG RECOMMENDATION: REMOVE avg_distance_km")
        print(f"   • Consistently improves accuracy in {positive_improvements}/{n_trials} trials")
        print(f"   • Average improvement: {improvement_avg*100:+.2f}%")
        print(f"   • Expected accuracy: ~{without_avg*100:.1f}%")
        
    elif improvement_avg > 0 and positive_improvements > negative_improvements:
        print(f"✅ WEAK RECOMMENDATION: REMOVE avg_distance_km")
        print(f"   • More often improves than hurts ({positive_improvements} vs {negative_improvements})")
        print(f"   • Average improvement: {improvement_avg*100:+.2f}%")
        print(f"   • But results are inconsistent")
        
    elif abs(improvement_avg) < 0.005:
        print(f"⚖️ NEUTRAL: KEEP OR REMOVE avg_distance_km")
        print(f"   • No significant impact either way")
        print(f"   • Average change: {improvement_avg*100:+.2f}%")
        print(f"   • Decision can be based on other factors (simplicity, etc.)")
        
    else:
        print(f"❌ RECOMMENDATION: KEEP avg_distance_km")
        print(f"   • More often hurts than helps ({negative_improvements} vs {positive_improvements})")
        print(f"   • Average change: {improvement_avg*100:+.2f}%")
        print(f"   • Removing it decreases accuracy")
    
    return {
        'baseline_avg': baseline_avg,
        'without_avg': without_avg,
        'improvement_avg': improvement_avg,
        'improvement_std': improvement_std,
        'positive_ratio': positive_improvements / n_trials,
        'p_value': p_value,
        'confidence_interval': confidence_interval
    }

if __name__ == "__main__":
    robust_feature_impact_test(n_trials=20)  # Test dengan 20 random seeds
