"""
Comprehensive Feature Selector
Sistem terpadu untuk seleksi fitur dan analisis kontribusi yang menggabungkan:
- SHAP Analysis (Shapley values)
- RFE (Recursive Feature Elimination) 
- Permutation Importance
- Statistical Analysis
- Feature Stability Analysis

TUJUAN UTAMA:
1. Memberikan ranking fitur yang konsisten dan dapat dipercaya
2. Menganalisis kontribusi setiap fitur terhadap prediksi
3. Memberikan rekomendasi fitur optimal untuk model
4. Menyediakan interpretabilitas yang mudah dipahami

KEUNGGULAN:
- Menggabungkan multiple metode untuk hasil yang robust
- Analisis konsistensi antar metode
- Visualisasi yang jelas dan informatif
- Rekomendasi yang actionable
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML imports
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.feature_selection import RFE, SelectKBest, f_classif
from sklearn.inspection import permutation_importance
from sklearn.metrics import accuracy_score, f1_score, classification_report

# SHAP imports (with error handling)
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("⚠️ SHAP not available. SHAP analysis will be skipped.")

logger = logging.getLogger(__name__)

class ComprehensiveFeatureSelector:
    """
    Sistem komprehensif untuk seleksi fitur dan analisis kontribusi
    """
    
    def __init__(self, random_state: int = 42):
        """
        Initialize comprehensive feature selector
        
        Args:
            random_state: Random state for reproducibility
        """
        self.random_state = random_state
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.label_encoder = LabelEncoder()
        
        # Results storage
        self.results = {
            'rfe_results': {},
            'permutation_results': {},
            'shap_results': {},
            'statistical_results': {},
            'consensus_ranking': {},
            'model_performance': {},
            'feature_stability': {}
        }
        
        # Models to test
        self.models = {
            'logistic_regression': {
                'model': LogisticRegression(random_state=random_state, max_iter=1000),
                'name': 'Logistic Regression'
            },
            'random_forest': {
                'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
                'name': 'Random Forest'
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
                'name': 'Gradient Boosting'
            }
        }
        
        logger.info("🔍 Comprehensive Feature Selector initialized")
        logger.info(f"   • SHAP Available: {'Yes' if SHAP_AVAILABLE else 'No'}")
        logger.info(f"   • Models: {len(self.models)}")
    
    def load_data(self, data_path: str, target_column: str) -> None:
        """Load and prepare data for analysis"""
        logger.info(f"Loading data from: {data_path}")
        
        self.data = pd.read_csv(data_path)
        logger.info(f"✅ Data loaded: {self.data.shape}")
        
        # Prepare features and target
        self.X = self.data.drop(columns=[target_column])
        self.y = self.data[target_column]
        self.feature_names = list(self.X.columns)
        
        # Encode target if needed
        if self.y.dtype == 'object':
            self.y = self.label_encoder.fit_transform(self.y)
            logger.info(f"✅ Target encoded: {len(self.label_encoder.classes_)} classes")
        
        # Handle missing values
        if self.X.isnull().sum().sum() > 0:
            logger.warning("⚠️ Missing values detected, filling with median/mode")
            for col in self.X.columns:
                if self.X[col].dtype in ['int64', 'float64']:
                    self.X[col].fillna(self.X[col].median(), inplace=True)
                else:
                    self.X[col].fillna(self.X[col].mode()[0], inplace=True)
        
        logger.info(f"✅ Data prepared: {len(self.feature_names)} features, {len(self.X)} samples")
        logger.info(f"   • Target distribution: {dict(zip(*np.unique(self.y, return_counts=True)))}")
    
    def run_rfe_analysis(self) -> Dict:
        """Run Recursive Feature Elimination analysis"""
        logger.info("🔄 Running RFE Analysis...")
        
        rfe_results = {}
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.2, random_state=self.random_state, stratify=self.y
        )
        
        for model_key, model_info in self.models.items():
            logger.info(f"   • RFE with {model_info['name']}...")
            
            model = model_info['model']
            
            # Test different numbers of features
            feature_counts = [5, 10, 15, min(20, len(self.feature_names))]
            best_score = 0
            best_features = None
            best_count = 0
            
            for n_features in feature_counts:
                if n_features > len(self.feature_names):
                    continue
                    
                rfe = RFE(estimator=model, n_features_to_select=n_features)
                rfe.fit(X_train, y_train)
                
                # Evaluate
                y_pred = rfe.predict(X_test)
                score = accuracy_score(y_test, y_pred)
                
                if score > best_score:
                    best_score = score
                    best_features = [self.feature_names[i] for i in range(len(self.feature_names)) if rfe.support_[i]]
                    best_count = n_features
            
            rfe_results[model_key] = {
                'best_features': best_features,
                'best_score': best_score,
                'best_count': best_count,
                'model_name': model_info['name']
            }
            
            logger.info(f"     ✅ Best: {best_count} features, Score: {best_score:.4f}")
        
        self.results['rfe_results'] = rfe_results
        return rfe_results
    
    def run_permutation_analysis(self) -> Dict:
        """Run Permutation Importance analysis"""
        logger.info("🔄 Running Permutation Importance Analysis...")
        
        permutation_results = {}
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.2, random_state=self.random_state, stratify=self.y
        )
        
        for model_key, model_info in self.models.items():
            logger.info(f"   • Permutation with {model_info['name']}...")
            
            model = model_info['model']
            model.fit(X_train, y_train)
            
            # Calculate permutation importance
            perm_importance = permutation_importance(
                model, X_test, y_test, n_repeats=10, random_state=self.random_state
            )
            
            # Create feature importance ranking
            feature_importance = []
            for i, feature in enumerate(self.feature_names):
                feature_importance.append({
                    'feature': feature,
                    'importance': perm_importance.importances_mean[i],
                    'std': perm_importance.importances_std[i],
                    'rank': 0  # Will be filled later
                })
            
            # Sort by importance and assign ranks
            feature_importance.sort(key=lambda x: x['importance'], reverse=True)
            for i, item in enumerate(feature_importance):
                item['rank'] = i + 1
            
            permutation_results[model_key] = {
                'feature_importance': feature_importance,
                'model_name': model_info['name'],
                'baseline_score': accuracy_score(y_test, model.predict(X_test))
            }
            
            logger.info(f"     ✅ Top feature: {feature_importance[0]['feature']} ({feature_importance[0]['importance']:.4f})")
        
        self.results['permutation_results'] = permutation_results
        return permutation_results
    
    def run_statistical_analysis(self) -> Dict:
        """Run statistical feature selection analysis"""
        logger.info("🔄 Running Statistical Analysis...")
        
        # ANOVA F-test
        selector = SelectKBest(score_func=f_classif, k='all')
        selector.fit(self.X, self.y)
        
        statistical_results = []
        for i, feature in enumerate(self.feature_names):
            statistical_results.append({
                'feature': feature,
                'f_score': selector.scores_[i],
                'p_value': selector.pvalues_[i],
                'rank': 0  # Will be filled later
            })
        
        # Sort by F-score and assign ranks
        statistical_results.sort(key=lambda x: x['f_score'], reverse=True)
        for i, item in enumerate(statistical_results):
            item['rank'] = i + 1
        
        self.results['statistical_results'] = statistical_results
        logger.info(f"✅ Statistical analysis completed. Top feature: {statistical_results[0]['feature']}")

        return statistical_results

    def run_shap_analysis(self) -> Dict:
        """Run SHAP analysis if available"""
        if not SHAP_AVAILABLE:
            logger.warning("⚠️ SHAP not available, skipping SHAP analysis")
            return {}

        logger.info("🔄 Running SHAP Analysis...")

        shap_results = {}
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.2, random_state=self.random_state, stratify=self.y
        )

        for model_key, model_info in self.models.items():
            logger.info(f"   • SHAP with {model_info['name']}...")

            try:
                model = model_info['model']
                model.fit(X_train, y_train)

                # Create SHAP explainer based on model type
                if model_key == 'random_forest':
                    explainer = shap.TreeExplainer(model)
                    shap_values = explainer.shap_values(X_test[:50])  # Sample for efficiency
                    if isinstance(shap_values, list):
                        shap_values = shap_values[1]  # For binary classification
                elif model_key == 'logistic_regression':
                    explainer = shap.LinearExplainer(model, X_train)
                    shap_values = explainer.shap_values(X_test[:50])
                else:
                    # Use KernelExplainer for other models
                    explainer = shap.KernelExplainer(model.predict_proba, X_train[:50])
                    shap_values = explainer.shap_values(X_test[:50])
                    if isinstance(shap_values, list):
                        shap_values = shap_values[1]

                # Calculate mean absolute SHAP values for feature importance
                mean_shap_values = np.mean(np.abs(shap_values), axis=0)

                feature_importance = []
                for i, feature in enumerate(self.feature_names):
                    feature_importance.append({
                        'feature': feature,
                        'shap_importance': mean_shap_values[i],
                        'rank': 0  # Will be filled later
                    })

                # Sort by SHAP importance and assign ranks
                feature_importance.sort(key=lambda x: x['shap_importance'], reverse=True)
                for i, item in enumerate(feature_importance):
                    item['rank'] = i + 1

                shap_results[model_key] = {
                    'feature_importance': feature_importance,
                    'model_name': model_info['name'],
                    'shap_values': shap_values,
                    'test_data': X_test[:50]
                }

                logger.info(f"     ✅ Top feature: {feature_importance[0]['feature']} ({feature_importance[0]['shap_importance']:.4f})")

            except Exception as e:
                logger.error(f"     ❌ SHAP failed for {model_info['name']}: {str(e)}")
                continue

        self.results['shap_results'] = shap_results
        return shap_results

    def create_consensus_ranking(self) -> Dict:
        """Create consensus ranking from all methods"""
        logger.info("🔄 Creating Consensus Ranking...")

        # Collect all rankings
        all_rankings = {}

        # RFE rankings (based on selection frequency)
        rfe_feature_counts = {}
        for model_key, result in self.results['rfe_results'].items():
            for feature in result['best_features']:
                rfe_feature_counts[feature] = rfe_feature_counts.get(feature, 0) + 1

        # Permutation rankings
        perm_rankings = {}
        for model_key, result in self.results['permutation_results'].items():
            for item in result['feature_importance']:
                if item['feature'] not in perm_rankings:
                    perm_rankings[item['feature']] = []
                perm_rankings[item['feature']].append(item['rank'])

        # SHAP rankings
        shap_rankings = {}
        if self.results['shap_results']:
            for model_key, result in self.results['shap_results'].items():
                for item in result['feature_importance']:
                    if item['feature'] not in shap_rankings:
                        shap_rankings[item['feature']] = []
                    shap_rankings[item['feature']].append(item['rank'])

        # Statistical rankings
        stat_rankings = {item['feature']: item['rank'] for item in self.results['statistical_results']}

        # Calculate consensus score for each feature
        consensus_scores = []
        for feature in self.feature_names:
            scores = []

            # RFE score (selection frequency)
            rfe_score = rfe_feature_counts.get(feature, 0) / len(self.results['rfe_results'])
            scores.append(rfe_score)

            # Permutation score (inverse of average rank)
            if feature in perm_rankings:
                avg_perm_rank = np.mean(perm_rankings[feature])
                perm_score = 1 / avg_perm_rank
                scores.append(perm_score)

            # SHAP score (inverse of average rank)
            if feature in shap_rankings:
                avg_shap_rank = np.mean(shap_rankings[feature])
                shap_score = 1 / avg_shap_rank
                scores.append(shap_score)

            # Statistical score (inverse of rank)
            stat_score = 1 / stat_rankings[feature]
            scores.append(stat_score)

            # Calculate weighted consensus score
            consensus_score = np.mean(scores)

            consensus_scores.append({
                'feature': feature,
                'consensus_score': consensus_score,
                'rfe_score': rfe_score,
                'perm_avg_rank': np.mean(perm_rankings.get(feature, [len(self.feature_names)])),
                'shap_avg_rank': np.mean(shap_rankings.get(feature, [len(self.feature_names)])),
                'stat_rank': stat_rankings[feature],
                'rank': 0  # Will be filled later
            })

        # Sort by consensus score and assign final ranks
        consensus_scores.sort(key=lambda x: x['consensus_score'], reverse=True)
        for i, item in enumerate(consensus_scores):
            item['rank'] = i + 1

        self.results['consensus_ranking'] = consensus_scores
        logger.info(f"✅ Consensus ranking created. Top feature: {consensus_scores[0]['feature']}")

        return consensus_scores

    def evaluate_model_performance(self) -> Dict:
        """Evaluate model performance with different feature sets"""
        logger.info("🔄 Evaluating Model Performance...")

        performance_results = {}

        # Test with different numbers of top features
        feature_counts = [5, 10, 15, len(self.feature_names)]
        consensus_features = [item['feature'] for item in self.results['consensus_ranking']]

        for n_features in feature_counts:
            if n_features > len(consensus_features):
                continue

            selected_features = consensus_features[:n_features]
            X_selected = self.X[selected_features]

            X_train, X_test, y_train, y_test = train_test_split(
                X_selected, self.y, test_size=0.2, random_state=self.random_state, stratify=self.y
            )

            model_scores = {}
            for model_key, model_info in self.models.items():
                model = model_info['model']

                # Cross-validation
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')

                # Test set performance
                model.fit(X_train, y_train)
                test_score = accuracy_score(y_test, model.predict(X_test))

                model_scores[model_key] = {
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'test_score': test_score,
                    'model_name': model_info['name']
                }

            performance_results[n_features] = {
                'features': selected_features,
                'model_scores': model_scores
            }

            logger.info(f"   • {n_features} features: Best CV = {max([s['cv_mean'] for s in model_scores.values()]):.4f}")

        self.results['model_performance'] = performance_results
        return performance_results

    def run_complete_analysis(self) -> Dict:
        """Run complete feature selection and contribution analysis"""
        logger.info("🚀 Starting Complete Feature Selection Analysis...")

        # Run all analysis methods
        logger.info("Phase 1: RFE Analysis")
        self.run_rfe_analysis()

        logger.info("Phase 2: Permutation Importance Analysis")
        self.run_permutation_analysis()

        logger.info("Phase 3: Statistical Analysis")
        self.run_statistical_analysis()

        logger.info("Phase 4: SHAP Analysis")
        self.run_shap_analysis()

        logger.info("Phase 5: Consensus Ranking")
        self.create_consensus_ranking()

        logger.info("Phase 6: Model Performance Evaluation")
        self.evaluate_model_performance()

        logger.info("✅ Complete analysis finished!")
        return self.results

    def generate_feature_contribution_report(self) -> str:
        """Generate comprehensive feature contribution report"""
        report = []
        report.append("=" * 80)
        report.append("🔍 COMPREHENSIVE FEATURE SELECTION & CONTRIBUTION ANALYSIS")
        report.append("=" * 80)
        report.append("")

        # Dataset info
        report.append("📋 DATASET INFORMATION:")
        report.append(f"   • Total Features: {len(self.feature_names)}")
        report.append(f"   • Total Samples: {len(self.X)}")
        report.append(f"   • Target Classes: {len(np.unique(self.y))}")
        report.append("")

        # Top features from consensus ranking
        report.append("🏆 TOP 10 FEATURES (CONSENSUS RANKING):")
        for i, item in enumerate(self.results['consensus_ranking'][:10]):
            report.append(f"   {i+1:2d}. {item['feature']}")
            report.append(f"       • Consensus Score: {item['consensus_score']:.4f}")
            report.append(f"       • RFE Selection Rate: {item['rfe_score']:.2f}")
            report.append(f"       • Avg Permutation Rank: {item['perm_avg_rank']:.1f}")
            if item['shap_avg_rank'] < len(self.feature_names):
                report.append(f"       • Avg SHAP Rank: {item['shap_avg_rank']:.1f}")
            report.append(f"       • Statistical Rank: {item['stat_rank']}")
            report.append("")

        # Model performance with different feature counts
        report.append("📊 MODEL PERFORMANCE BY FEATURE COUNT:")
        for n_features, perf_data in self.results['model_performance'].items():
            report.append(f"   🔹 {n_features} Features:")
            best_model = max(perf_data['model_scores'].items(), key=lambda x: x[1]['cv_mean'])
            report.append(f"      • Best Model: {best_model[1]['model_name']}")
            report.append(f"      • CV Accuracy: {best_model[1]['cv_mean']:.4f} ± {best_model[1]['cv_std']:.4f}")
            report.append(f"      • Test Accuracy: {best_model[1]['test_score']:.4f}")
            report.append("")

        # Method-specific insights
        report.append("🔍 METHOD-SPECIFIC INSIGHTS:")

        # RFE insights
        report.append("   📈 RFE Analysis:")
        for model_key, result in self.results['rfe_results'].items():
            report.append(f"      • {result['model_name']}: {result['best_count']} features, Score: {result['best_score']:.4f}")
        report.append("")

        # Permutation insights
        report.append("   🎯 Permutation Importance:")
        for model_key, result in self.results['permutation_results'].items():
            top_feature = result['feature_importance'][0]
            report.append(f"      • {result['model_name']}: Top = {top_feature['feature']} ({top_feature['importance']:.4f})")
        report.append("")

        # SHAP insights
        if self.results['shap_results']:
            report.append("   🧠 SHAP Analysis:")
            for model_key, result in self.results['shap_results'].items():
                top_feature = result['feature_importance'][0]
                report.append(f"      • {result['model_name']}: Top = {top_feature['feature']} ({top_feature['shap_importance']:.4f})")
            report.append("")

        # Recommendations
        report.append("💡 RECOMMENDATIONS:")

        # Find optimal feature count
        best_performance = 0
        best_count = 0
        for n_features, perf_data in self.results['model_performance'].items():
            best_cv = max([s['cv_mean'] for s in perf_data['model_scores'].values()])
            if best_cv > best_performance:
                best_performance = best_cv
                best_count = n_features

        report.append(f"   ✅ Optimal Feature Count: {best_count} features")
        report.append(f"   ✅ Expected Performance: {best_performance:.4f} accuracy")
        report.append("")

        # Top recommended features
        top_features = [item['feature'] for item in self.results['consensus_ranking'][:best_count]]
        report.append(f"   🎯 Recommended Feature Set ({best_count} features):")
        for i, feature in enumerate(top_features):
            report.append(f"      {i+1:2d}. {feature}")
        report.append("")

        report.append("=" * 80)

        return "\n".join(report)

    def save_results(self, prefix: str = "comprehensive_feature_analysis") -> Tuple[str, str]:
        """Save analysis results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create results directory
        results_dir = Path("results/comprehensive_feature_analysis")
        results_dir.mkdir(parents=True, exist_ok=True)

        # Save detailed results (CSV)
        results_file = results_dir / f"{prefix}_results_{timestamp}.csv"

        # Prepare results DataFrame
        results_data = []
        for item in self.results['consensus_ranking']:
            results_data.append({
                'feature': item['feature'],
                'consensus_rank': item['rank'],
                'consensus_score': item['consensus_score'],
                'rfe_selection_rate': item['rfe_score'],
                'avg_permutation_rank': item['perm_avg_rank'],
                'avg_shap_rank': item['shap_avg_rank'],
                'statistical_rank': item['stat_rank']
            })

        results_df = pd.DataFrame(results_data)
        results_df.to_csv(results_file, index=False)

        # Save report (TXT)
        report_file = results_dir / f"{prefix}_report_{timestamp}.txt"
        report_content = self.generate_feature_contribution_report()

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Save optimal features as Python code
        code_file = results_dir / f"{prefix}_optimal_features_{timestamp}.py"

        # Find optimal feature count
        best_performance = 0
        best_count = 0
        for n_features, perf_data in self.results['model_performance'].items():
            best_cv = max([s['cv_mean'] for s in perf_data['model_scores'].values()])
            if best_cv > best_performance:
                best_performance = best_cv
                best_count = n_features

        optimal_features = [item['feature'] for item in self.results['consensus_ranking'][:best_count]]

        code_content = f'''"""
Optimal Feature Set - Generated by Comprehensive Feature Selector
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Expected Performance: {best_performance:.4f} accuracy
"""

# Optimal feature set ({best_count} features)
OPTIMAL_FEATURES = {optimal_features}

# Feature importance ranking (top 10)
TOP_FEATURES_RANKING = {{
'''

        for i, item in enumerate(self.results['consensus_ranking'][:10]):
            code_content += f'    "{item["feature"]}": {{"rank": {item["rank"]}, "score": {item["consensus_score"]:.4f}}},\n'

        code_content += "}\n"

        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(code_content)

        logger.info(f"✅ Results saved:")
        logger.info(f"   • Results: {results_file}")
        logger.info(f"   • Report: {report_file}")
        logger.info(f"   • Code: {code_file}")

        return str(results_file), str(report_file)


def main():
    """Main function for testing"""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Test with sample data
    data_path = "dataset/processed/safe_ml_title_only_dataset.csv"
    target_column = "title_fatigue_risk"

    try:
        selector = ComprehensiveFeatureSelector(random_state=42)
        selector.load_data(data_path, target_column)

        # Run complete analysis
        results = selector.run_complete_analysis()

        # Save results
        results_file, report_file = selector.save_results("comprehensive_analysis")

        print("🎉 Comprehensive Feature Analysis Completed!")
        print(f"📊 Results: {results_file}")
        print(f"📋 Report: {report_file}")

        # Print top 5 features
        print("\n🏆 Top 5 Features:")
        for i, item in enumerate(results['consensus_ranking'][:5]):
            print(f"   {i+1}. {item['feature']} (Score: {item['consensus_score']:.4f})")

    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
