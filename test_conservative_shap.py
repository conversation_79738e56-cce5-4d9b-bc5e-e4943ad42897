import sys
sys.path.append('src')
from simple_shap_analysis import SimpleSHAPAnalysis
import logging
logging.basicConfig(level=logging.INFO)

# Test on conservative dataset
analyzer = SimpleSHAPAnalysis(
    data_path='dataset/processed/conservative_safe_fatigue_dataset.csv',
    target_column='external_fatigue_risk',
    random_state=42
)

results = analyzer.run_analysis()
results_file, report_file = analyzer.save_results(results, prefix='conservative')

print('Conservative Dataset Analysis Completed!')
print(f'Results: {results_file}')
print(f'Report: {report_file}')

# Print top features
if 'feature_importance' in results:
    for model_name, importance_list in results['feature_importance'].items():
        print(f'\nTop 5 features for {model_name.replace("_", " ").title()}:')
        for item in importance_list[:5]:
            print(f'   {item["rank"]}. {item["feature"]}: {item["importance"]:.4f}')
