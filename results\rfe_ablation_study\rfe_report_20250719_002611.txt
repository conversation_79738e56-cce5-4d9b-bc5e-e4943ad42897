================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 18
   • Total Samples: 300

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [4, 5, 9, 10, 13, 15, 18, 20, 25, 30]
   • Total Experiments: 28

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: Random Forest
   • Features Used: 18
   • Accuracy: 0.6900 ± 0.0750
   • F1-Score: 0.6113
   • ⚠️ Overfitting Risk: 0.3100 (HIGH - Train-Test gap > 10%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 13
     - Accuracy: 0.6667 ± 0.0506
     - F1-Score: 0.6478
     - ⚠️ Overfitting: 0.0608 (MODERATE)
   • Random Forest:
     - Features: 18
     - Accuracy: 0.6900 ± 0.0750
     - F1-Score: 0.6113
     - ⚠️ Overfitting: 0.3100 (HIGH)
   • Gradient Boosting:
     - Features: 10
     - Accuracy: 0.6767 ± 0.0429
     - F1-Score: 0.6427
     - ⚠️ Overfitting: 0.3225 (HIGH)
   • Support Vector Machine:
     - Features: 9
     - Accuracy: 0.6633 ± 0.0499
     - F1-Score: 0.6369
     - ✅ Overfitting: 0.0450 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. pomokit_unique_words: 27 times (96.4%)
    2. title_balance_ratio: 26 times (92.9%)
    3. achievement_rate: 25 times (89.3%)
    4. work_days: 24 times (85.7%)
    5. total_cycles: 20 times (71.4%)
    6. total_distance_km: 20 times (71.4%)
    7. avg_distance_km: 20 times (71.4%)
    8. pomokit_title_count: 18 times (64.3%)
    9. productivity_points: 16 times (57.1%)
   10. total_time_minutes: 16 times (57.1%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. total_title_diversity: 10/10 top results
    2. achievement_rate: 10/10 top results
    3. pomokit_unique_words: 10/10 top results
    4. title_balance_ratio: 10/10 top results
    5. total_distance_km: 8/10 top results
    6. total_time_minutes: 8/10 top results
    7. avg_distance_km: 8/10 top results
    8. avg_time_minutes: 7/10 top results
    9. pomokit_title_count: 7/10 top results
   10. total_cycles: 7/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.3100
   • ⚠️ HIGH OVERFITTING RISK: Train-Test gap > 10%
   • Recommendation: Consider regularization or more data

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.0608 (MODERATE)
     ⚠️ Random Forest: 0.3100 (HIGH)
     ⚠️ Gradient Boosting: 0.3225 (HIGH)
     ✅ Support Vector Machine: 0.0450 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: Random Forest
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 18
   • Baseline Accuracy: 0.6900

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ avg_distance_km:
         - Baseline: 0.6900 | Without: 0.7267
         - Impact: -0.0367 (-5.31%)
         - MINIMAL - Negligible performance impact
      2. ▫️ productivity_points:
         - Baseline: 0.6900 | Without: 0.6700
         - Impact: 0.0200 (2.90%)
         - LOW - Minor performance impact
      3. ⚪ total_cycles:
         - Baseline: 0.6900 | Without: 0.7100
         - Impact: -0.0200 (-2.90%)
         - MINIMAL - Negligible performance impact
      4. ⚪ pomokit_unique_words:
         - Baseline: 0.6900 | Without: 0.7100
         - Impact: -0.0200 (-2.90%)
         - MINIMAL - Negligible performance impact
      5. ▫️ title_balance_ratio:
         - Baseline: 0.6900 | Without: 0.6733
         - Impact: 0.0167 (2.42%)
         - LOW - Minor performance impact
      6. ⚪ total_distance_km:
         - Baseline: 0.6900 | Without: 0.7067
         - Impact: -0.0167 (-2.42%)
         - MINIMAL - Negligible performance impact
      7. ⚪ work_days:
         - Baseline: 0.6900 | Without: 0.7033
         - Impact: -0.0133 (-1.93%)
         - MINIMAL - Negligible performance impact
      8. ▫️ total_time_minutes:
         - Baseline: 0.6900 | Without: 0.6800
         - Impact: 0.0100 (1.45%)
         - LOW - Minor performance impact
      9. ⚪ gamification_balance:
         - Baseline: 0.6900 | Without: 0.7000
         - Impact: -0.0100 (-1.45%)
         - MINIMAL - Negligible performance impact
     10. ⚪ consistency_score:
         - Baseline: 0.6900 | Without: 0.7000
         - Impact: -0.0100 (-1.45%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • avg_distance_km: 5.33% (score: 0.0120 ±0.0040)
     • productivity_points: 1.48% (score: 0.0033 ±0.0000)
     • total_cycles: 0.00% (score: 0.0000 ±0.0000)
     • pomokit_unique_words: 21.33% (score: 0.0480 ±0.0048)
     • title_balance_ratio: 13.33% (score: 0.0300 ±0.0080)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: Random Forest
   • Number of Features: 18
   • Selected Features:
      1. achievement_rate
      2. activity_days
      3. activity_points
      4. avg_distance_km
      5. avg_time_minutes
      6. consistency_score
      7. gamification_balance
      8. pomokit_title_count
      9. pomokit_unique_words
     10. productivity_points
     11. strava_title_count
     12. strava_unique_words
     13. title_balance_ratio
     14. total_cycles
     15. total_distance_km
     16. total_time_minutes
     17. total_title_diversity
     18. work_days
================================================================================