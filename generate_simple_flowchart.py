#!/usr/bin/env python3
"""
Script untuk generate gambar alur metodologi yang lebih sederhana
tanpa emoji untuk menghindari masalah font
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_simple_methodology_flowchart():
    """Create simple methodology flowchart without emojis"""
    
    # Create figure
    fig, ax = plt.subplots(1, 1, figsize=(14, 18))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 20)
    ax.axis('off')
    
    # Define colors
    colors = {
        'data_collection': '#E3F2FD',      # Light Blue
        'critical_phase': '#FFEBEE',       # Light Red
        'valid_process': '#E8F5E8',        # Light Green
        'invalid_process': '#FFCDD2',      # Light Red
        'best_performance': '#C8E6C9',     # Green
        'production': '#DCEDC8'            # Light Green
    }
    
    # Title
    title_box = FancyBboxPatch((1, 18.5), 8, 1, 
                               boxstyle="round,pad=0.2", 
                               facecolor='lightgray', 
                               edgecolor='black', linewidth=2)
    ax.add_patch(title_box)
    ax.text(5, 19, 'METODOLOGI PENELITIAN\nSequential Process & Data Leakage Prevention', 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    # Phase 1: Data Collection
    phase1_box = FancyBboxPatch((2, 16.5), 6, 1.5, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['data_collection'], 
                                edgecolor='blue', linewidth=2)
    ax.add_patch(phase1_box)
    ax.text(5, 17.25, 'PHASE 1: DATA COLLECTION & PREPROCESSING\n\nStrava API (Cardiovascular) + Pomokit API (Productivity)\nData Integration & Basic Feature Engineering', 
            ha='center', va='center', fontsize=11, fontweight='bold')
    
    # Arrow 1
    ax.arrow(5, 16.3, 0, -0.8, head_width=0.2, head_length=0.1, fc='black', ec='black', linewidth=2)
    
    # Phase 2: Critical Independent Labeling
    phase2_box = FancyBboxPatch((1.5, 13.5), 7, 2, 
                                boxstyle="round,pad=0.2", 
                                facecolor=colors['critical_phase'], 
                                edgecolor='red', linewidth=3)
    ax.add_patch(phase2_box)
    ax.text(5, 14.5, 'PHASE 2: INDEPENDENT LABELING (CRITICAL)\n\nExternal Independent Labeling + Temporal Patterns Analysis\nExpert Simulation + Domain Knowledge Application\n\nTarget Recreation Testing: >70% Success = LEAKAGE', 
            ha='center', va='center', fontsize=11, fontweight='bold', color='darkred')
    
    # Arrow 2
    ax.arrow(5, 13.3, 0, -0.8, head_width=0.2, head_length=0.1, fc='black', ec='black', linewidth=2)
    
    # Decision Diamond
    diamond_y = 11.5
    diamond = patches.RegularPolygon((5, diamond_y), 4, radius=1, 
                                     orientation=np.pi/4, 
                                     facecolor='yellow', 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(diamond)
    ax.text(5, diamond_y, 'Data Leakage\nDetected?\n(>70% Recreation)', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Invalid Path (LEFT)
    invalid_box = FancyBboxPatch((0.5, 9.5), 3, 1.5, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['invalid_process'], 
                                 edgecolor='red', linewidth=2)
    ax.add_patch(invalid_box)
    ax.text(2, 10.25, 'INVALID PIPELINE\n\nmain4.py: 96.67%\nArtificial Performance\nCIRCULAR DEPENDENCY', 
            ha='center', va='center', fontsize=9, color='red', fontweight='bold')
    
    # Valid Path (RIGHT)
    valid_box = FancyBboxPatch((6.5, 9.5), 3, 1.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['valid_process'], 
                               edgecolor='green', linewidth=2)
    ax.add_patch(valid_box)
    ax.text(8, 10.25, 'VALID LABELS\n\nLeak-Free\nIndependent Target\nREADY FOR MODELING', 
            ha='center', va='center', fontsize=9, color='green', fontweight='bold')
    
    # Decision arrows
    ax.arrow(4.2, 11, -1.5, -1, head_width=0.15, head_length=0.1, fc='red', ec='red', linewidth=2)
    ax.text(3, 10.8, 'YES\nLEAKAGE', ha='center', va='center', fontsize=9, color='red', fontweight='bold')
    
    ax.arrow(5.8, 11, 1.5, -1, head_width=0.15, head_length=0.1, fc='green', ec='green', linewidth=2)
    ax.text(7, 10.8, 'NO\nLEAK-FREE', ha='center', va='center', fontsize=9, color='green', fontweight='bold')
    
    # Continue from valid path
    ax.arrow(8, 9.3, -2.5, -1, head_width=0.15, head_length=0.1, fc='green', ec='green', linewidth=2)
    
    # Phase 3: Feature Engineering
    phase3_box = FancyBboxPatch((2.5, 7), 5, 1.2, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['valid_process'], 
                                edgecolor='green', linewidth=2)
    ax.add_patch(phase3_box)
    ax.text(5, 7.6, 'PHASE 3: LEAK-FREE FEATURE ENGINEERING\n\nStrict Feature Filtering + Independence Validation', 
            ha='center', va='center', fontsize=11, fontweight='bold')
    
    # Arrow 3
    ax.arrow(5, 6.8, 0, -0.8, head_width=0.2, head_length=0.1, fc='black', ec='black', linewidth=2)
    
    # Phase 4: Multiple Pipeline Comparison
    phase4_box = FancyBboxPatch((0.5, 4), 9, 1.8, 
                                boxstyle="round,pad=0.1", 
                                facecolor='lightcyan', 
                                edgecolor='blue', linewidth=2)
    ax.add_patch(phase4_box)
    ax.text(5, 4.9, 'PHASE 4: MULTIPLE PIPELINE COMPARISON\n\n6 Pipeline Implementation:\nmain3.py (58-61%) | main4.py (INVALID) | main5.py (69-71%)\nmain6.py (65%, Overfitting) | main7.py (68.33%, BEST) | main_leak_free.py (60-61%, Production)', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Arrow 4
    ax.arrow(5, 3.8, 0, -0.8, head_width=0.2, head_length=0.1, fc='black', ec='black', linewidth=2)
    
    # SHAP Analysis Results
    shap_box = FancyBboxPatch((1, 1.5), 8, 1.2, 
                              boxstyle="round,pad=0.1", 
                              facecolor=colors['best_performance'], 
                              edgecolor='blue', linewidth=2)
    ax.add_patch(shap_box)
    ax.text(5, 2.1, 'SHAP FEATURE IMPORTANCE ANALYSIS\n\nPhysical Activity: 23.84% | Time Investment: 12.33% | Work Patterns: 17.34%', 
            ha='center', va='center', fontsize=11, fontweight='bold', color='darkblue')
    
    # Production Recommendation
    prod_box = FancyBboxPatch((2, 0.2), 6, 1, 
                              boxstyle="round,pad=0.1", 
                              facecolor='gold', 
                              edgecolor='blue', linewidth=3)
    ax.add_patch(prod_box)
    ax.text(5, 0.7, 'RECOMMENDED: main7.py (SHAP Title-Only)\n68.33% Accuracy + Business Interpretability', 
            ha='center', va='center', fontsize=11, fontweight='bold', color='darkblue')
    
    plt.tight_layout()
    return fig

def create_simple_pipeline_comparison():
    """Create simple pipeline comparison without emojis"""
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Title
    title_box = FancyBboxPatch((1, 8.5), 8, 1, 
                               boxstyle="round,pad=0.2", 
                               facecolor='lightgray', 
                               edgecolor='black', linewidth=2)
    ax.add_patch(title_box)
    ax.text(5, 9, 'MULTIPLE MACHINE LEARNING PIPELINE COMPARISON', 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    # Pipeline results
    pipelines = [
        ('main3.py', 'Standard Ablation', '58-61%', 'VALID', 'lightgreen'),
        ('main4.py', 'RFE Regular', '96.67%', 'INVALID', 'lightcoral'),
        ('main5.py', 'RFE Bias-Corrected', '69-71%', 'CAUTION', 'lightyellow'),
        ('main6.py', 'RFE Title-Only', '65%', 'CAUTION', 'lightyellow'),
        ('main7.py', 'SHAP Title-Only', '68.33%', 'BEST', 'lightgreen'),
        ('main_leak_free.py', 'External Labels', '60-61%', 'PRODUCTION', 'lightgreen')
    ]
    
    y_start = 7
    for i, (name, method, accuracy, status, color) in enumerate(pipelines):
        y_pos = y_start - i * 1.1
        
        # Pipeline box
        pipeline_box = FancyBboxPatch((1, y_pos), 8, 0.8, 
                                      boxstyle="round,pad=0.1", 
                                      facecolor=color, 
                                      edgecolor='black', linewidth=1)
        ax.add_patch(pipeline_box)
        
        # Status and details
        status_text = f"[{status}] {name}: {method} - Accuracy: {accuracy}"
        ax.text(5, y_pos+0.4, status_text, 
                ha='center', va='center', fontsize=11, fontweight='bold')
    
    plt.tight_layout()
    return fig

if __name__ == "__main__":
    # Create directories
    import os
    os.makedirs('laporan-akhir/figures', exist_ok=True)
    
    # Generate simple methodology flowchart
    print("Creating Simple Methodology Flowchart...")
    fig1 = create_simple_methodology_flowchart()
    fig1.savefig('laporan-akhir/figures/gambar_3_1_metodologi_flowchart_revised.png', 
                 dpi=300, bbox_inches='tight', facecolor='white')
    print("Saved: gambar_3_1_metodologi_flowchart_revised.png")
    
    # Generate simple pipeline comparison
    print("Creating Simple Pipeline Comparison...")
    fig2 = create_simple_pipeline_comparison()
    fig2.savefig('laporan-akhir/figures/gambar_3_2_ml_pipeline_comparison.png', 
                 dpi=300, bbox_inches='tight', facecolor='white')
    print("Saved: gambar_3_2_ml_pipeline_comparison.png")
    
    plt.close('all')
    
    print("\nGAMBAR ALUR METODOLOGI BERHASIL DIBUAT!")
    print("Location: laporan-akhir/figures/")
    print("Files:")
    print("  - gambar_3_1_metodologi_flowchart_revised.png")
    print("  - gambar_3_2_ml_pipeline_comparison.png")
