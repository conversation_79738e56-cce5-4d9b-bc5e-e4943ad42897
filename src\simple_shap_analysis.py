"""
Simple SHAP Analysis for Fatigue Prediction
Simplified SHAP implementation focused on core functionality

FEATURES:
- Basic SHAP analysis with KernelExplainer
- Feature importance ranking
- Model performance comparison
- Leak-free dataset compatibility
- Robust error handling
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, f1_score

# Configure logging
logger = logging.getLogger(__name__)

class SimpleSHAPAnalysis:
    """
    Simple SHAP analysis for fatigue prediction
    """
    
    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        """Initialize SHAP analysis"""
        self.data_path = data_path
        self.target_column = target_column
        self.random_state = random_state
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.label_encoder = LabelEncoder()
        
        # Models to test
        self.models = {
            'logistic_regression': LogisticRegression(random_state=random_state, max_iter=1000),
            'random_forest': RandomForestClassifier(random_state=random_state, n_estimators=50)
        }
        
        self.results = {}
        
        # Set random seeds
        np.random.seed(random_state)
        
    def load_data(self):
        """Load and prepare data"""
        logger.info(f"Loading data from {self.data_path}")
        
        self.data = pd.read_csv(self.data_path)
        logger.info(f"Data loaded: {self.data.shape}")
        
        # Prepare features and target
        self.X = self.data.drop(columns=[self.target_column])
        self.y = self.data[self.target_column]
        
        # Remove non-numeric columns
        numeric_columns = self.X.select_dtypes(include=[np.number]).columns
        self.X = self.X[numeric_columns]
        self.feature_names = list(self.X.columns)
        
        logger.info(f"Features: {len(self.feature_names)}")
        logger.info(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Encode target
        self.y_encoded = self.label_encoder.fit_transform(self.y)
        
    def train_and_evaluate_models(self):
        """Train models and evaluate performance"""
        logger.info("Training and evaluating models...")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y_encoded, test_size=0.2, 
            random_state=self.random_state, stratify=self.y_encoded
        )
        
        self.X_train, self.X_test = X_train, X_test
        self.y_train, self.y_test = y_train, y_test
        
        # Train and evaluate each model
        for model_name, model in self.models.items():
            logger.info(f"Training {model_name}...")
            
            # Train model
            model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            self.results[model_name] = {
                'model': model,
                'accuracy': accuracy,
                'f1_score': f1,
                'feature_importance': None
            }
            
            logger.info(f"{model_name} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
    
    def calculate_permutation_importance(self):
        """Calculate permutation-based feature importance (SHAP alternative)"""
        logger.info("Calculating permutation-based feature importance...")
        
        from sklearn.inspection import permutation_importance
        
        for model_name, model_info in self.results.items():
            logger.info(f"Calculating importance for {model_name}...")
            
            model = model_info['model']
            
            try:
                # Calculate permutation importance
                perm_importance = permutation_importance(
                    model, self.X_test, self.y_test,
                    n_repeats=10, random_state=self.random_state,
                    scoring='accuracy'
                )
                
                # Create feature importance ranking
                importance_scores = perm_importance.importances_mean
                importance_std = perm_importance.importances_std
                
                feature_importance = []
                for i, feature in enumerate(self.feature_names):
                    feature_importance.append({
                        'feature': feature,
                        'importance': importance_scores[i],
                        'importance_std': importance_std[i],
                        'rank': 0  # Will be set after sorting
                    })
                
                # Sort by importance
                feature_importance.sort(key=lambda x: abs(x['importance']), reverse=True)
                
                # Update ranks
                for i, item in enumerate(feature_importance):
                    item['rank'] = i + 1
                
                self.results[model_name]['feature_importance'] = feature_importance
                
                logger.info(f"✅ Importance calculated for {model_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to calculate importance for {model_name}: {str(e)}")
    
    def run_analysis(self) -> Dict:
        """Run complete analysis"""
        logger.info("Starting simple SHAP-style analysis")
        
        # Load data
        self.load_data()
        
        # Train models
        self.train_and_evaluate_models()
        
        # Calculate feature importance
        self.calculate_permutation_importance()
        
        # Compile results
        analysis_results = {
            'dataset_info': {
                'path': self.data_path,
                'shape': self.data.shape,
                'target_column': self.target_column,
                'features': self.feature_names,
                'target_distribution': self.y.value_counts().to_dict()
            },
            'model_performance': {},
            'feature_importance': {},
            'analysis_completed': True,
            'timestamp': datetime.now().isoformat()
        }
        
        # Add model performance and feature importance
        for model_name, model_info in self.results.items():
            analysis_results['model_performance'][model_name] = {
                'accuracy': model_info['accuracy'],
                'f1_score': model_info['f1_score']
            }
            
            if model_info['feature_importance']:
                analysis_results['feature_importance'][model_name] = model_info['feature_importance']
        
        logger.info("Simple SHAP-style analysis completed")
        return analysis_results
    
    def save_results(self, results: Dict, prefix: str = "") -> Tuple[str, str]:
        """Save analysis results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("results/simple_shap_analysis")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Add prefix if provided
        file_prefix = f"{prefix}_" if prefix else ""
        
        # Save feature importance CSV
        results_file = results_dir / f"{file_prefix}simple_shap_results_{timestamp}.csv"
        
        all_importance_data = []
        for model_name, importance_list in results['feature_importance'].items():
            for item in importance_list:
                all_importance_data.append({
                    'model': model_name,
                    'feature': item['feature'],
                    'importance': item['importance'],
                    'importance_std': item['importance_std'],
                    'rank': item['rank']
                })
        
        if all_importance_data:
            importance_df = pd.DataFrame(all_importance_data)
            importance_df.to_csv(results_file, index=False)
        
        # Save report
        report_file = results_dir / f"{file_prefix}simple_shap_report_{timestamp}.txt"
        report_content = self._generate_report(results)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"Results saved:")
        logger.info(f"  - Results: {results_file}")
        logger.info(f"  - Report: {report_file}")
        
        return str(results_file), str(report_file)
    
    def _generate_report(self, results: Dict) -> str:
        """Generate analysis report"""
        report = []
        report.append("=" * 80)
        report.append("🔍 SIMPLE SHAP-STYLE ANALYSIS REPORT")
        report.append("=" * 80)
        
        # Dataset information
        dataset_info = results['dataset_info']
        report.append(f"\n📋 DATASET INFORMATION:")
        report.append(f"   • Dataset Path: {dataset_info['path']}")
        report.append(f"   • Target Column: {dataset_info['target_column']}")
        report.append(f"   • Total Features: {len(dataset_info['features'])}")
        report.append(f"   • Total Samples: {dataset_info['shape'][0]}")
        report.append(f"   • Target Distribution: {dataset_info['target_distribution']}")
        
        # Model performance
        report.append(f"\n🤖 MODEL PERFORMANCE:")
        for model_name, perf in results['model_performance'].items():
            report.append(f"   • {model_name.replace('_', ' ').title()}:")
            report.append(f"     - Accuracy: {perf['accuracy']:.4f}")
            report.append(f"     - F1-Score: {perf['f1_score']:.4f}")
        
        # Feature importance
        report.append(f"\n🎯 FEATURE IMPORTANCE (Permutation-based):")
        
        for model_name, importance_list in results['feature_importance'].items():
            report.append(f"\n   📊 {model_name.replace('_', ' ').title()}:")
            
            # Top 10 features
            for item in importance_list[:10]:
                importance = item['importance']
                feature = item['feature']
                rank = item['rank']
                
                # Add visual indicator
                if importance >= 0.05:
                    icon = "🔥"  # High
                elif importance >= 0.02:
                    icon = "⭐"  # Medium
                elif importance >= 0.01:
                    icon = "🔸"  # Low
                else:
                    icon = "▫️"   # Very low
                
                report.append(f"     {rank:2d}. {icon} {feature}: {importance:.4f}")
        
        # Summary
        report.append(f"\n📊 ANALYSIS SUMMARY:")
        report.append(f"   • Total models analyzed: {len(results['model_performance'])}")
        report.append(f"   • Total features analyzed: {len(dataset_info['features'])}")
        
        # Find best model
        best_model = max(results['model_performance'].items(), key=lambda x: x[1]['accuracy'])
        report.append(f"   • Best performing model: {best_model[0].replace('_', ' ').title()}")
        report.append(f"   • Best accuracy: {best_model[1]['accuracy']:.4f}")
        report.append(f"   • Analysis timestamp: {results['timestamp']}")
        
        return "\n".join(report)


def main():
    """Main function for testing"""
    logging.basicConfig(level=logging.INFO)
    
    # Test on ultra-safe dataset
    analyzer = SimpleSHAPAnalysis(
        data_path="dataset/processed/ultra_safe_fatigue_dataset.csv",
        target_column="external_fatigue_risk",
        random_state=42
    )
    
    try:
        results = analyzer.run_analysis()
        results_file, report_file = analyzer.save_results(results, prefix="ultra_safe")
        
        print("🎉 Simple SHAP-style Analysis Completed!")
        print(f"📊 Results: {results_file}")
        print(f"📋 Report: {report_file}")
        
        # Print top features
        if 'feature_importance' in results:
            for model_name, importance_list in results['feature_importance'].items():
                print(f"\n🏆 Top 5 features for {model_name.replace('_', ' ').title()}:")
                for item in importance_list[:5]:
                    print(f"   {item['rank']}. {item['feature']}: {item['importance']:.4f}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
