================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 18
   • Total Samples: 300

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [4, 5, 9, 10, 13, 15, 18, 20, 25, 30]
   • Total Experiments: 28

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: Random Forest
   • Features Used: 9
   • Accuracy: 0.6967 ± 0.0499
   • F1-Score: 0.6427
   • ⚠️ Overfitting Risk: 0.3033 (HIGH - Train-Test gap > 10%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 13
     - Accuracy: 0.6667 ± 0.0506
     - F1-Score: 0.6478
     - ⚠️ Overfitting: 0.0608 (MODERATE)
   • Random Forest:
     - Features: 9
     - Accuracy: 0.6967 ± 0.0499
     - F1-Score: 0.6427
     - ⚠️ Overfitting: 0.3033 (HIGH)
   • Gradient Boosting:
     - Features: 10
     - Accuracy: 0.6733 ± 0.0429
     - F1-Score: 0.6370
     - ⚠️ Overfitting: 0.3258 (HIGH)
   • Support Vector Machine:
     - Features: 9
     - Accuracy: 0.6633 ± 0.0499
     - F1-Score: 0.6369
     - ✅ Overfitting: 0.0450 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. pomokit_unique_words: 27 times (96.4%)
    2. title_balance_ratio: 26 times (92.9%)
    3. achievement_rate: 25 times (89.3%)
    4. work_days: 22 times (78.6%)
    5. pomokit_title_count: 21 times (75.0%)
    6. total_cycles: 20 times (71.4%)
    7. avg_distance_km: 20 times (71.4%)
    8. total_distance_km: 19 times (67.9%)
    9. productivity_points: 17 times (60.7%)
   10. total_title_diversity: 16 times (57.1%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. pomokit_unique_words: 10/10 top results
    2. title_balance_ratio: 10/10 top results
    3. total_cycles: 9/10 top results
    4. avg_distance_km: 9/10 top results
    5. achievement_rate: 9/10 top results
    6. total_title_diversity: 9/10 top results
    7. avg_time_minutes: 8/10 top results
    8. pomokit_title_count: 8/10 top results
    9. total_distance_km: 8/10 top results
   10. work_days: 7/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.3033
   • ⚠️ HIGH OVERFITTING RISK: Train-Test gap > 10%
   • Recommendation: Consider regularization or more data

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.0608 (MODERATE)
     ⚠️ Random Forest: 0.3033 (HIGH)
     ⚠️ Gradient Boosting: 0.3258 (HIGH)
     ✅ Support Vector Machine: 0.0450 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: Random Forest
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 9
   • Baseline Accuracy: 0.7033

   🎯 Feature Impact Analysis (Top 10):
      1. ▫️ avg_time_minutes:
         - Baseline: 0.7033 | Without: 0.6800
         - Impact: 0.0233 (3.32%)
         - LOW - Minor performance impact
      2. ▫️ total_title_diversity:
         - Baseline: 0.7033 | Without: 0.6867
         - Impact: 0.0167 (2.37%)
         - LOW - Minor performance impact
      3. ▫️ avg_distance_km:
         - Baseline: 0.7033 | Without: 0.6900
         - Impact: 0.0133 (1.90%)
         - LOW - Minor performance impact
      4. ▫️ title_balance_ratio:
         - Baseline: 0.7033 | Without: 0.6900
         - Impact: 0.0133 (1.90%)
         - LOW - Minor performance impact
      5. ⚪ total_cycles:
         - Baseline: 0.7033 | Without: 0.7000
         - Impact: 0.0033 (0.47%)
         - MINIMAL - Negligible performance impact
      6. ⚪ pomokit_title_count:
         - Baseline: 0.7033 | Without: 0.7067
         - Impact: -0.0033 (-0.47%)
         - MINIMAL - Negligible performance impact
      7. ⚪ achievement_rate:
         - Baseline: 0.7033 | Without: 0.7000
         - Impact: 0.0033 (0.47%)
         - MINIMAL - Negligible performance impact
      8. ⚪ work_days:
         - Baseline: 0.7033 | Without: 0.7067
         - Impact: -0.0033 (-0.47%)
         - MINIMAL - Negligible performance impact
      9. ⚪ pomokit_unique_words:
         - Baseline: 0.7033 | Without: 0.7000
         - Impact: 0.0033 (0.47%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • avg_time_minutes: 10.81% (score: 0.0553 ±0.0075)
     • total_title_diversity: 19.09% (score: 0.0977 ±0.0082)
     • avg_distance_km: 11.92% (score: 0.0610 ±0.0092)
     • title_balance_ratio: 22.74% (score: 0.1163 ±0.0129)
     • total_cycles: 2.35% (score: 0.0120 ±0.0027)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: Random Forest
   • Number of Features: 9
   • Selected Features:
      1. achievement_rate
      2. avg_distance_km
      3. avg_time_minutes
      4. pomokit_title_count
      5. pomokit_unique_words
      6. title_balance_ratio
      7. total_cycles
      8. total_title_diversity
      9. work_days
================================================================================