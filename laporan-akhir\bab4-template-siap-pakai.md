# BAB IV
# EKSPERIMEN DAN HASIL

## 4.1 EKSPERIMEN

### 4.1.1 Deskripsi Dataset dan Preprocessing

#### ******* Karakteristik Dataset

[Jelaskan karakteristik dataset penelitian Anda, termasuk:]
- Jumlah observasi dan periode pengumpulan data
- Sumber data (Strava, Pomokit)
- Kategori fitur utama
- Kriteria inklusi dan eksklusi data
- Statistik deskriptif dasar

**Tabel 4.1** Statistik Deskriptif Dataset Penelitian
[Masukkan tabel statistik deskriptif dengan kolom: Variabel, Mean, Std, Min, Max, N]

#### ******* Distribusi Target Variable (Fatigue Risk)

[Jelaskan distribusi target variable:]
- Definisi three-level classification (Low/Medium/High Risk)
- Distribusi jumlah dan persentase setiap kelas
- Temporal patterns sepanjang semester
- Validasi konstruk fatigue risk

**Gambar 4.1** Distribusi Risiko Fatigue Sepanjang Semester
[Masukkan visualisasi temporal distribution]

#### ******* Data Quality Assurance

[Jelaskan proses quality assurance:]
- Data integrity checks
- Cross-platform validation
- Outlier detection dan treatment
- Missing data handling
- Quality metrics hasil

### 4.1.2 Feature Engineering dan Text Processing

#### ******* Derived Features Creation

[Jelaskan proses pembuatan derived features:]
- Consistency score calculation
- Gamification balance metrics
- Weekly efficiency indicators
- Activity-productivity ratios
- Validasi derived features

#### ******* Text-based Feature Extraction

[Jelaskan text processing:]
- Preprocessing steps (tokenization, stemming)
- Feature extraction methods (TF-IDF, keyword frequency)
- Language pattern detection
- Text diversity metrics
- Predictive power text features

#### 4.1.2.3 Feature Selection dan Filtering

[Jelaskan feature selection process:]
- Temporal filtering untuk prevent data leakage
- Multicollinearity detection (VIF analysis)
- Feature importance preliminary analysis
- Domain knowledge validation
- Final feature set characteristics

### 4.1.3 Metodologi Klasifikasi Fatigue Risk

#### 4.1.3.1 Composite Scoring Approach

[Jelaskan composite scoring methodology:]
- Physical indicators (weight dan components)
- Cognitive indicators (weight dan components)
- Behavioral indicators (weight dan components)
- Scoring formula dan threshold determination
- Inter-rater reliability assessment

#### 4.1.3.2 Validation Strategy

[Jelaskan validation approach:]
- Nested cross-validation setup
- Stratified sampling strategy
- Temporal validation considerations
- Hold-out test set preparation
- Performance metrics selection

### 4.1.4 Machine Learning Model Setup

#### 4.1.4.1 Algorithm Selection

[Jelaskan pemilihan algoritma:]
- Random Forest: karakteristik dan keunggulan
- SVM (RBF): konfigurasi dan aplikabilitas
- Neural Network: arsitektur dan design rationale
- Ensemble methods: strategy dan implementation

#### 4.1.4.2 Hyperparameter Optimization Strategy

[Jelaskan hyperparameter optimization:]
- Grid search parameter spaces untuk setiap algoritma
- Cross-validation strategy untuk tuning
- Primary dan secondary metrics
- Computational resource considerations

#### 4.1.4.3 Class Imbalance Handling

[Jelaskan handling class imbalance:]
- SMOTE implementation strategy
- Balanced sampling techniques
- Cost-sensitive learning approach
- Evaluation metrics untuk imbalanced data

### 4.1.5 Experimental Design

#### ******* Ablation Study Design

[Jelaskan ablation study methodology:]
- Systematic feature removal approach
- Feature group analysis strategy
- Performance impact measurement
- Statistical significance testing

#### ******* Specialized Analysis Setup

[Jelaskan specialized experiments:]
- Title-only classification experiment design
- Bias correction methodology
- Cross-validation dan external validation
- Robustness testing framework

## 4.2 HASIL

### 4.2.1 Dataset Analysis Results

#### ******* Descriptive Statistics

[Presentasikan hasil analisis deskriptif:]
- Dataset characteristics summary
- Variable distribution findings
- Temporal pattern analysis
- Data quality assessment results

**Tabel 4.2** Hasil Analisis Deskriptif Dataset
[Masukkan tabel dengan statistik lengkap termasuk skewness, kurtosis]

#### ******* Fatigue Risk Distribution

[Presentasikan hasil distribusi target:]
- Classification distribution results (n dan %)
- Temporal fatigue patterns findings
- Peak fatigue period identification
- Construct validation results

**Gambar 4.2** Pola Temporal Risiko Fatigue
[Masukkan visualisasi temporal patterns]

### 4.2.2 Feature Engineering Results

#### ******* Derived Features Performance

[Presentasikan hasil derived features:]
- Feature creation success rates
- Correlation dengan target variable
- Feature stability analysis
- Predictive power comparison

**Tabel 4.3** Performance Derived Features
[Masukkan tabel dengan correlation, stability, interpretability scores]

#### ******* Text Processing Results

[Presentasikan hasil text processing:]
- Language pattern analysis findings
- Keyword frequency analysis results
- Text diversity metrics
- Predictive capability text features

#### 4.2.2.3 Feature Selection Results

[Presentasikan hasil feature selection:]
- Feature importance rankings
- Multicollinearity analysis results
- Final feature set characteristics
- Data leakage prevention validation

### 4.2.3 Classification Model Results

#### 4.2.3.1 Baseline Model Performance

[Presentasikan baseline results:]

**Tabel 4.4** Baseline Model Performance Comparison

| Model | Accuracy | Precision | Recall | F1-Score | AUC-ROC |
|================|==========|===========|========|==========|=========|
| Random Forest | [nilai] | [nilai] | [nilai] | [nilai] | [nilai] |
| SVM (RBF) | [nilai] | [nilai] | [nilai] | [nilai] | [nilai] |
| Neural Network | [nilai] | [nilai] | [nilai] | [nilai] | [nilai] |

[Jelaskan interpretasi hasil dan model terbaik]

#### 4.2.3.2 Hyperparameter Optimization Results

[Presentasikan hasil optimization:]
- Optimal parameter combinations untuk setiap model
- Performance improvement analysis
- Computational efficiency comparison
- Final model configuration

#### 4.2.3.3 Class Imbalance Handling Results

[Presentasikan hasil handling imbalance:]
- SMOTE implementation effectiveness
- Balanced accuracy improvements
- Precision-recall trade-offs analysis
- Minority class performance enhancement

### 4.2.4 Ablation Study Results

#### 4.2.4.1 Feature Impact Analysis

[Presentasikan ablation study results:]

**Tabel 4.5** Individual Feature Ablation Results

| Feature Removed | Accuracy Drop | Impact Level | Critical? |
|========================|===============|==============|===========|
| [Feature 1] | [nilai]% | [level] | [Ya/Tidak] |
| [Feature 2] | [nilai]% | [level] | [Ya/Tidak] |
| [dst...] | [nilai]% | [level] | [Ya/Tidak] |

[Jelaskan interpretasi dan critical features identification]

#### 4.2.4.2 Feature Group Analysis Results

[Presentasikan feature group analysis:]
- Physical activity features contribution
- Productivity features impact
- Gamification features effectiveness
- Text-based features performance
- Group synergy effects

#### 4.2.4.3 Optimal Feature Set Results

[Presentasikan optimal feature set:]
- Minimal viable feature set identification
- Performance vs complexity trade-off
- Recommended feature combinations
- Practical implementation considerations

### 4.2.5 Specialized Analysis Results

#### 4.2.5.1 Title-Only Classification Results

[Presentasikan title-only results:]
- Pure text-based prediction performance
- Comparison dengan full feature model
- Practical applicability assessment
- Implementation advantages dan limitations

#### 4.2.5.2 Bias Correction Results

[Presentasikan bias correction results:]
- Bias detection findings
- Bias correction effectiveness
- Fairness metrics improvement
- Generalizability enhancement

#### 4.2.5.3 Model Validation Results

[Presentasikan validation results:]
- Cross-validation performance consistency
- External validation results
- Robustness testing outcomes
- Generalizability assessment

### 4.2.6 Final Model Performance

#### 4.2.6.1 Production Model Selection

[Presentasikan final model:]
- Model selection rationale
- Final performance metrics
- Interpretability analysis
- Deployment readiness assessment

#### 4.2.6.2 Model Interpretability

[Presentasikan interpretability analysis:]
- SHAP value analysis results
- Feature importance visualization
- Decision boundary analysis
- Practical insights untuk practitioners

#### 4.2.6.3 Implementation Guidelines

[Presentasikan implementation guidelines:]
- Practical deployment recommendations
- Monitoring indicators
- Maintenance requirements
- Scalability considerations

---

**Catatan Penggunaan Template:**
1. Ganti placeholder [nilai], [level], dll dengan data aktual Anda
2. Tambahkan tabel dan gambar sesuai hasil eksperimen
3. Sesuaikan detail dengan metodologi yang digunakan
4. Pastikan konsistensi numbering dan referensi
5. Tambahkan interpretasi dan diskusi untuk setiap hasil
