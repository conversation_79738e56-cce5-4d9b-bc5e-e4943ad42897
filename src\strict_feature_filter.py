"""
Strict Feature Filter for Data Leakage Prevention
Removes ALL features that could potentially be used to create target labels

This module implements the most conservative approach to prevent data leakage:
1. Removes all behavioral indicators used in classification
2. Removes all derived metrics that could leak information
3. Keeps only raw, independent measurements
4. Validates feature independence from target creation process
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Set
from pathlib import Path

logger = logging.getLogger(__name__)

class StrictFeatureFilter:
    """
    Implements strict feature filtering to prevent any data leakage
    """
    
    def __init__(self):
        """Initialize the strict feature filter"""
        
        # Features that are DEFINITELY used in target creation (MUST REMOVE)
        self.target_creation_features = {
            'work_days',                    # Used in classification rules
            'consistency_score',            # Used in classification rules  
            'activity_days',               # Used in classification rules
            'stress_count',                # Used in fatigue score calculation
            'negative_emotion_count',      # Used in fatigue score calculation
            'workload_count',              # Used in fatigue score calculation
            'time_pressure_count',         # Used in fatigue score calculation
            'recovery_count',              # Used in fatigue score calculation
            'work_intensity',              # Used in fatigue score calculation
            'work_life_imbalance',         # Used in fatigue score calculation
            'activity_deficit',            # Used in fatigue score calculation
            'consistency_deficit',         # Used in fatigue score calculation
            'fatigue_risk_score',          # Direct target creation
            'workaholic_pattern',          # Derived from target creation features
            'recovery_deficit',            # Derived from target creation features
            'chronic_stress_pattern',      # Derived from target creation features
            'time_pressure_syndrome'       # Derived from target creation features
        }
        
        # Features that are POTENTIALLY derived from target creation (SUSPICIOUS)
        self.suspicious_features = {
            'productivity_points',         # May be derived from work patterns
            'achievement_rate',           # May be derived from work patterns
            'gamification_balance',       # May be derived from work-life balance
            'total_cycles',               # May correlate with work_days
            'pomokit_title_count',        # May correlate with work patterns
            'strava_title_count',         # May correlate with activity patterns
        }
        
        # Features that are SAFE (raw measurements, not used in target creation)
        self.safe_features = {
            'total_distance_km',          # Raw physical measurement
            'avg_distance_km',            # Raw physical measurement
            'total_time_minutes',         # Raw time measurement
            'avg_time_minutes',           # Raw time measurement
            'strava_title_length',        # Raw text measurement
            'pomokit_title_length',       # Raw text measurement
            'strava_unique_words',        # Raw text measurement
            'pomokit_unique_words',       # Raw text measurement
            'title_balance_ratio',        # Raw ratio measurement
            'total_title_diversity',      # Raw text measurement
            'activity_points',            # Raw gamification measurement
            'identity'                    # Identifier (not used in target creation)
        }
    
    def create_ultra_safe_dataset(self, input_path: str, output_path: str, 
                                 target_column: str = 'external_fatigue_risk') -> str:
        """
        Create ultra-safe dataset with only guaranteed leak-free features
        
        Args:
            input_path: Path to input dataset
            output_path: Path to save safe dataset
            target_column: Name of target column
            
        Returns:
            Path to created safe dataset
        """
        logger.info("Creating ultra-safe dataset with strict feature filtering...")
        
        # Load data
        df = pd.read_csv(input_path)
        logger.info(f"Loaded dataset: {df.shape}")
        
        # Identify all features to remove
        features_to_remove = self.target_creation_features.union(self.suspicious_features)
        
        # Log removal process
        logger.info(f"Removing {len(features_to_remove)} potentially leaky features:")
        for feature in sorted(features_to_remove):
            if feature in df.columns:
                logger.info(f"  - Removing: {feature}")
        
        # Keep only safe features + target
        available_safe_features = [col for col in self.safe_features if col in df.columns]
        
        if target_column in df.columns:
            columns_to_keep = available_safe_features + [target_column]
        else:
            columns_to_keep = available_safe_features
            logger.warning(f"Target column '{target_column}' not found in dataset")
        
        # Create safe dataset
        safe_df = df[columns_to_keep].copy()
        
        logger.info(f"Ultra-safe dataset created: {safe_df.shape}")
        logger.info(f"Safe features retained: {available_safe_features}")
        
        # Save safe dataset
        safe_df.to_csv(output_path, index=False)
        logger.info(f"Ultra-safe dataset saved to: {output_path}")
        
        return output_path
    
    def create_conservative_dataset(self, input_path: str, output_path: str,
                                  target_column: str = 'external_fatigue_risk') -> str:
        """
        Create conservative dataset removing only definite leaky features
        
        Args:
            input_path: Path to input dataset
            output_path: Path to save conservative dataset
            target_column: Name of target column
            
        Returns:
            Path to created conservative dataset
        """
        logger.info("Creating conservative dataset with moderate feature filtering...")
        
        # Load data
        df = pd.read_csv(input_path)
        logger.info(f"Loaded dataset: {df.shape}")
        
        # Remove only definite target creation features
        features_to_remove = self.target_creation_features
        
        # Log removal process
        logger.info(f"Removing {len(features_to_remove)} definite leaky features:")
        for feature in sorted(features_to_remove):
            if feature in df.columns:
                logger.info(f"  - Removing: {feature}")
        
        # Keep all other features
        columns_to_keep = [col for col in df.columns if col not in features_to_remove]
        
        if target_column not in columns_to_keep and target_column in df.columns:
            columns_to_keep.append(target_column)
        
        # Create conservative dataset
        conservative_df = df[columns_to_keep].copy()
        
        logger.info(f"Conservative dataset created: {conservative_df.shape}")
        
        # Save conservative dataset
        conservative_df.to_csv(output_path, index=False)
        logger.info(f"Conservative dataset saved to: {output_path}")
        
        return output_path
    
    def validate_feature_independence(self, df: pd.DataFrame, target_column: str) -> Dict:
        """
        Validate that features are independent from target creation
        
        Args:
            df: DataFrame to validate
            target_column: Name of target column
            
        Returns:
            Dictionary with validation results
        """
        logger.info("Validating feature independence from target creation...")
        
        validation_results = {
            'total_features': len(df.columns) - 1,  # Exclude target
            'safe_features': [],
            'suspicious_correlations': [],
            'high_risk_features': [],
            'validation_passed': True
        }
        
        if target_column not in df.columns:
            logger.warning(f"Target column '{target_column}' not found")
            return validation_results
        
        # Encode target for correlation analysis
        from sklearn.preprocessing import LabelEncoder
        le = LabelEncoder()
        target_encoded = le.fit_transform(df[target_column])
        
        # Check correlation of each feature with target
        for col in df.columns:
            if col == target_column:
                continue
                
            try:
                correlation = np.corrcoef(df[col], target_encoded)[0, 1]
                abs_correlation = abs(correlation)
                
                if abs_correlation > 0.5:
                    validation_results['high_risk_features'].append((col, correlation))
                    validation_results['validation_passed'] = False
                    logger.warning(f"HIGH RISK: {col} has correlation {correlation:.4f} with target")
                elif abs_correlation > 0.3:
                    validation_results['suspicious_correlations'].append((col, correlation))
                    logger.warning(f"SUSPICIOUS: {col} has correlation {correlation:.4f} with target")
                else:
                    validation_results['safe_features'].append((col, correlation))
                    
            except Exception as e:
                logger.warning(f"Could not calculate correlation for {col}: {str(e)}")
        
        logger.info(f"Validation completed:")
        logger.info(f"  - Safe features: {len(validation_results['safe_features'])}")
        logger.info(f"  - Suspicious features: {len(validation_results['suspicious_correlations'])}")
        logger.info(f"  - High risk features: {len(validation_results['high_risk_features'])}")
        logger.info(f"  - Validation passed: {validation_results['validation_passed']}")
        
        return validation_results
    
    def generate_filtering_report(self, original_df: pd.DataFrame, safe_df: pd.DataFrame,
                                validation_results: Dict) -> Dict:
        """Generate comprehensive filtering report"""
        
        report = {
            'original_shape': original_df.shape,
            'safe_shape': safe_df.shape,
            'features_removed': original_df.shape[1] - safe_df.shape[1],
            'features_retained': safe_df.shape[1] - 1,  # Exclude target
            'removal_percentage': ((original_df.shape[1] - safe_df.shape[1]) / original_df.shape[1]) * 100,
            'validation_passed': validation_results['validation_passed'],
            'safe_features_count': len(validation_results['safe_features']),
            'suspicious_features_count': len(validation_results['suspicious_correlations']),
            'high_risk_features_count': len(validation_results['high_risk_features']),
            'retained_features': [col for col in safe_df.columns if col != safe_df.columns[-1]],  # Exclude target
            'data_leakage_risk': 'LOW' if validation_results['validation_passed'] else 'HIGH'
        }
        
        return report


def main():
    """Main function for testing strict feature filtering"""
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Create strict feature filter
    filter_system = StrictFeatureFilter()
    
    # Test with external labeled data
    input_path = "dataset/processed/external_labeled_fatigue_dataset.csv"
    
    if not Path(input_path).exists():
        logger.error(f"Input file not found: {input_path}")
        logger.info("Please run external_fatigue_labeler.py first")
        return
    
    try:
        # Create ultra-safe dataset
        ultra_safe_path = "dataset/processed/ultra_safe_fatigue_dataset.csv"
        filter_system.create_ultra_safe_dataset(input_path, ultra_safe_path, 'external_fatigue_risk')
        
        # Create conservative dataset
        conservative_path = "dataset/processed/conservative_safe_fatigue_dataset.csv"
        filter_system.create_conservative_dataset(input_path, conservative_path, 'external_fatigue_risk')
        
        # Validate both datasets
        ultra_safe_df = pd.read_csv(ultra_safe_path)
        conservative_df = pd.read_csv(conservative_path)
        original_df = pd.read_csv(input_path)
        
        ultra_validation = filter_system.validate_feature_independence(ultra_safe_df, 'external_fatigue_risk')
        conservative_validation = filter_system.validate_feature_independence(conservative_df, 'external_fatigue_risk')
        
        # Generate reports
        ultra_report = filter_system.generate_filtering_report(original_df, ultra_safe_df, ultra_validation)
        conservative_report = filter_system.generate_filtering_report(original_df, conservative_df, conservative_validation)
        
        print("🎉 Strict Feature Filtering Completed!")
        print(f"\n📊 ULTRA-SAFE DATASET:")
        print(f"   Original: {ultra_report['original_shape']} → Safe: {ultra_report['safe_shape']}")
        print(f"   Features removed: {ultra_report['features_removed']} ({ultra_report['removal_percentage']:.1f}%)")
        print(f"   Data leakage risk: {ultra_report['data_leakage_risk']}")
        
        print(f"\n📊 CONSERVATIVE DATASET:")
        print(f"   Original: {conservative_report['original_shape']} → Safe: {conservative_report['safe_shape']}")
        print(f"   Features removed: {conservative_report['features_removed']} ({conservative_report['removal_percentage']:.1f}%)")
        print(f"   Data leakage risk: {conservative_report['data_leakage_risk']}")
        
    except Exception as e:
        logger.error(f"Strict filtering failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
