# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-20 12:16:15
# Best Algorithm: Random Forest
# Accuracy: 0.6967 ± 0.0499

OPTIMAL_FEATURES = [
    'achievement_rate',
    'avg_distance_km',
    'avg_time_minutes',
    'pomokit_title_count',
    'pomokit_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_title_diversity',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.7033

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'avg_time_minutes': 0.0233,  # 3.32% impact
    'total_title_diversity': 0.0167,  # 2.37% impact
    'avg_distance_km': 0.0133,  # 1.90% impact
    'title_balance_ratio': 0.0133,  # 1.90% impact
    'total_cycles': 0.0033,  # 0.47% impact
    'pomokit_title_count': -0.0033,  # -0.47% impact
    'achievement_rate': 0.0033,  # 0.47% impact
    'work_days': -0.0033,  # -0.47% impact
    'pomokit_unique_words': 0.0033,  # 0.47% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'avg_time_minutes': 10.81,  # 10.81%
    'total_title_diversity': 19.09,  # 19.09%
    'avg_distance_km': 11.92,  # 11.92%
    'title_balance_ratio': 22.74,  # 22.74%
    'total_cycles': 2.35,  # 2.35%
    'pomokit_title_count': 4.82,  # 4.82%
    'achievement_rate': 13.22,  # 13.22%
    'work_days': 0.00,  # 0.00%
    'pomokit_unique_words': 15.05,  # 15.05%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'avg_time_minutes',  # 3.32% impact when removed
    'total_title_diversity',  # 2.37% impact when removed
    'avg_distance_km',  # 1.90% impact when removed
    'title_balance_ratio',  # 1.90% impact when removed
    'total_cycles',  # 0.47% impact when removed
    'pomokit_title_count',  # -0.47% impact when removed
    'achievement_rate',  # 0.47% impact when removed
    'work_days',  # -0.47% impact when removed
    'pomokit_unique_words',  # 0.47% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'avg_time_minutes',  # 10.81% permutation importance
    'total_title_diversity',  # 19.09% permutation importance
    'avg_distance_km',  # 11.92% permutation importance
    'title_balance_ratio',  # 22.74% permutation importance
    'total_cycles',  # 2.35% permutation importance
    'pomokit_title_count',  # 4.82% permutation importance
    'achievement_rate',  # 13.22% permutation importance
    'work_days',  # 0.00% permutation importance
    'pomokit_unique_words',  # 15.05% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'avg_time_minutes': 'LOW - Minor performance impact',
    'total_title_diversity': 'LOW - Minor performance impact',
    'avg_distance_km': 'LOW - Minor performance impact',
    'title_balance_ratio': 'LOW - Minor performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
    'achievement_rate': 'MINIMAL - Negligible performance impact',
    'work_days': 'MINIMAL - Negligible performance impact',
    'pomokit_unique_words': 'MINIMAL - Negligible performance impact',
}
