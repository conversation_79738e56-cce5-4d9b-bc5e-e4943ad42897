# BAB IV - EKSPERIMEN DAN HASIL

## 4.1 EKSPERIMEN

### 4.1.1 Deskripsi Dataset dan Preprocessing
#### ******* Karakteristik Dataset
- Statistik deskriptif dataset penelitian
- Distribusi variabel utama (aktivitas fisik, produktivitas, gamifikasi)
- Temporal patterns dan konsistensi data
- Kualitas data dan missing values analysis

#### ******* Distribusi Target Variable (Fatigue Risk)
- Distribusi klasifikasi fatigue risk (low/medium/high)
- Temporal patterns fatigue risk sepanjang semester
- Analisis periode peak fatigue (ujian, deadline)
- Validasi konstruk fatigue risk

#### ******* Data Quality Assurance
- Proses data cleaning dan validation
- Outlier detection dan treatment
- Cross-validation antara sumber data (Strava-Pomokit)
- Data integrity checks

### 4.1.2 Feature Engineering dan Text Processing
#### ******* Derived Features Creation
- Consistency score calculation
- Gamification balance metrics
- Weekly efficiency indicators
- Activity-productivity ratios

#### ******* Text-based Feature Extraction
- Title analysis dari aktivitas Strava dan Pomokit
- Keyword extraction dan vocabulary analysis
- Language pattern detection (Indonesian/English)
- Text statistics dan diversity metrics

#### 4.1.2.3 Feature Selection dan Filtering
- Feature importance preliminary analysis
- Multicollinearity detection
- Feature filtering untuk prevent data leakage
- Safe feature sets untuk ML training

### 4.1.3 Metodologi Klasifikasi Fatigue Risk
#### 4.1.3.1 Composite Scoring Approach
- Definisi dan validasi composite scoring
- Threshold analysis untuk optimal cut-points
- Inter-rater reliability assessment
- Classification consistency across time periods

#### 4.1.3.2 Validation Strategy
- Stratified cross-validation setup
- Temporal validation approach
- Hold-out test set preparation
- Performance metrics selection

### 4.1.4 Machine Learning Model Setup
#### 4.1.4.1 Algorithm Selection
- Random Forest configuration
- SVM dengan RBF kernel setup
- Neural Network architecture design
- Ensemble methods preparation

#### 4.1.4.2 Hyperparameter Optimization Strategy
- Grid search parameter space definition
- Cross-validation strategy untuk tuning
- Performance metrics untuk optimization
- Computational resource allocation

#### 4.1.4.3 Class Imbalance Handling
- SMOTE implementation strategy
- Balanced sampling techniques
- Cost-sensitive learning approach
- Evaluation metrics untuk imbalanced data

### 4.1.5 Experimental Design
#### 4.1.5.1 Ablation Study Design
- Systematic feature removal methodology
- Feature group analysis approach
- Performance impact measurement
- Statistical significance testing

#### ******* Specialized Analysis Setup
- Title-only classification experiment
- Bias correction methodology
- Cross-validation dan external validation
- Robustness testing framework

#### ******* Evaluation Framework
- Performance metrics definition
- Statistical testing procedures
- Visualization dan interpretation methods
- Reproducibility considerations

## 4.2 HASIL

### 4.2.1 Dataset Analysis Results
#### ******* Descriptive Statistics
- Dataset characteristics summary
- Variable distribution analysis
- Temporal pattern findings
- Data quality assessment results

#### ******* Fatigue Risk Distribution
- Classification distribution results
- Temporal fatigue patterns
- Peak fatigue period identification
- Validation results

### 4.2.2 Feature Engineering Results
#### ******* Derived Features Performance
- Feature creation success rates
- Feature quality metrics
- Correlation dengan target variable
- Feature stability analysis

#### ******* Text Processing Results
- Text feature extraction results
- Language pattern analysis findings
- Vocabulary analysis results
- Text-based prediction capability

#### ******* Feature Selection Results
- Feature importance rankings
- Multicollinearity analysis results
- Optimal feature set identification
- Data leakage prevention validation

### 4.2.3 Classification Model Results
#### ******* Baseline Model Performance
- Random Forest baseline results
- SVM baseline performance
- Neural Network baseline metrics
- Cross-validation results comparison

#### 4.2.3.2 Hyperparameter Optimization Results
- Grid search optimization results
- Optimal parameter combinations
- Performance improvement analysis
- Computational efficiency analysis

#### 4.2.3.3 Class Imbalance Handling Results
- SMOTE implementation results
- Balanced accuracy improvements
- Precision-recall trade-offs
- Minority class performance enhancement

### 4.2.4 Ablation Study Results
#### 4.2.4.1 Feature Impact Analysis
- Individual feature removal impact
- Feature importance hierarchy
- Critical feature identification
- Performance degradation analysis

#### 4.2.4.2 Feature Group Analysis Results
- Physical activity features impact
- Productivity features contribution
- Gamification features effectiveness
- Text-based features performance

#### 4.2.4.3 Optimal Feature Set Results
- Minimal viable feature set
- Performance vs complexity trade-off
- Recommended feature combinations
- Implementation considerations

### 4.2.5 Specialized Analysis Results
#### 4.2.5.1 Title-Only Classification Results
- Pure text-based prediction performance
- Comparison dengan full feature model
- Practical applicability assessment
- Implementation advantages

#### 4.2.5.2 Bias Correction Results
- Bias detection findings
- Bias correction effectiveness
- Fairness metrics improvement
- Generalizability enhancement

#### 4.2.5.3 Model Validation Results
- Cross-validation performance
- External validation results
- Robustness testing outcomes
- Generalizability assessment

### 4.2.6 Advanced Model Performance
#### 4.2.6.1 Ensemble Methods Results
- Model combination performance
- Voting classifier results
- Stacking ensemble outcomes
- Ensemble vs individual comparison

#### 4.2.6.2 Model Interpretability Results
- SHAP value analysis results
- Feature importance visualization
- Decision boundary analysis
- Model explanation insights

#### 4.2.6.3 Production Model Results
- Final model selection rationale
- Performance-interpretability balance
- Deployment readiness assessment
- Maintenance requirements

### 4.2.7 Gamification Impact Results
#### 4.2.7.1 Gamification Balance Analysis
- Optimal balance point identification
- Threshold analysis results
- Sustainability pattern findings
- Long-term impact assessment

#### 4.2.7.2 Achievement Rate Impact
- Achievement level effect analysis
- Optimal challenge-skill balance
- Motivation vs fatigue trade-offs
- Personalization opportunities

#### 4.2.7.3 Implementation Guidelines
- Practical strategy recommendations
- Risk mitigation approaches
- Monitoring indicator results
- Intervention threshold identification

### 4.2.8 Statistical Validation Results
#### 4.2.8.1 Significance Testing Results
- Model comparison statistical tests
- Confidence interval analysis
- Effect size assessment
- Practical significance evaluation

#### 4.2.8.2 Robustness Analysis Results
- Sensitivity analysis outcomes
- Noise tolerance assessment
- Missing data impact results
- Edge case performance

#### 4.2.8.3 Real-world Applicability Results
- Implementation feasibility assessment
- Resource requirement analysis
- Scalability evaluation
- Integration challenge identification

### 4.2.9 Comparative Analysis Results
#### 4.2.9.1 Literature Comparison
- Performance vs existing studies
- Methodological advantage identification
- Novel contribution assessment
- Limitation acknowledgment

#### 4.2.9.2 Baseline Method Comparison
- Rule-based approach comparison
- Traditional method performance
- Expert prediction comparison
- Random baseline results

#### 4.2.9.3 Cost-Benefit Analysis Results
- Implementation cost assessment
- Accuracy vs complexity evaluation
- Maintenance overhead analysis
- ROI projection results
