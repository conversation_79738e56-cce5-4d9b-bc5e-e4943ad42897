#!/usr/bin/env python3
"""
Script untuk generate gambar alur metodologi penelitian
yang mencerminkan sequential process dan data leakage prevention
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_methodology_flowchart():
    """Create comprehensive methodology flowchart"""
    
    # Create figure with larger size
    fig, ax = plt.subplots(1, 1, figsize=(16, 20))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 24)
    ax.axis('off')
    
    # Define colors
    colors = {
        'data_collection': '#E3F2FD',      # Light Blue
        'critical_phase': '#FFEBEE',       # Light Red
        'valid_process': '#E8F5E8',        # Light Green
        'invalid_process': '#FFCDD2',      # Light Red
        'best_performance': '#C8E6C9',     # Green
        'production': '#DCEDC8'            # Light Green
    }
    
    # Title
    ax.text(5, 23, 'METODOLOGI PENELITIAN\nSequential Process & Data Leakage Prevention', 
            ha='center', va='center', fontsize=16, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray'))
    
    # Phase 1: Data Collection & Preprocessing
    phase1_y = 21
    ax.text(5, phase1_y + 0.5, 'PHASE 1: DATA COLLECTION & PREPROCESSING', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Strava and Pomokit boxes
    strava_box = FancyBboxPatch((1, phase1_y-0.5), 3, 0.8, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['data_collection'], 
                                edgecolor='black', linewidth=1)
    ax.add_patch(strava_box)
    ax.text(2.5, phase1_y-0.1, 'Strava API\nCardiovascular Data', 
            ha='center', va='center', fontsize=10)
    
    pomokit_box = FancyBboxPatch((6, phase1_y-0.5), 3, 0.8, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['data_collection'], 
                                 edgecolor='black', linewidth=1)
    ax.add_patch(pomokit_box)
    ax.text(7.5, phase1_y-0.1, 'Pomokit API\nProductivity Data', 
            ha='center', va='center', fontsize=10)
    
    # Data preprocessing
    preprocess_box = FancyBboxPatch((3.5, phase1_y-2), 3, 0.8, 
                                    boxstyle="round,pad=0.1", 
                                    facecolor=colors['data_collection'], 
                                    edgecolor='black', linewidth=1)
    ax.add_patch(preprocess_box)
    ax.text(5, phase1_y-1.6, 'Data Preprocessing\n& Integration', 
            ha='center', va='center', fontsize=10)
    
    # Arrows for Phase 1
    ax.arrow(2.5, phase1_y-0.9, 1.5, -0.6, head_width=0.1, head_length=0.1, fc='black', ec='black')
    ax.arrow(7.5, phase1_y-0.9, -1.5, -0.6, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    # Phase 2: Independent Labeling (CRITICAL)
    phase2_y = 18
    critical_box = FancyBboxPatch((2, phase2_y), 6, 1.2, 
                                  boxstyle="round,pad=0.2", 
                                  facecolor=colors['critical_phase'], 
                                  edgecolor='red', linewidth=3)
    ax.add_patch(critical_box)
    ax.text(5, phase2_y+0.6, 'PHASE 2: INDEPENDENT LABELING (CRITICAL)', 
            ha='center', va='center', fontsize=12, fontweight='bold', color='red')
    
    # External labeling methods
    external_methods = [
        'External Independent\nLabeling',
        'Temporal Patterns\nAnalysis', 
        'Expert Simulation\nRules',
        'Domain Knowledge\nApplication'
    ]
    
    for i, method in enumerate(external_methods):
        x_pos = 1 + i * 2
        method_box = FancyBboxPatch((x_pos, phase2_y-1.5), 1.8, 0.8, 
                                    boxstyle="round,pad=0.1", 
                                    facecolor=colors['valid_process'], 
                                    edgecolor='green', linewidth=1)
        ax.add_patch(method_box)
        ax.text(x_pos+0.9, phase2_y-1.1, method, 
                ha='center', va='center', fontsize=9)
    
    # Target Recreation Testing
    recreation_box = FancyBboxPatch((3.5, phase2_y-3), 3, 0.8, 
                                    boxstyle="round,pad=0.1", 
                                    facecolor='orange', 
                                    edgecolor='black', linewidth=2)
    ax.add_patch(recreation_box)
    ax.text(5, phase2_y-2.6, 'Target Recreation Testing\n>70% Success = LEAKAGE', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Decision Diamond
    diamond_y = phase2_y - 4.5
    diamond = patches.RegularPolygon((5, diamond_y), 4, radius=0.8, 
                                     orientation=np.pi/4, 
                                     facecolor='yellow', 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(diamond)
    ax.text(5, diamond_y, 'Leakage\nDetected?', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Invalid Pipeline (main4.py)
    invalid_box = FancyBboxPatch((0.5, diamond_y-1.5), 3, 1, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=colors['invalid_process'], 
                                 edgecolor='red', linewidth=2)
    ax.add_patch(invalid_box)
    ax.text(2, diamond_y-1, '❌ INVALID PIPELINE\nmain4.py: 96.67%\nArtificial Performance', 
            ha='center', va='center', fontsize=9, color='red', fontweight='bold')
    
    # Valid Labels
    valid_box = FancyBboxPatch((6.5, diamond_y-1.5), 3, 1, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['valid_process'], 
                               edgecolor='green', linewidth=2)
    ax.add_patch(valid_box)
    ax.text(8, diamond_y-1, '✅ VALID LABELS\nLeak-Free\nIndependent Target', 
            ha='center', va='center', fontsize=9, color='green', fontweight='bold')
    
    # Arrows from diamond
    ax.arrow(4.2, diamond_y, -1.5, -0.8, head_width=0.1, head_length=0.1, fc='red', ec='red')
    ax.text(3, diamond_y-0.3, 'YES', ha='center', va='center', fontsize=9, color='red', fontweight='bold')
    
    ax.arrow(5.8, diamond_y, 1.5, -0.8, head_width=0.1, head_length=0.1, fc='green', ec='green')
    ax.text(7, diamond_y-0.3, 'NO', ha='center', va='center', fontsize=9, color='green', fontweight='bold')
    
    # Phase 3: Leak-Free Feature Engineering
    phase3_y = 10
    ax.text(5, phase3_y+0.5, 'PHASE 3: LEAK-FREE FEATURE ENGINEERING', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    feature_eng_box = FancyBboxPatch((3.5, phase3_y-0.5), 3, 0.8, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['valid_process'], 
                                     edgecolor='green', linewidth=1)
    ax.add_patch(feature_eng_box)
    ax.text(5, phase3_y-0.1, 'Strict Feature Filtering\n& Independence Validation', 
            ha='center', va='center', fontsize=10)
    
    # Phase 4: Multiple Pipeline Comparison
    phase4_y = 8
    ax.text(5, phase4_y+0.5, 'PHASE 4: MULTIPLE PIPELINE COMPARISON', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Pipeline boxes
    pipelines = [
        ('main3.py\nStandard Ablation\n58-61%', colors['valid_process'], 'green'),
        ('main5.py\nRFE Bias-Corrected\n69-71%', colors['valid_process'], 'orange'),
        ('main6.py\nRFE Title-Only\n65% (35% Overfitting)', colors['invalid_process'], 'orange'),
        ('main7.py\nSHAP Title-Only\n68.33% ⭐', colors['best_performance'], 'blue'),
        ('main_leak_free.py\nExternal Labels\n60-61%', colors['production'], 'green')
    ]
    
    for i, (pipeline_text, color, edge_color) in enumerate(pipelines):
        x_pos = 0.5 + i * 1.8
        pipeline_box = FancyBboxPatch((x_pos, phase4_y-1), 1.6, 1.2, 
                                      boxstyle="round,pad=0.1", 
                                      facecolor=color, 
                                      edgecolor=edge_color, linewidth=1)
        ax.add_patch(pipeline_box)
        ax.text(x_pos+0.8, phase4_y-0.4, pipeline_text, 
                ha='center', va='center', fontsize=8)
    
    # SHAP Feature Importance
    shap_y = 5
    ax.text(5, shap_y+0.5, 'SHAP FEATURE IMPORTANCE ANALYSIS', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Top features
    features = [
        'Physical Activity\n23.84%\n(avg_distance_km +\ntotal_distance_km)',
        'Time Investment\n12.33%\n(total_time_minutes)',
        'Work Patterns\n17.34%\n(total_cycles +\nwork_days)'
    ]
    
    for i, feature in enumerate(features):
        x_pos = 1.5 + i * 2.5
        feature_box = FancyBboxPatch((x_pos, shap_y-1), 2.2, 1.2, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['best_performance'], 
                                     edgecolor='blue', linewidth=1)
        ax.add_patch(feature_box)
        ax.text(x_pos+1.1, shap_y-0.4, feature, 
                ha='center', va='center', fontsize=9, fontweight='bold')
    
    # Production Deployment
    prod_y = 2.5
    ax.text(5, prod_y+0.5, 'PRODUCTION DEPLOYMENT FRAMEWORK', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    prod_components = [
        'Real-time\nMonitoring',
        'Multi-level\nAlert System', 
        'Personalized\nInterventions',
        'Continuous\nValidation'
    ]
    
    for i, component in enumerate(prod_components):
        x_pos = 1 + i * 2
        comp_box = FancyBboxPatch((x_pos, prod_y-0.8), 1.8, 0.8, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['production'], 
                                  edgecolor='green', linewidth=1)
        ax.add_patch(comp_box)
        ax.text(x_pos+0.9, prod_y-0.4, component, 
                ha='center', va='center', fontsize=9)
    
    # Main flow arrows
    arrow_positions = [
        (5, phase1_y-2.8, 0, -1.2),  # Phase 1 to Phase 2
        (8, diamond_y-2.5, -2.5, -2),  # Valid to Phase 3
        (5, phase3_y-1.3, 0, -1.2),  # Phase 3 to Phase 4
        (5, phase4_y-2.2, 0, -1.3),  # Phase 4 to SHAP
        (5, shap_y-1.8, 0, -1.2)   # SHAP to Production
    ]
    
    for x, y, dx, dy in arrow_positions:
        ax.arrow(x, y, dx, dy, head_width=0.15, head_length=0.15, 
                fc='black', ec='black', linewidth=2)
    
    # Legend
    legend_y = 0.5
    ax.text(0.5, legend_y, 'LEGEND:', fontsize=10, fontweight='bold')
    
    legend_items = [
        ('Data Collection', colors['data_collection']),
        ('Critical Phase', colors['critical_phase']),
        ('Valid Process', colors['valid_process']),
        ('Invalid Process', colors['invalid_process']),
        ('Best Performance', colors['best_performance']),
        ('Production Ready', colors['production'])
    ]
    
    for i, (label, color) in enumerate(legend_items):
        x_pos = 0.5 + (i % 3) * 3
        y_pos = legend_y - 0.3 - (i // 3) * 0.3
        
        legend_box = FancyBboxPatch((x_pos, y_pos-0.1), 0.3, 0.2, 
                                    boxstyle="round,pad=0.02", 
                                    facecolor=color, 
                                    edgecolor='black', linewidth=0.5)
        ax.add_patch(legend_box)
        ax.text(x_pos+0.4, y_pos, label, fontsize=8, va='center')
    
    plt.tight_layout()
    return fig

def create_ml_pipeline_comparison():
    """Create ML Pipeline Comparison Architecture diagram"""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # Title
    ax.text(5, 11.5, 'MULTIPLE MACHINE LEARNING PIPELINE COMPARISON', 
            ha='center', va='center', fontsize=16, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray'))
    
    # Input Data
    input_box = FancyBboxPatch((4, 10), 2, 0.8, 
                               boxstyle="round,pad=0.1", 
                               facecolor='lightblue', 
                               edgecolor='black', linewidth=1)
    ax.add_patch(input_box)
    ax.text(5, 10.4, 'Integrated Dataset\n300 observations', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    # Pipeline branches
    pipelines = [
        ('main3.py', 'Standard Ablation', '58-61%', 'Valid', 'lightgreen'),
        ('main4.py', 'RFE Regular', '96.67%', 'INVALID', 'lightcoral'),
        ('main5.py', 'RFE Bias-Corrected', '69-71%', 'Caution', 'lightyellow'),
        ('main6.py', 'RFE Title-Only', '65%', 'Caution', 'lightyellow'),
        ('main7.py', 'SHAP Title-Only', '68.33%', 'BEST', 'lightgreen'),
        ('main_leak_free.py', 'External Labels', '60-61%', 'Production', 'lightgreen')
    ]
    
    y_positions = [8.5, 8.5, 7, 7, 5.5, 5.5]
    x_positions = [1, 5.5, 1, 5.5, 1, 5.5]
    
    for i, (name, method, accuracy, status, color) in enumerate(pipelines):
        x_pos = x_positions[i]
        y_pos = y_positions[i]
        
        # Pipeline box
        pipeline_box = FancyBboxPatch((x_pos, y_pos), 3, 1.2, 
                                      boxstyle="round,pad=0.1", 
                                      facecolor=color, 
                                      edgecolor='black', linewidth=1)
        ax.add_patch(pipeline_box)
        
        # Status indicator
        if status == 'INVALID':
            status_color = 'red'
            status_symbol = '❌'
        elif status == 'BEST':
            status_color = 'blue'
            status_symbol = '⭐'
        elif status == 'Production':
            status_color = 'green'
            status_symbol = '✅'
        elif status == 'Caution':
            status_color = 'orange'
            status_symbol = '⚠️'
        else:
            status_color = 'green'
            status_symbol = '✅'
        
        ax.text(x_pos+1.5, y_pos+0.6, f'{status_symbol} {name}', 
                ha='center', va='center', fontsize=10, fontweight='bold')
        ax.text(x_pos+1.5, y_pos+0.3, method, 
                ha='center', va='center', fontsize=9)
        ax.text(x_pos+1.5, y_pos, f'Accuracy: {accuracy}', 
                ha='center', va='center', fontsize=9, color=status_color, fontweight='bold')
        
        # Arrow from input
        ax.arrow(5, 9.8, x_pos+1.5-5, y_pos+1.2-9.8, 
                head_width=0.1, head_length=0.1, fc='gray', ec='gray', alpha=0.7)
    
    # Results summary
    results_box = FancyBboxPatch((2, 3), 6, 1.5, 
                                 boxstyle="round,pad=0.2", 
                                 facecolor='lightcyan', 
                                 edgecolor='blue', linewidth=2)
    ax.add_patch(results_box)
    ax.text(5, 3.75, 'KEY FINDINGS', 
            ha='center', va='center', fontsize=12, fontweight='bold', color='blue')
    ax.text(5, 3.25, '• Data Leakage Detected: main4.py (96.67% → 60-68%)\n• SHAP Analysis Superior: main7.py (68.33% + Interpretability)\n• Production Ready: main_leak_free.py (Leak-free methodology)', 
            ha='center', va='center', fontsize=10)
    
    # Best pipeline highlight
    best_box = FancyBboxPatch((3, 1), 4, 1.2, 
                              boxstyle="round,pad=0.2", 
                              facecolor='gold', 
                              edgecolor='blue', linewidth=3)
    ax.add_patch(best_box)
    ax.text(5, 1.6, '🏆 RECOMMENDED PIPELINE', 
            ha='center', va='center', fontsize=12, fontweight='bold', color='blue')
    ax.text(5, 1.2, 'main7.py: SHAP Title-Only Analysis\n68.33% Accuracy + Business Interpretability', 
            ha='center', va='center', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    return fig

if __name__ == "__main__":
    # Create directories
    import os
    os.makedirs('laporan-akhir/figures', exist_ok=True)
    
    # Generate methodology flowchart
    print("🎨 Generating Methodology Flowchart...")
    fig1 = create_methodology_flowchart()
    fig1.savefig('laporan-akhir/figures/gambar_3_1_metodologi_flowchart_revised.png', 
                 dpi=300, bbox_inches='tight', facecolor='white')
    print("✅ Saved: gambar_3_1_metodologi_flowchart_revised.png")
    
    # Generate ML pipeline comparison
    print("🎨 Generating ML Pipeline Comparison...")
    fig2 = create_ml_pipeline_comparison()
    fig2.savefig('laporan-akhir/figures/gambar_3_2_ml_pipeline_comparison.png', 
                 dpi=300, bbox_inches='tight', facecolor='white')
    print("✅ Saved: gambar_3_2_ml_pipeline_comparison.png")
    
    plt.show()
    
    print("\n🎉 GAMBAR ALUR METODOLOGI BERHASIL DIBUAT!")
    print("📁 Location: laporan-akhir/figures/")
    print("📊 Files:")
    print("   • gambar_3_1_metodologi_flowchart_revised.png")
    print("   • gambar_3_2_ml_pipeline_comparison.png")
