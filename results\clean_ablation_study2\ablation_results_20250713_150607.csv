experiment_name,features_used,feature_count,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
Baseline_All_Features,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",18,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
Without_avg_distance_km,"['activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6599999999999999,0.04027681991198192,0.6399768600306711,0.05459019812195859,0.6501189768207369,0.648383200872682,0.7275,0.06750000000000012,success
Without_activity_points,"['avg_distance_km', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6566666666666666,0.0522812904711937,0.6422767700526977,0.07259280470305109,0.6489436755824672,0.6551215521271622,0.7224999999999999,0.0658333333333333,success
Without_total_title_diversity,"['avg_distance_km', 'activity_points', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.725,0.06833333333333336,success
Without_work_days,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6533333333333333,0.047609522856952316,0.6319817198039898,0.05906748287707736,0.6409854918698079,0.6440104410160511,0.7275,0.07416666666666671,success
Without_total_distance_km,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666666,0.039440531887330786,0.6462927946699435,0.051509911893317224,0.6574656716378122,0.653383200872682,0.7258333333333333,0.0591666666666667,success
Without_total_time_minutes,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666666,0.04714045207910315,0.6452491486667962,0.0538909874298222,0.6551745923097514,0.6528276453171264,0.7283333333333333,0.06166666666666665,success
Without_total_cycles,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6533333333333333,0.047609522856952316,0.6319817198039898,0.05906748287707736,0.6409854918698079,0.6440104410160511,0.7275,0.07416666666666671,success
Without_avg_time_minutes,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666666,0.04714045207910315,0.6452491486667962,0.0538909874298222,0.6551745923097514,0.6528276453171264,0.7275,0.060833333333333406,success
Without_pomokit_title_count,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6533333333333333,0.047609522856952316,0.6319817198039898,0.05906748287707736,0.6409854918698079,0.6440104410160511,0.7275,0.07416666666666671,success
Without_title_balance_ratio,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666666,0.05676462121975463,0.6500965855379032,0.07528601087060688,0.6546212569598457,0.6616448496182016,0.7266666666666667,0.06000000000000005,success
Without_productivity_points,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666666,0.04594682917363405,0.6469427473261854,0.060863523949172105,0.6550775230908267,0.6624154589371981,0.7241666666666666,0.057499999999999996,success
Without_strava_unique_words,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6599999999999999,0.038873012632301994,0.638318010175323,0.05779604704552808,0.6420141052884462,0.6590104410160511,0.7216666666666667,0.06166666666666676,success
Without_activity_days,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7300000000000001,0.07333333333333347,success
Without_pomokit_unique_words,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'gamification_balance', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7258333333333333,0.06916666666666671,success
Without_gamification_balance,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'consistency_score', 'achievement_rate', 'strava_title_count']",17,0.6666666666666667,0.04216370213557837,0.6473203175222655,0.06597998077982554,0.6564198723041883,0.6538670718404238,0.7300000000000001,0.06333333333333335,success
Without_consistency_score,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'achievement_rate', 'strava_title_count']",17,0.6633333333333333,0.04988876515698586,0.6448804781232422,0.07031510728035183,0.6501843316033802,0.6601215521271622,0.7266666666666668,0.06333333333333346,success
Without_achievement_rate,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'strava_title_count']",17,0.6633333333333333,0.04988876515698586,0.6448804781232422,0.07031510728035183,0.6501843316033802,0.6601215521271622,0.725,0.06166666666666665,success
Without_strava_title_count,"['avg_distance_km', 'activity_points', 'total_title_diversity', 'work_days', 'total_distance_km', 'total_time_minutes', 'total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'activity_days', 'pomokit_unique_words', 'gamification_balance', 'consistency_score', 'achievement_rate']",17,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7300000000000001,0.07333333333333347,success
Optimal_Feature_Set,"['total_cycles', 'work_days', 'pomokit_title_count', 'activity_points', 'activity_days', 'total_title_diversity', 'pomokit_unique_words', 'strava_title_count', 'avg_distance_km', 'strava_unique_words', 'achievement_rate', 'consistency_score', 'total_distance_km', 'total_time_minutes', 'title_balance_ratio', 'avg_time_minutes', 'productivity_points', 'gamification_balance']",18,0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
