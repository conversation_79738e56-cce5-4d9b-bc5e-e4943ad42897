"""
Simple test for main8.py data processing
"""

import sys
sys.path.append('src')

from data_processor import DataProcessor

def test_data_processor():
    """Test DataProcessor methods"""
    print("🧪 Testing DataProcessor...")
    
    dp = DataProcessor()
    
    # Check available methods
    methods = [method for method in dir(dp) if not method.startswith('_')]
    print("Available methods:")
    for method in methods:
        print(f"  - {method}")
    
    # Test process_all method
    try:
        print("\n🚀 Testing process_all method...")
        result = dp.process_all()
        print(f"✅ process_all worked! Got {len(result)} records")
        return True
    except Exception as e:
        print(f"❌ process_all failed: {e}")
        return False

if __name__ == "__main__":
    test_data_processor()
