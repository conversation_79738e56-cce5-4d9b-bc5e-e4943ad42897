"""
Main Feature Analysis Pipeline
Dashboard utama untuk seleksi fitur dan analisis kontribusi yang komprehensif

TUJUAN:
1. Memberikan sistem seleksi fitur yang mudah dipahami dan digunakan
2. Menganalisis kontribusi setiap fitur terhadap prediksi
3. Memberikan rekomendasi fitur optimal untuk model
4. Menyediakan visualisasi yang informatif dan actionable

FITUR UTAMA:
- Comprehensive Feature Selection (RFE + Permutation + SHAP + Statistical)
- Feature Contribution Analysis
- Model Performance Evaluation
- Interactive Visualizations
- Actionable Recommendations

USAGE:
python main_feature_analysis.py --data dataset/processed/safe_ml_title_only_dataset.csv --target title_fatigue_risk
python main_feature_analysis.py --quick-analysis  # Untuk analisis cepat
python main_feature_analysis.py --visualize-only  # Hanya buat visualisasi
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Add src to path
sys.path.append('src')

from comprehensive_feature_selector import ComprehensiveFeatureSelector
from feature_contribution_analyzer import FeatureContributionAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('feature_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class FeatureAnalysisDashboard:
    """
    Dashboard utama untuk analisis fitur komprehensif
    """
    
    def __init__(self, random_state: int = 42):
        """Initialize dashboard"""
        self.random_state = random_state
        self.selector = None
        self.analyzer = None
        self.results = None
        
        logger.info("🚀 Feature Analysis Dashboard initialized")
    
    def run_comprehensive_analysis(self, data_path: str, target_column: str) -> Dict:
        """Run comprehensive feature analysis"""
        logger.info("="*80)
        logger.info("🔍 COMPREHENSIVE FEATURE SELECTION & CONTRIBUTION ANALYSIS")
        logger.info("="*80)
        
        # Phase 1: Feature Selection
        logger.info("Phase 1: Comprehensive Feature Selection")
        self.selector = ComprehensiveFeatureSelector(random_state=self.random_state)
        self.selector.load_data(data_path, target_column)
        
        # Run complete analysis
        self.results = self.selector.run_complete_analysis()
        
        # Phase 2: Contribution Analysis
        logger.info("Phase 2: Feature Contribution Analysis")
        self.analyzer = FeatureContributionAnalyzer(self.results)
        
        # Phase 3: Generate Visualizations
        logger.info("Phase 3: Creating Visualizations")
        
        # Create feature importance visualization
        importance_viz = self.analyzer.create_feature_importance_visualization()
        
        # Create correlation analysis
        correlation_viz = self.analyzer.create_feature_correlation_analysis(self.selector.X)
        
        # Phase 4: Save Results
        logger.info("Phase 4: Saving Results")
        results_file, report_file = self.selector.save_results("comprehensive_feature_analysis")
        
        # Generate contribution summary
        contribution_summary = self.analyzer.generate_contribution_summary()
        
        # Save contribution summary
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = f"results/comprehensive_feature_analysis/contribution_summary_{timestamp}.txt"
        Path(summary_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(contribution_summary)
        
        logger.info("✅ Comprehensive Analysis Completed!")
        logger.info(f"📊 Results: {results_file}")
        logger.info(f"📋 Report: {report_file}")
        logger.info(f"📈 Visualizations: {importance_viz}")
        logger.info(f"🔗 Correlation: {correlation_viz}")
        logger.info(f"📝 Summary: {summary_file}")
        
        return {
            'results_file': results_file,
            'report_file': report_file,
            'importance_visualization': importance_viz,
            'correlation_visualization': correlation_viz,
            'contribution_summary': summary_file,
            'analysis_results': self.results
        }
    
    def run_quick_analysis(self, data_path: str, target_column: str) -> Dict:
        """Run quick analysis (only essential methods)"""
        logger.info("⚡ Running Quick Feature Analysis...")
        
        self.selector = ComprehensiveFeatureSelector(random_state=self.random_state)
        self.selector.load_data(data_path, target_column)
        
        # Run only essential analyses
        logger.info("Running RFE Analysis...")
        self.selector.run_rfe_analysis()
        
        logger.info("Running Permutation Analysis...")
        self.selector.run_permutation_analysis()
        
        logger.info("Running Statistical Analysis...")
        self.selector.run_statistical_analysis()
        
        logger.info("Creating Consensus Ranking...")
        self.selector.create_consensus_ranking()
        
        logger.info("Evaluating Model Performance...")
        self.selector.evaluate_model_performance()
        
        # Store results
        self.results = self.selector.results
        
        # Save results
        results_file, report_file = self.selector.save_results("quick_feature_analysis")
        
        logger.info("✅ Quick Analysis Completed!")
        return {
            'results_file': results_file,
            'report_file': report_file,
            'analysis_results': self.results
        }
    
    def print_summary(self):
        """Print analysis summary to console"""
        if not self.results:
            logger.warning("No results available. Run analysis first.")
            return
        
        print("\n" + "="*80)
        print("🎯 FEATURE ANALYSIS SUMMARY")
        print("="*80)
        
        # Dataset info
        if 'consensus_ranking' in self.results:
            total_features = len(self.results['consensus_ranking'])
            print(f"📊 Total Features Analyzed: {total_features}")
        
        # Top 5 features
        print("\n🏆 TOP 5 FEATURES:")
        if 'consensus_ranking' in self.results:
            for i, item in enumerate(self.results['consensus_ranking'][:5]):
                print(f"   {i+1}. {item['feature']}")
                print(f"      • Consensus Score: {item['consensus_score']:.4f}")
                print(f"      • RFE Selection Rate: {item['rfe_score']:.2f}")
                print(f"      • Statistical Rank: {item['stat_rank']}")
                print()
        
        # Best model performance
        print("📈 BEST MODEL PERFORMANCE:")
        if 'model_performance' in self.results:
            best_performance = 0
            best_config = None
            
            for n_features, perf_data in self.results['model_performance'].items():
                for model_key, model_scores in perf_data['model_scores'].items():
                    if model_scores['cv_mean'] > best_performance:
                        best_performance = model_scores['cv_mean']
                        best_config = {
                            'features': n_features,
                            'model': model_scores['model_name'],
                            'cv_score': model_scores['cv_mean'],
                            'cv_std': model_scores['cv_std'],
                            'test_score': model_scores['test_score']
                        }
            
            if best_config:
                print(f"   • Best Model: {best_config['model']}")
                print(f"   • Optimal Features: {best_config['features']}")
                print(f"   • CV Accuracy: {best_config['cv_score']:.4f} ± {best_config['cv_std']:.4f}")
                print(f"   • Test Accuracy: {best_config['test_score']:.4f}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        if 'consensus_ranking' in self.results and 'model_performance' in self.results:
            # Find optimal feature count
            best_performance = 0
            best_count = 0
            for n_features, perf_data in self.results['model_performance'].items():
                best_cv = max([s['cv_mean'] for s in perf_data['model_scores'].values()])
                if best_cv > best_performance:
                    best_performance = best_cv
                    best_count = n_features
            
            optimal_features = [item['feature'] for item in self.results['consensus_ranking'][:best_count]]
            
            print(f"   ✅ Use {best_count} features for optimal performance")
            print(f"   ✅ Expected accuracy: {best_performance:.4f}")
            print(f"   ✅ Recommended features:")
            for i, feature in enumerate(optimal_features[:10]):  # Show top 10
                print(f"      {i+1:2d}. {feature}")
            if len(optimal_features) > 10:
                print(f"      ... and {len(optimal_features) - 10} more features")
        
        print("\n" + "="*80)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Comprehensive Feature Analysis Dashboard')
    parser.add_argument('--data', type=str, 
                       default='dataset/processed/safe_ml_title_only_dataset.csv',
                       help='Path to dataset')
    parser.add_argument('--target', type=str, 
                       default='title_fatigue_risk',
                       help='Target column name')
    parser.add_argument('--quick-analysis', action='store_true',
                       help='Run quick analysis (skip SHAP)')
    parser.add_argument('--visualize-only', action='store_true',
                       help='Only create visualizations from existing results')
    parser.add_argument('--random-state', type=int, default=42,
                       help='Random state for reproducibility')
    
    args = parser.parse_args()
    
    try:
        dashboard = FeatureAnalysisDashboard(random_state=args.random_state)
        
        if args.visualize_only:
            print("🎨 Visualization-only mode not implemented yet")
            print("💡 Run full analysis to generate visualizations")
            return
        
        # Check if data file exists
        if not Path(args.data).exists():
            logger.error(f"❌ Data file not found: {args.data}")
            print(f"\n❌ Error: Data file not found: {args.data}")
            print("💡 Available datasets:")
            dataset_dir = Path("dataset/processed")
            if dataset_dir.exists():
                for file in dataset_dir.glob("*.csv"):
                    print(f"   • {file}")
            return
        
        # Run analysis
        if args.quick_analysis:
            print("⚡ Running Quick Feature Analysis...")
            results = dashboard.run_quick_analysis(args.data, args.target)
        else:
            print("🔍 Running Comprehensive Feature Analysis...")
            results = dashboard.run_comprehensive_analysis(args.data, args.target)
        
        # Print summary
        dashboard.print_summary()
        
        print(f"\n🎉 Feature Analysis Completed Successfully!")
        print(f"📁 Check results in: results/comprehensive_feature_analysis/")
        
    except KeyboardInterrupt:
        print("\n❌ Analysis interrupted by user")
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        print(f"\n❌ Analysis failed: {str(e)}")
        print("💡 Check the log file for detailed error information")


if __name__ == "__main__":
    main()
