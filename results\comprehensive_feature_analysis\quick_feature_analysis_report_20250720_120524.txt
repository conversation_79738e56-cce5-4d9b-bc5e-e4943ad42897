================================================================================
🔍 COMPREHENSIVE FEATURE SELECTION & CONTRIBUTION ANALYSIS
================================================================================

📋 DATASET INFORMATION:
   • Total Features: 18
   • Total Samples: 300
   • Target Classes: 3

🏆 TOP 10 FEATURES (CONSENSUS RANKING):
    1. total_cycles
       • Consensus Score: 0.5990
       • RFE Selection Rate: 0.67
       • Avg Permutation Rank: 7.7
       • Statistical Rank: 1

    2. pomokit_title_count
       • Consensus Score: 0.5357
       • RFE Selection Rate: 1.00
       • Avg Permutation Rank: 9.3
       • Statistical Rank: 2

    3. consistency_score
       • Consensus Score: 0.4385
       • RFE Selection Rate: 1.00
       • Avg Permutation Rank: 8.7
       • Statistical Rank: 5

    4. title_balance_ratio
       • Consensus Score: 0.3972
       • RFE Selection Rate: 1.00
       • Avg Permutation Rank: 8.0
       • Statistical Rank: 15

    5. total_distance_km
       • Consensus Score: 0.3886
       • RFE Selection Rate: 1.00
       • Avg Permutation Rank: 9.7
       • Statistical Rank: 16

    6. work_days
       • Consensus Score: 0.3678
       • RFE Selection Rate: 0.67
       • Avg Permutation Rank: 9.7
       • Statistical Rank: 3

    7. avg_distance_km
       • Consensus Score: 0.3418
       • RFE Selection Rate: 0.67
       • Avg Permutation Rank: 3.3
       • Statistical Rank: 17

    8. strava_unique_words
       • Consensus Score: 0.2713
       • RFE Selection Rate: 0.67
       • Avg Permutation Rank: 15.7
       • Statistical Rank: 12

    9. total_time_minutes
       • Consensus Score: 0.2704
       • RFE Selection Rate: 0.67
       • Avg Permutation Rank: 13.7
       • Statistical Rank: 14

   10. activity_points
       • Consensus Score: 0.2082
       • RFE Selection Rate: 0.33
       • Avg Permutation Rank: 4.7
       • Statistical Rank: 13

📊 MODEL PERFORMANCE BY FEATURE COUNT:
   🔹 5 Features:
      • Best Model: Gradient Boosting
      • CV Accuracy: 0.6167 ± 0.0486
      • Test Accuracy: 0.5667

   🔹 10 Features:
      • Best Model: Gradient Boosting
      • CV Accuracy: 0.6375 ± 0.0386
      • Test Accuracy: 0.5833

   🔹 15 Features:
      • Best Model: Random Forest
      • CV Accuracy: 0.6458 ± 0.0574
      • Test Accuracy: 0.6667

   🔹 18 Features:
      • Best Model: Random Forest
      • CV Accuracy: 0.6458 ± 0.0697
      • Test Accuracy: 0.6167

🔍 METHOD-SPECIFIC INSIGHTS:
   📈 RFE Analysis:
      • Logistic Regression: 10 features, Score: 0.6500
      • Random Forest: 15 features, Score: 0.6500
      • Gradient Boosting: 5 features, Score: 0.5667

   🎯 Permutation Importance:
      • Logistic Regression: Top = avg_distance_km (0.0883)
      • Random Forest: Top = title_balance_ratio (0.0350)
      • Gradient Boosting: Top = activity_points (0.0617)

💡 RECOMMENDATIONS:
   ✅ Optimal Feature Count: 15 features
   ✅ Expected Performance: 0.6458 accuracy

   🎯 Recommended Feature Set (15 features):
       1. total_cycles
       2. pomokit_title_count
       3. consistency_score
       4. title_balance_ratio
       5. total_distance_km
       6. work_days
       7. avg_distance_km
       8. strava_unique_words
       9. total_time_minutes
      10. activity_points
      11. strava_title_count
      12. activity_days
      13. achievement_rate
      14. pomokit_unique_words
      15. gamification_balance

================================================================================