"""
Test script for main8.py to check if it can run without errors
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all imports work"""
    print("🧪 Testing imports...")
    
    try:
        # Add src to path
        sys.path.append('src')
        
        # Test individual imports
        from data_processor import DataProcessor
        print("✅ DataProcessor imported successfully")
        
        from visualizer import Visualizer
        print("✅ Visualizer imported successfully")
        
        from title_only_fatigue_classifier import TitleOnlyFatigueClassifier
        print("✅ TitleOnlyFatigueClassifier imported successfully")
        
        from feature_filter3 import FeatureFilter3
        print("✅ FeatureFilter3 imported successfully")
        
        from shap_ablation_study import SHAPAblationStudy
        print("✅ SHAPAblationStudy imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_data_processor_methods():
    """Test if DataProcessor has required methods"""
    print("\n🧪 Testing DataProcessor methods...")
    
    try:
        sys.path.append('src')
        from data_processor import DataProcessor
        
        dp = DataProcessor()
        
        # Check if required methods exist
        required_methods = [
            'process_all',
            'load_raw_data', 
            'clean_strava_data',
            'clean_pomokit_data',
            'create_weekly_aggregation',
            'add_gamification_variables'
        ]
        
        for method in required_methods:
            if hasattr(dp, method):
                print(f"✅ {method} method exists")
            else:
                print(f"❌ {method} method missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ DataProcessor test failed: {e}")
        return False

def test_shap_dependencies():
    """Test SHAP dependencies"""
    print("\n🧪 Testing SHAP dependencies...")
    
    missing_deps = []
    
    try:
        import shap
        print("✅ shap library available")
    except ImportError:
        missing_deps.append("shap")
        print("❌ shap library missing")
    
    try:
        import matplotlib.pyplot as plt
        print("✅ matplotlib available")
    except ImportError:
        missing_deps.append("matplotlib")
        print("❌ matplotlib missing")
    
    try:
        import seaborn as sns
        print("✅ seaborn available")
    except ImportError:
        missing_deps.append("seaborn")
        print("❌ seaborn missing")
    
    if missing_deps:
        print(f"\n📦 Install missing dependencies:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True

def test_main8_class():
    """Test if main8.py class can be instantiated"""
    print("\n🧪 Testing main8.py class...")
    
    try:
        # Import main8 module
        import main8
        
        # Try to instantiate the pipeline class
        pipeline = main8.ComprehensiveSHAPAnalysisPipeline(include_ml=False)
        print("✅ ComprehensiveSHAPAnalysisPipeline instantiated successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ main8.py class test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 TESTING MAIN8.PY COMPATIBILITY")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("DataProcessor Methods Test", test_data_processor_methods),
        ("SHAP Dependencies Test", test_shap_dependencies),
        ("Main8 Class Test", test_main8_class)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! main8.py should work correctly.")
        print("You can now run: python main8.py")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues before running main8.py")
        
        if not test_shap_dependencies():
            print("\n💡 Quick fix for SHAP dependencies:")
            print("pip install shap matplotlib seaborn")

if __name__ == "__main__":
    main()
