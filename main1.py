"""
Complete Analysis Pipeline
Combines Physical Activity Analysis + Fatigue Prediction with Feature Filtering & Model Accuracy

UPDATED FOR FATIGUE_CLASSIFIER.PY:
- Integrates fatigue_classifier.py with feature_filter.py
- Prevents data leakage in ML models
- Provides safe datasets for training
- Supports multiple pipeline modes

PIPELINE MODES:
1. Complete Pipeline (default): Data Processing + Fatigue Prediction + ML
2. Fatigue Only (--fatigue-only): Fatigue Classification + Feature Filtering + ML
3. No ML (--no-ml): Data Processing only
4. ML Only (--ml-only): Advanced ML pipeline only

FEATURE FILTERING:
- Automatically removes features used for label creation
- Prevents data leakage in ML models
- Ensures model reliability and generalization
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from fatigue_classifier import FatigueRiskClassifier
from feature_filter1 import FeatureFilter
from clean_ablation_study import AblationStudy


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class CompleteAnalysisPipeline:
    """
    Complete analysis pipeline for fatigue prediction
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()
        
        if include_ml:
            self.fatigue_classifier = FatigueRiskClassifier()
            self.feature_filter = FeatureFilter()
        
        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_data_processing(self):
        """Run data processing only"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING")
        logger.info("="*60)

        # Step 1: Data Processing
        logger.info("Step 1: Processing raw data...")
        processed_data = self.data_processor.process_all()
        logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")

        return processed_data
    
    def run_fatigue_prediction(self, processed_data=None):
        """Run fatigue prediction with feature filtering and model accuracy"""
        logger.info("="*60)
        logger.info("PHASE 2: FATIGUE RISK PREDICTION WITH FEATURE FILTERING & ML MODELS")
        logger.info("="*60)

        # Step 1: Fatigue Classification
        logger.info("Step 1: Running fatigue classification...")
        classified_data = self.fatigue_classifier.process_fatigue_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )

        # Save classified data
        classified_output = "dataset/processed/fatigue_risk_classified_dataset.csv"
        classified_data.to_csv(classified_output, index=False)

        summary = self.fatigue_classifier.generate_classification_summary(classified_data)
        logger.info(f"✅ Fatigue classification completed. Distribution: {summary['fatigue_percentages']}")

        # Step 2: Feature Filtering for ML Safety
        logger.info("Step 2: Applying feature filtering to prevent data leakage...")
        safe_output = "dataset/processed/safe_ml_fatigue_dataset.csv"

        safe_path = self.feature_filter.create_safe_dataset_for_fatigue_classifier(
            classified_output,
            safe_output,
            target_column='fatigue_risk'
        )
        logger.info(f"✅ Feature filtering completed. Safe dataset: {safe_path}")

        # Step 3: Advanced ML Pipeline with Safe Dataset
        logger.info("Step 3: Running advanced ML pipeline with safe features...")

        # Use safe dataset for ML training with auto-detection
        # Check which safe datasets are available (prioritize current pipeline's output)
        safe_datasets = [
            {
                'path': safe_output,  # Current pipeline output (highest priority)
                'target': 'fatigue_risk',
                'name': 'Current Pipeline Output',
                'priority': 1
            },
            {
                'path': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
                'target': 'corrected_fatigue_risk',
                'name': 'Bias-Corrected Fatigue Classification',
                'priority': 2
            },
            {
                'path': 'dataset/processed/safe_ml_title_only_dataset.csv',
                'target': 'title_fatigue_risk',
                'name': 'Title-Only Fatigue Classification',
                'priority': 3
            }
        ]

        # Find the first available safe dataset
        selected_dataset = None
        for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
            if Path(dataset['path']).exists():
                selected_dataset = dataset
                break

        if selected_dataset:
            logger.info(f"📁 Using safe dataset: {selected_dataset['path']}")
            logger.info(f"🎯 Target column: {selected_dataset['target']}")
            logger.info(f"📋 Dataset type: {selected_dataset['name']}")

            logger.info("Step 3a: Running ablation study with safe features...")
            ablation_study = AblationStudy(
                data_path=selected_dataset['path'],
                target_column=selected_dataset['target'],
                random_state=42
            )
            ablation_study.load_data()
            importance_df, optimal_result = ablation_study.run_complete_ablation_study()

            # Save ablation study results (including Feature Impact Report)
            results_file, importance_file = ablation_study.save_results(importance_df, optimal_result)
            logger.info(f"✅ Ablation study results saved:")
            logger.info(f"   • Results: {results_file}")
            logger.info(f"   • Importance: {importance_file}")
            logger.info(f"   • Feature Impact Report: results/clean_ablation_study/feature_impact_report_*.txt")

            logger.info("Step 3b: Advanced model step skipped (clean_advanced_model removed)")
            # Advanced model functionality removed
            advanced_result = {
                'accuracy_mean': 0.0,
                'f1_mean': 0.0,
                'precision_mean': 0.0,
                'recall_mean': 0.0
            }

            return {
                'classification_summary': summary,
                'safe_dataset_path': selected_dataset['path'],
                'safe_dataset_type': selected_dataset['name'],
                'target_column': selected_dataset['target'],
                'ablation_result': optimal_result,
                'advanced_result': advanced_result,
                'best_accuracy': optimal_result['accuracy_mean'],
                'best_model': 'Ablation_Study_Optimal_Safe_Features',
                'data_leakage_prevented': True
            }
        else:
            logger.warning("❌ No safe dataset found!")
            logger.warning("Available safe datasets to check:")
            for dataset in safe_datasets:
                status = "✅ EXISTS" if Path(dataset['path']).exists() else "❌ NOT FOUND"
                logger.warning(f"   • {dataset['path']} - {status}")
            logger.warning("Please run the pipeline first to generate safe datasets")
            return {
                'classification_summary': summary,
                'safe_dataset_path': None,
                'best_accuracy': None,
                'best_model': 'Classification_Only',
                'data_leakage_prevented': False
            }

    def run_fatigue_only_pipeline(self):
        """Execute fatigue classification with feature filtering only"""
        logger.info("🚀 STARTING FATIGUE CLASSIFICATION PIPELINE")
        logger.info("Includes: Fatigue Classification + Feature Filtering + ML Models")
        logger.info("="*80)

        try:
            # Check if processed data exists
            processed_data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            if not Path(processed_data_path).exists():
                logger.error(f"Processed data not found: {processed_data_path}")
                logger.error("Please run data processing first or use --complete flag")
                return None

            # Load processed data for summary
            processed_data = pd.read_csv(processed_data_path)
            logger.info(f"✅ Loaded processed data: {processed_data.shape}")

            # Run fatigue prediction with feature filtering
            ml_results = self.run_fatigue_prediction(processed_data)

            # Print summary
            self._print_fatigue_only_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 FATIGUE CLASSIFICATION PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Fatigue pipeline failed: {str(e)}")
            raise

    def _print_fatigue_only_summary(self, data, ml_results):
        """Print summary for fatigue-only pipeline"""

        print("\n" + "="*80)
        print("🎉 FATIGUE CLASSIFICATION PIPELINE SUMMARY")
        print("="*80)

        # Data Summary
        print(f"\n📊 DATA SUMMARY:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 FATIGUE PREDICTION & ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best ML Model: {ml_results['best_model']}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/fatigue_risk_classified_dataset.csv")
        if ml_results and ml_results.get('safe_dataset_path'):
            print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
        if ml_results and ml_results.get('best_accuracy'):
            print(f"   • results/clean_production_model/*.pkl")
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Models trained with data leakage prevention")

        print(f"\n✅ Fatigue classification completed successfully!")
        print("="*80)

    def run_complete_pipeline(self):
        """Execute complete analysis pipeline"""
        logger.info("🚀 STARTING COMPLETE ANALYSIS PIPELINE")
        logger.info("Includes: Data Processing + Fatigue Prediction + ML Models")
        logger.info("="*80)
        
        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()

            # Phase 2: Fatigue Prediction (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_fatigue_prediction(processed_data)

            # Final Summary
            self._print_final_summary(processed_data, ml_results)
            
            logger.info("="*80)
            logger.info("🎉 COMPLETE PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise
    
    def _print_final_summary(self, data, ml_results):
        """Print comprehensive final summary"""

        print("\n" + "="*80)
        print("🎉 COMPLETE ANALYSIS PIPELINE SUMMARY")
        print("="*80)

        # Data Processing Summary
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")
        
        # ML Results Summary
        if ml_results:
            print(f"\n🤖 FATIGUE PREDICTION & ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best ML Model: {ml_results['best_model']}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

                # Model safety note
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • ✅ Model trained with safe features (no data leakage)")
                else:
                    print(f"   • ⚠️ Model may have data leakage risk")
        
        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if ml_results:
            print(f"   • dataset/processed/fatigue_risk_classified_dataset.csv")
            if ml_results.get('safe_dataset_path'):
                print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
            if ml_results.get('best_accuracy'):
                print(f"   • results/clean_production_model/*.pkl")
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • 🛡️ Models trained with data leakage prevention")
        
        print(f"\n✅ All analyses completed successfully!")
        print("="*80)


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Complete Analysis Pipeline')
    parser.add_argument('--no-ml', action='store_true',
                       help='Skip machine learning components (data processing only)')
    parser.add_argument('--ml-only', action='store_true',
                       help='Run only machine learning pipeline')
    parser.add_argument('--fatigue-only', action='store_true',
                       help='Run only fatigue classification with feature filtering')
    parser.add_argument('--complete', action='store_true',
                       help='Run complete pipeline (default behavior)')

    args = parser.parse_args()
    
    try:
        # Check if raw data exists (unless ml-only)
        if not args.ml_only:
            raw_data_path = Path("dataset/raw")
            if not raw_data_path.exists():
                print("❌ Raw data directory not found!")
                print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
                return
            
            strava_file = raw_data_path / "strava.csv"
            pomokit_file = raw_data_path / "pomokit.csv"
            
            if not strava_file.exists() or not pomokit_file.exists():
                print("❌ Required data files not found!")
                print(f"Expected files:")
                print(f"  - {strava_file}")
                print(f"  - {pomokit_file}")
                return
        
        # Run appropriate pipeline
        if args.ml_only:
            # Create safe dataset first
            print("🛡️  Creating safe ML dataset (removing label creation features)...")
            safe_ml_path = "dataset/processed/safe_ml_fatigue_dataset.csv"

            # Check if safe dataset exists, if not create it
            if not Path(safe_ml_path).exists():
                from src.fatigue_classifier import FatigueClassifier
                from src.feature_filter1 import FeatureFilter

                # Need to run fatigue classification first
                fatigue_classifier = FatigueClassifier()
                classified_data = fatigue_classifier.process_fatigue_classification(
                    'dataset/processed/weekly_merged_dataset_with_gamification.csv'
                )
                classified_output = "dataset/processed/fatigue_risk_classified_dataset.csv"
                classified_data.to_csv(classified_output, index=False)

                # Then create safe dataset
                feature_filter = FeatureFilter()
                feature_filter.create_safe_dataset_for_fatigue_classifier(
                    classified_output,
                    safe_ml_path,
                    target_column='fatigue_risk'
                )

            # Run only ML pipeline with safe dataset
            from src.main_clean_pipeline import FatiguePredictionPipeline
            pipeline = FatiguePredictionPipeline(
                data_path=safe_ml_path,  # ✅ Use safe dataset
                target_column='fatigue_risk',  # ✅ Consistent target
                random_state=42
            )
            results = pipeline.run_complete_pipeline()
            print(f"\n🎉 ML Pipeline Completed!")
            print(f"📋 Results: {results}")
        elif args.fatigue_only:
            # Run only fatigue classification with feature filtering
            print("🚀 Running Fatigue Classification Pipeline with Feature Filtering...")
            pipeline = CompleteAnalysisPipeline(include_ml=True)
            results = pipeline.run_fatigue_only_pipeline()
            print(f"\n🎉 Fatigue Classification Pipeline Completed!")
        else:
            # Run complete pipeline (default)
            include_ml = not args.no_ml
            pipeline = CompleteAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
