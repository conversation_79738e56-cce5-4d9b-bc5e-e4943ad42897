"""
SHAP-Based Fatigue Prediction Analysis
Complete pipeline using SHAP (SHapley Additive exPlanations) for model interpretability

SHAP ADVANTAGES OVER RFE:
1. Theoretically grounded (game theory)
2. Individual prediction explanations
3. Feature interaction detection
4. Model-agnostic interpretability
5. Positive/negative contribution analysis
6. Global and local insights

PIPELINE MODES:
1. Complete Pipeline (default): Data Processing + External Labeling + SHAP Analysis
2. SHAP Only (--shap-only): Run SHAP analysis on existing leak-free data
3. Compare Methods (--compare): Compare SHAP vs RFE results
4. Visualization (--visualize): Generate SHAP plots and visualizations

DESIGNED FOR LEAK-FREE ANALYSIS:
- Uses external independent labels
- Works with ultra-safe and conservative datasets
- Prevents data leakage through strict feature filtering
- Provides realistic performance estimates with interpretability
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd
import numpy as np

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from external_fatigue_labeler import ExternalFatigueLabeler
from strict_feature_filter import StrictFeatureFilter
from shap_ablation_study import SHAPAblationStudy
from rfe_ablation_study import RFEAblationStudy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shap_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SHAPFatiguePipeline:
    """
    Complete SHAP-based fatigue prediction pipeline
    """

    def __init__(self):
        """Initialize pipeline components"""
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.external_labeler = ExternalFatigueLabeler(random_state=42)
        self.strict_filter = StrictFeatureFilter()
        
        # Ensure output directories exist
        Path("results/shap_ablation_study").mkdir(parents=True, exist_ok=True)
        Path("results/comparison_analysis").mkdir(parents=True, exist_ok=True)
        Path("dataset/processed").mkdir(parents=True, exist_ok=True)
    
    def run_shap_analysis(self, dataset_path: str, dataset_name: str, target_column: str = 'external_fatigue_risk'):
        """Run SHAP analysis on specified dataset"""
        logger.info(f"="*60)
        logger.info(f"SHAP ANALYSIS: {dataset_name.upper()}")
        logger.info(f"="*60)
        
        if not Path(dataset_path).exists():
            logger.error(f"Dataset not found: {dataset_path}")
            return None
        
        try:
            # Run SHAP analysis
            logger.info(f"Running SHAP analysis on {dataset_name}...")
            shap_study = SHAPAblationStudy(
                data_path=dataset_path,
                target_column=target_column,
                random_state=42
            )
            
            shap_results = shap_study.run_complete_shap_study()
            
            # Save results
            results_file, report_file = shap_study.save_results(shap_results, prefix=f"leak_free_{dataset_name}")
            
            logger.info(f"✅ SHAP analysis completed for {dataset_name}:")
            logger.info(f"   • Results: {results_file}")
            logger.info(f"   • Report: {report_file}")
            
            # Extract key metrics
            best_algo = max(shap_results['model_performance'].items(), key=lambda x: x[1]['accuracy'])
            best_algo_key, best_perf = best_algo
            
            return {
                'dataset_name': dataset_name,
                'dataset_path': dataset_path,
                'best_accuracy': best_perf['accuracy'],
                'best_f1': best_perf['f1_score'],
                'best_algorithm': best_perf['algorithm_name'],
                'results_file': results_file,
                'report_file': report_file,
                'shap_results': shap_results
            }
            
        except Exception as e:
            logger.error(f"❌ SHAP analysis failed for {dataset_name}: {str(e)}")
            return {'error': str(e), 'dataset_name': dataset_name}
    
    def run_shap_only_pipeline(self):
        """Execute SHAP analysis only on existing leak-free datasets"""
        logger.info("🚀 STARTING SHAP-ONLY ANALYSIS PIPELINE")
        logger.info("Analyzing: Ultra-Safe + Conservative Leak-Free Datasets")
        logger.info("="*80)
        
        try:
            # Check available datasets
            datasets = [
                {
                    'name': 'ultra_safe',
                    'path': 'dataset/processed/ultra_safe_fatigue_dataset.csv',
                    'description': 'Ultra-Safe Dataset (12 features)'
                },
                {
                    'name': 'conservative',
                    'path': 'dataset/processed/conservative_safe_fatigue_dataset.csv',
                    'description': 'Conservative Dataset (29 features)'
                }
            ]
            
            results = {}
            
            for dataset in datasets:
                if Path(dataset['path']).exists():
                    logger.info(f"\n📊 Analyzing {dataset['description']}...")
                    result = self.run_shap_analysis(dataset['path'], dataset['name'])
                    if result:
                        results[dataset['name']] = result
                else:
                    logger.warning(f"❌ Dataset not found: {dataset['path']}")
                    logger.warning("Please run leak-free pipeline first: python main_leak_free.py")
            
            # Print summary
            self._print_shap_summary(results)
            
            logger.info("="*80)
            logger.info("🎉 SHAP-ONLY ANALYSIS FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return results
            
        except Exception as e:
            logger.error(f"SHAP pipeline failed: {str(e)}")
            raise
    
    def run_comparison_analysis(self):
        """Compare SHAP vs RFE results"""
        logger.info("🚀 STARTING SHAP vs RFE COMPARISON ANALYSIS")
        logger.info("="*80)
        
        try:
            # Run SHAP analysis
            shap_results = self.run_shap_only_pipeline()
            
            # Run RFE analysis for comparison
            logger.info("\n🔄 Running RFE analysis for comparison...")
            
            rfe_results = {}
            datasets = [
                ('ultra_safe', 'dataset/processed/ultra_safe_fatigue_dataset.csv'),
                ('conservative', 'dataset/processed/conservative_safe_fatigue_dataset.csv')
            ]
            
            for dataset_name, dataset_path in datasets:
                if Path(dataset_path).exists():
                    logger.info(f"Running RFE on {dataset_name}...")
                    
                    rfe_study = RFEAblationStudy(
                        data_path=dataset_path,
                        target_column='external_fatigue_risk',
                        random_state=42
                    )
                    rfe_study.load_data()
                    rfe_study_results = rfe_study.run_complete_rfe_study()
                    
                    # Extract best result
                    best_rfe = rfe_study_results['analysis'].get('best_overall', {})
                    
                    rfe_results[dataset_name] = {
                        'best_accuracy': best_rfe.get('accuracy', 0.0),
                        'best_algorithm': best_rfe.get('algorithm_name', 'Unknown'),
                        'best_features': best_rfe.get('n_features', 0)
                    }
            
            # Generate comparison report
            self._generate_comparison_report(shap_results, rfe_results)
            
            logger.info("="*80)
            logger.info("🎉 COMPARISON ANALYSIS FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {'shap_results': shap_results, 'rfe_results': rfe_results}
            
        except Exception as e:
            logger.error(f"Comparison analysis failed: {str(e)}")
            raise
    
    def run_complete_pipeline(self):
        """Execute complete pipeline with data processing + SHAP analysis"""
        logger.info("🚀 STARTING COMPLETE SHAP ANALYSIS PIPELINE")
        logger.info("Includes: Data Processing + External Labeling + Strict Filtering + SHAP Analysis")
        logger.info("="*80)
        
        try:
            # Phase 1: Data Processing
            logger.info("PHASE 1: DATA PROCESSING")
            processed_data = self.data_processor.process_all()
            logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")
            
            # Phase 2: External Labeling
            logger.info("\nPHASE 2: EXTERNAL INDEPENDENT LABELING")
            external_labeled_path = "dataset/processed/external_labeled_fatigue_dataset.csv"
            labeled_data = self.external_labeler.create_external_labels(
                "dataset/processed/weekly_merged_dataset_with_gamification.csv",
                external_labeled_path
            )
            
            # Phase 3: Strict Filtering
            logger.info("\nPHASE 3: STRICT FEATURE FILTERING")
            ultra_safe_path = "dataset/processed/ultra_safe_fatigue_dataset.csv"
            conservative_path = "dataset/processed/conservative_safe_fatigue_dataset.csv"
            
            self.strict_filter.create_ultra_safe_dataset(
                external_labeled_path, ultra_safe_path, 'external_fatigue_risk'
            )
            self.strict_filter.create_conservative_dataset(
                external_labeled_path, conservative_path, 'external_fatigue_risk'
            )
            
            # Phase 4: SHAP Analysis
            logger.info("\nPHASE 4: SHAP ANALYSIS")
            shap_results = self.run_shap_only_pipeline()
            
            # Final Summary
            self._print_complete_summary(processed_data, shap_results)
            
            logger.info("="*80)
            logger.info("🎉 COMPLETE SHAP PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return shap_results
            
        except Exception as e:
            logger.error(f"Complete pipeline failed: {str(e)}")
            raise
    
    def _print_shap_summary(self, results):
        """Print SHAP analysis summary"""
        print("\n" + "="*80)
        print("🎉 SHAP ANALYSIS SUMMARY")
        print("="*80)
        
        for dataset_name, result in results.items():
            if 'error' not in result:
                print(f"\n📊 {dataset_name.upper()} DATASET:")
                print(f"   • Best Algorithm: {result['best_algorithm']}")
                print(f"   • Best Accuracy: {result['best_accuracy']:.4f} ({result['best_accuracy']*100:.2f}%)")
                print(f"   • Best F1-Score: {result['best_f1']:.4f}")
                print(f"   • Results: {result['results_file']}")
                print(f"   • Report: {result['report_file']}")
            else:
                print(f"\n❌ {dataset_name.upper()} DATASET: FAILED")
                print(f"   • Error: {result['error']}")
        
        print(f"\n🔍 SHAP ADVANTAGES DEMONSTRATED:")
        print(f"   • Individual prediction explanations available")
        print(f"   • Feature interaction detection")
        print(f"   • Theoretically grounded (Shapley values)")
        print(f"   • Model-agnostic interpretability")
        
        print("="*80)
    
    def _print_complete_summary(self, processed_data, shap_results):
        """Print complete pipeline summary"""
        print("\n" + "="*80)
        print("🎉 COMPLETE SHAP PIPELINE SUMMARY")
        print("="*80)
        
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(processed_data)} weekly observations processed")
        print(f"   • External labels created with independent criteria")
        print(f"   • Strict feature filtering applied")
        
        print(f"\n🔍 SHAP ANALYSIS RESULTS:")
        for dataset_name, result in shap_results.items():
            if 'error' not in result:
                print(f"   • {dataset_name.title()}: {result['best_accuracy']:.4f} ({result['best_algorithm']})")
        
        print(f"\n🛡️ DATA LEAKAGE PREVENTION:")
        print(f"   • ✅ External independent labeling")
        print(f"   • ✅ Strict feature filtering")
        print(f"   • ✅ Leak-free methodology")
        print(f"   • ✅ Realistic performance estimates")
        
        print("="*80)
    
    def _generate_comparison_report(self, shap_results, rfe_results):
        """Generate SHAP vs RFE comparison report"""
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"results/comparison_analysis/shap_vs_rfe_comparison_{timestamp}.txt"
        
        report = []
        report.append("="*80)
        report.append("🔍 SHAP vs RFE COMPARISON ANALYSIS")
        report.append("="*80)
        
        for dataset_name in ['ultra_safe', 'conservative']:
            if dataset_name in shap_results and dataset_name in rfe_results:
                shap_res = shap_results[dataset_name]
                rfe_res = rfe_results[dataset_name]
                
                report.append(f"\n📊 {dataset_name.upper()} DATASET COMPARISON:")
                report.append(f"   SHAP Analysis:")
                report.append(f"     • Best Accuracy: {shap_res['best_accuracy']:.4f}")
                report.append(f"     • Best Algorithm: {shap_res['best_algorithm']}")
                report.append(f"   RFE Analysis:")
                report.append(f"     • Best Accuracy: {rfe_res['best_accuracy']:.4f}")
                report.append(f"     • Best Algorithm: {rfe_res['best_algorithm']}")
                report.append(f"     • Optimal Features: {rfe_res['best_features']}")
        
        report.append(f"\n🎯 METHODOLOGY COMPARISON:")
        report.append(f"   SHAP Advantages:")
        report.append(f"     • Individual prediction explanations")
        report.append(f"     • Feature interaction detection")
        report.append(f"     • Theoretically grounded (game theory)")
        report.append(f"     • Model-agnostic interpretability")
        report.append(f"   RFE Advantages:")
        report.append(f"     • Feature selection optimization")
        report.append(f"     • Recursive elimination process")
        report.append(f"     • Optimal feature count determination")
        report.append(f"     • Cross-validation based selection")
        
        # Save report
        Path("results/comparison_analysis").mkdir(parents=True, exist_ok=True)
        with open(report_path, 'w') as f:
            f.write('\n'.join(report))
        
        logger.info(f"Comparison report saved: {report_path}")


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='SHAP-Based Fatigue Prediction Analysis')
    parser.add_argument('--shap-only', action='store_true',
                       help='Run only SHAP analysis on existing leak-free data')
    parser.add_argument('--compare', action='store_true',
                       help='Compare SHAP vs RFE results')
    parser.add_argument('--complete', action='store_true',
                       help='Run complete pipeline with SHAP analysis')

    args = parser.parse_args()
    
    try:
        pipeline = SHAPFatiguePipeline()
        
        if args.shap_only:
            print("🔍 Running SHAP Analysis Only...")
            pipeline.run_shap_only_pipeline()
        elif args.compare:
            print("🔄 Running SHAP vs RFE Comparison...")
            pipeline.run_comparison_analysis()
        else:
            # Default: run SHAP-only if leak-free data exists, otherwise complete pipeline
            ultra_safe_exists = Path("dataset/processed/ultra_safe_fatigue_dataset.csv").exists()
            conservative_exists = Path("dataset/processed/conservative_safe_fatigue_dataset.csv").exists()
            
            if ultra_safe_exists and conservative_exists:
                print("🔍 Running SHAP Analysis on Existing Leak-Free Data...")
                pipeline.run_shap_only_pipeline()
            else:
                print("🚀 Running Complete Pipeline with SHAP Analysis...")
                pipeline.run_complete_pipeline()
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
