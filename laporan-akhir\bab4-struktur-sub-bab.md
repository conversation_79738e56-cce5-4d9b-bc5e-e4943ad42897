# BAB IV - EKSPERIMEN DAN HASIL

## Struktur Sub Bab yang Direkomendasikan

## 4.1 EKSPERIMEN

### 4.1.1 Deskripsi Dataset dan Preprocessing

#### ******* Karakteristik Dataset

-   Statistik deskriptif dataset penelitian
-   Distribusi variabel utama (aktivitas fisik, produktivitas, gamifikasi)
-   Temporal patterns dan konsistensi data
-   Kualitas data dan missing values analysis

#### ******* Distribusi Target Variable (Fatigue Risk)

-   Distribusi klasifikasi fatigue risk (low/medium/high)
-   Temporal patterns fatigue risk sepanjang semester
-   Analisis periode peak fatigue (ujian, deadline)
-   Validasi konstruk fatigue risk

#### ******* Data Quality Assurance

-   Proses data cleaning dan validation
-   Outlier detection dan treatment
-   Cross-validation antara sumber data (Strava-Pomokit)
-   Data integrity checks

### 4.1.2 Feature Engineering dan Text Processing

#### ******* Derived Features Creation

-   Consistency score calculation
-   Gamification balance metrics
-   Weekly efficiency indicators
-   Activity-productivity ratios

#### ******* Text-based Feature Extraction

-   Title analysis dari aktivitas Strava dan Pomokit
-   Keyword extraction dan vocabulary analysis
-   Language pattern detection (Indonesian/English)
-   Text statistics dan diversity metrics

#### 4.1.2.3 Feature Selection dan Filtering

-   Feature importance preliminary analysis
-   Multicollinearity detection
-   Feature filtering untuk prevent data leakage
-   Safe feature sets untuk ML training

### 4.1.3 Metodologi Klasifikasi Fatigue Risk

#### 4.3.1 Classification Methodology Validation

-   Composite scoring approach validation
-   Threshold analysis untuk optimal cut-points
-   Inter-rater reliability assessment
-   Classification consistency across time periods

#### 4.3.2 Temporal Pattern Analysis

-   Weekly fatigue risk patterns
-   Seasonal variations dalam fatigue levels
-   Peak fatigue periods identification
-   Recovery pattern analysis

#### 4.3.3 Feature Contribution Analysis

-   SHAP values untuk feature importance
-   Top contributing features untuk setiap risk level
-   Feature interaction effects
-   Productivity vs physical activity feature comparison

### 4.4 Machine Learning Model Development

#### 4.4.1 Baseline Model Performance

-   Random Forest baseline results
-   SVM baseline performance
-   Neural Network baseline metrics
-   Cross-validation results comparison

#### 4.4.2 Hyperparameter Optimization

-   Grid search results untuk setiap algorithm
-   Optimal parameter combinations
-   Performance improvement analysis
-   Computational efficiency considerations

#### 4.4.3 Class Imbalance Handling

-   SMOTE implementation results
-   Balanced accuracy improvements
-   Precision-recall trade-offs
-   Minority class performance enhancement

### 4.5 Ablation Study Results

#### 4.5.1 Systematic Feature Removal Analysis

-   Individual feature impact assessment
-   Feature importance ranking
-   Performance degradation analysis
-   Critical feature identification

#### 4.5.2 Feature Group Analysis

-   Physical activity features impact
-   Productivity features contribution
-   Gamification features effectiveness
-   Text-based features performance

#### 4.5.3 Optimal Feature Set Determination

-   Minimal viable feature set
-   Performance vs complexity trade-off
-   Recommended feature combinations
-   Practical implementation considerations

### 4.6 Specialized Analysis Approaches

#### 4.6.1 Title-Only Classification

-   Pure text-based fatigue prediction
-   Title-only model performance
-   Comparison dengan full feature model
-   Practical applicability assessment

#### 4.6.2 Bias Correction Analysis

-   Language bias detection dan correction
-   Activity type bias identification
-   Bias-corrected model performance
-   Fairness metrics evaluation

#### 4.6.3 Cross-validation dan External Validation

-   Temporal cross-validation results
-   Stratified validation performance
-   Generalizability assessment
-   Model robustness testing

### 4.7 Advanced Model Performance

#### 4.7.1 Ensemble Methods Results

-   Model combination strategies
-   Voting classifier performance
-   Stacking ensemble results
-   Ensemble vs individual model comparison

#### 4.7.2 Model Interpretability Analysis

-   SHAP value interpretation
-   Feature importance visualization
-   Decision boundary analysis
-   Model explanation untuk practitioners

#### 4.7.3 Production Model Selection

-   Final model recommendation
-   Performance-interpretability balance
-   Deployment considerations
-   Maintenance requirements

### 4.8 Gamification Impact Analysis

#### 4.8.1 Gamification Balance Optimization

-   Optimal activity-productivity point balance
-   Threshold analysis untuk sustainable engagement
-   Balance ratio impact pada fatigue risk
-   Long-term sustainability patterns

#### 4.8.2 Achievement Rate Analysis

-   Achievement level impact pada fatigue
-   Optimal challenge-skill balance
-   Motivation vs fatigue trade-offs
-   Personalization opportunities

#### 4.8.3 Practical Implementation Guidelines

-   Recommended gamification strategies
-   Risk mitigation approaches
-   Monitoring indicators
-   Intervention thresholds

### 4.9 Model Validation dan Reliability

#### 4.9.1 Statistical Significance Testing

-   Model comparison statistical tests
-   Confidence intervals untuk performance metrics
-   Effect size analysis
-   Practical significance assessment

#### 4.9.2 Robustness Testing

-   Sensitivity analysis
-   Noise tolerance testing
-   Missing data impact assessment
-   Edge case performance

#### 4.9.3 Real-world Applicability

-   Implementation feasibility analysis
-   Resource requirements assessment
-   Scalability considerations
-   Integration challenges

### 4.10 Comparative Analysis dan Benchmarking

#### 4.10.1 Literature Comparison

-   Performance vs existing studies
-   Methodological improvements
-   Novel contributions identification
-   Limitation acknowledgments

#### 4.10.2 Baseline Method Comparison

-   Simple rule-based approaches
-   Traditional statistical methods
-   Domain expert predictions
-   Random baseline performance

#### 4.10.3 Cost-Benefit Analysis

-   Implementation cost considerations
-   Accuracy vs complexity trade-offs
-   Maintenance overhead analysis
-   ROI projections untuk practical deployment
