================================================================================
📊 FEATURE IMPACT ANALYSIS REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_title_only_dataset.csv
   • Target Column: title_fatigue_risk
   • Total Samples: N/A
   • Total Features: 18

📝 ALL FEATURES USED IN ANALYSIS:
    1. achievement_rate
    2. activity_days
    3. activity_points
    4. avg_distance_km
    5. avg_time_minutes
    6. consistency_score
    7. gamification_balance
    8. pomokit_title_count
    9. pomokit_unique_words
   10. productivity_points
   11. strava_title_count
   12. strava_unique_words
   13. title_balance_ratio
   14. total_cycles
   15. total_distance_km
   16. total_time_minutes
   17. total_title_diversity
   18. work_days

🟢 NEUTRAL FEATURES (Optional - 18):
   • productivity_points: -0.33% impact - 🟢 Netral
   • total_distance_km: -0.33% impact - 🟢 Netral
   • achievement_rate: -0.00% impact - 🟢 Netral
   • avg_distance_km: -0.00% impact - 🟢 Netral
   • pomokit_unique_words: +0.00% impact - 🟢 Netral
   • total_title_diversity: +0.00% impact - 🟢 Netral
   • consistency_score: +0.00% impact - 🟢 Netral
   • activity_days: +0.00% impact - 🟢 Netral
   • gamification_balance: +0.00% impact - 🟢 Netral
   • strava_title_count: +0.00% impact - 🟢 Netral
   • strava_unique_words: +0.33% impact - 🟢 Netral
   • avg_time_minutes: +0.33% impact - 🟢 Netral
   • activity_points: +0.33% impact - 🟢 Netral
   • work_days: +0.67% impact - 🟢 Netral
   • total_cycles: +0.67% impact - 🟢 Netral
   • total_time_minutes: +0.67% impact - 🟢 Netral
   • pomokit_title_count: +0.67% impact - 🟢 Netral
   • title_balance_ratio: +1.00% impact - 🟢 Netral

📈 BASELINE MODEL PERFORMANCE:
   • Baseline Accuracy: 0.5933 (59.33%)
   • Total Features: 18

🎯 FEATURE CATEGORIES:
   • 🔴 Critical Features (hurt when removed): 0
   • ⚠️ Noise Features (help when removed): 0
   • 🟢 Neutral Features: 18

🏆 TOP 5 MOST IMPORTANT FEATURES:
   1. productivity_points
      • Impact when removed: -0.33%
      • Accuracy without: 0.5900
      • Status: 🟢 Netral
   2. total_distance_km
      • Impact when removed: -0.33%
      • Accuracy without: 0.5900
      • Status: 🟢 Netral
   3. achievement_rate
      • Impact when removed: -0.00%
      • Accuracy without: 0.5933
      • Status: 🟢 Netral
   4. avg_distance_km
      • Impact when removed: -0.00%
      • Accuracy without: 0.5933
      • Status: 🟢 Netral
   5. pomokit_unique_words
      • Impact when removed: +0.00%
      • Accuracy without: 0.5933
      • Status: 🟢 Netral

⚠️ POTENTIAL NOISE FEATURES (Bottom 5):
   14. work_days
      • Impact when removed: +0.67%
      • Accuracy without: 0.6000
      • Status: 🟢 Netral
   15. total_cycles
      • Impact when removed: +0.67%
      • Accuracy without: 0.6000
      • Status: 🟢 Netral
   16. total_time_minutes
      • Impact when removed: +0.67%
      • Accuracy without: 0.6000
      • Status: 🟢 Netral
   17. pomokit_title_count
      • Impact when removed: +0.67%
      • Accuracy without: 0.6000
      • Status: 🟢 Netral
   18. title_balance_ratio
      • Impact when removed: +1.00%
      • Accuracy without: 0.6033
      • Status: 🟢 Netral

💡 RECOMMENDATIONS:

🎯 OPTIMAL FEATURE SET (10 features):
    1. achievement_rate (-0.00%)
    2. activity_days (+0.00%)
    3. avg_distance_km (-0.00%)
    4. consistency_score (+0.00%)
    5. gamification_balance (+0.00%)
    6. pomokit_unique_words (+0.00%)
    7. productivity_points (-0.33%)
    8. strava_title_count (+0.00%)
    9. total_distance_km (-0.33%)
   10. total_title_diversity (+0.00%)

🚫 FEATURES EXCLUDED FROM OPTIMAL SET (8):
   • activity_points (+0.33%) - Low Impact
   • avg_time_minutes (+0.33%) - Low Impact
   • pomokit_title_count (+0.67%) - Low Impact
   • strava_unique_words (+0.33%) - Low Impact
   • title_balance_ratio (+1.00%) - Low Impact
   • total_cycles (+0.67%) - Low Impact
   • total_time_minutes (+0.67%) - Low Impact
   • work_days (+0.67%) - Low Impact

📊 FEATURE SELECTION SUMMARY:
   • Original Features: 18
   • Optimal Features: 10
   • Features Removed: 8
   • Reduction: 44.4%

💻 IMPLEMENTATION GUIDE:
   1. Remove 0 noise features for immediate improvement
   2. Use optimal feature set with 10 features
   3. Expected accuracy improvement: +0.00%
   4. Model complexity reduction: 44.4%
================================================================================