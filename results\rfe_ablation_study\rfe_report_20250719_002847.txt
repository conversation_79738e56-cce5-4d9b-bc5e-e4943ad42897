================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 18
   • Total Samples: 300

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [4, 5, 9, 10, 13, 15, 18, 20, 25, 30]
   • Total Experiments: 28

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: Random Forest
   • Features Used: 18
   • Accuracy: 0.7100 ± 0.0620
   • F1-Score: 0.6410
   • ⚠️ Overfitting Risk: 0.2900 (HIGH - Train-Test gap > 10%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 13
     - Accuracy: 0.6667 ± 0.0506
     - F1-Score: 0.6478
     - ⚠️ Overfitting: 0.0608 (MODERATE)
   • Random Forest:
     - Features: 18
     - Accuracy: 0.7100 ± 0.0620
     - F1-Score: 0.6410
     - ⚠️ Overfitting: 0.2900 (HIGH)
   • Gradient Boosting:
     - Features: 10
     - Accuracy: 0.6800 ± 0.0476
     - F1-Score: 0.6418
     - ⚠️ Overfitting: 0.3192 (HIGH)
   • Support Vector Machine:
     - Features: 9
     - Accuracy: 0.6633 ± 0.0499
     - F1-Score: 0.6369
     - ✅ Overfitting: 0.0450 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. pomokit_unique_words: 27 times (96.4%)
    2. title_balance_ratio: 26 times (92.9%)
    3. achievement_rate: 25 times (89.3%)
    4. work_days: 22 times (78.6%)
    5. total_cycles: 22 times (78.6%)
    6. avg_distance_km: 20 times (71.4%)
    7. pomokit_title_count: 19 times (67.9%)
    8. total_distance_km: 19 times (67.9%)
    9. total_title_diversity: 18 times (64.3%)
   10. productivity_points: 16 times (57.1%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. achievement_rate: 10/10 top results
    2. pomokit_unique_words: 10/10 top results
    3. title_balance_ratio: 10/10 top results
    4. total_time_minutes: 9/10 top results
    5. avg_distance_km: 9/10 top results
    6. work_days: 9/10 top results
    7. total_title_diversity: 9/10 top results
    8. pomokit_title_count: 8/10 top results
    9. total_distance_km: 8/10 top results
   10. total_cycles: 7/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.2900
   • ⚠️ HIGH OVERFITTING RISK: Train-Test gap > 10%
   • Recommendation: Consider regularization or more data

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.0608 (MODERATE)
     ⚠️ Random Forest: 0.2900 (HIGH)
     ⚠️ Gradient Boosting: 0.3192 (HIGH)
     ✅ Support Vector Machine: 0.0450 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: Random Forest
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 18
   • Baseline Accuracy: 0.7100

   🎯 Feature Impact Analysis (Top 10):
      1. 🔸 consistency_score:
         - Baseline: 0.7100 | Without: 0.6633
         - Impact: 0.0467 (6.57%)
         - MEDIUM - Moderate performance impact
      2. 🔸 total_title_diversity:
         - Baseline: 0.7100 | Without: 0.6633
         - Impact: 0.0467 (6.57%)
         - MEDIUM - Moderate performance impact
      3. 🔸 gamification_balance:
         - Baseline: 0.7100 | Without: 0.6667
         - Impact: 0.0433 (6.10%)
         - MEDIUM - Moderate performance impact
      4. ▫️ pomokit_unique_words:
         - Baseline: 0.7100 | Without: 0.6767
         - Impact: 0.0333 (4.69%)
         - LOW - Minor performance impact
      5. ▫️ title_balance_ratio:
         - Baseline: 0.7100 | Without: 0.6767
         - Impact: 0.0333 (4.69%)
         - LOW - Minor performance impact
      6. ▫️ strava_unique_words:
         - Baseline: 0.7100 | Without: 0.6800
         - Impact: 0.0300 (4.23%)
         - LOW - Minor performance impact
      7. ▫️ avg_time_minutes:
         - Baseline: 0.7100 | Without: 0.6833
         - Impact: 0.0267 (3.76%)
         - LOW - Minor performance impact
      8. ▫️ activity_points:
         - Baseline: 0.7100 | Without: 0.6867
         - Impact: 0.0233 (3.29%)
         - LOW - Minor performance impact
      9. ▫️ total_cycles:
         - Baseline: 0.7100 | Without: 0.6867
         - Impact: 0.0233 (3.29%)
         - LOW - Minor performance impact
     10. ▫️ achievement_rate:
         - Baseline: 0.7100 | Without: 0.6900
         - Impact: 0.0200 (2.82%)
         - LOW - Minor performance impact

   📊 Permutation Importance Summary:
     • consistency_score: 1.60% (score: 0.0040 ±0.0039)
     • total_title_diversity: 21.50% (score: 0.0537 ±0.0080)
     • gamification_balance: 1.20% (score: 0.0030 ±0.0023)
     • pomokit_unique_words: 21.36% (score: 0.0533 ±0.0049)
     • title_balance_ratio: 23.23% (score: 0.0580 ±0.0083)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: Random Forest
   • Number of Features: 18
   • Selected Features:
      1. achievement_rate
      2. activity_days
      3. activity_points
      4. avg_distance_km
      5. avg_time_minutes
      6. consistency_score
      7. gamification_balance
      8. pomokit_title_count
      9. pomokit_unique_words
     10. productivity_points
     11. strava_title_count
     12. strava_unique_words
     13. title_balance_ratio
     14. total_cycles
     15. total_distance_km
     16. total_time_minutes
     17. total_title_diversity
     18. work_days
================================================================================