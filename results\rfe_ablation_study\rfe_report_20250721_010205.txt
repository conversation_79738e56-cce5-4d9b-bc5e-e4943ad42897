================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 20
   • Total Samples: 300

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [5, 10, 15, 20, 25, 30]
   • Total Experiments: 16

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: Random Forest
   • Features Used: 15
   • Accuracy: 0.9667 ± 0.0183
   • F1-Score: 0.9550
   • ✅ Overfitting Risk: 0.0333 (LOW - Train-Test gap < 5%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 15
     - Accuracy: 0.6733 ± 0.0359
     - F1-Score: 0.6735
     - ✅ Overfitting: 0.0458 (LOW)
   • Random Forest:
     - Features: 15
     - Accuracy: 0.9667 ± 0.0183
     - F1-Score: 0.9550
     - ✅ Overfitting: 0.0333 (LOW)
   • Gradient Boosting:
     - Features: 5
     - Accuracy: 0.9600 ± 0.0133
     - F1-Score: 0.9504
     - ✅ Overfitting: 0.0267 (LOW)
   • Support Vector Machine:
     - Features: 15
     - Accuracy: 0.6900 ± 0.0374
     - F1-Score: 0.6875
     - ✅ Overfitting: 0.0292 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. total_cycles: 16 times (100.0%)
    2. consistency_score: 16 times (100.0%)
    3. work_days: 16 times (100.0%)
    4. pomokit_title_count: 15 times (93.8%)
    5. productivity_points: 15 times (93.8%)
    6. gamification_balance: 12 times (75.0%)
    7. title_balance_ratio: 11 times (68.8%)
    8. achievement_rate: 10 times (62.5%)
    9. pomokit_unique_words: 10 times (62.5%)
   10. total_title_diversity: 9 times (56.2%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. total_cycles: 10/10 top results
    2. consistency_score: 10/10 top results
    3. work_days: 10/10 top results
    4. productivity_points: 10/10 top results
    5. pomokit_title_count: 9/10 top results
    6. gamification_balance: 8/10 top results
    7. pomokit_title_length: 7/10 top results
    8. strava_title_length: 7/10 top results
    9. total_time_minutes: 7/10 top results
   10. title_balance_ratio: 7/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.0333
   • ✅ LOW OVERFITTING RISK: Good generalization expected

   📊 Overfitting by Algorithm:
     ✅ Logistic Regression: 0.0458 (LOW)
     ✅ Random Forest: 0.0333 (LOW)
     ✅ Gradient Boosting: 0.0267 (LOW)
     ✅ Support Vector Machine: 0.0292 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: Random Forest
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 15
   • Baseline Accuracy: 0.9667

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ strava_title_count:
         - Baseline: 0.9667 | Without: 0.9700
         - Impact: -0.0033 (-0.34%)
         - MINIMAL - Negligible performance impact
      2. ⚪ total_cycles:
         - Baseline: 0.9667 | Without: 0.9700
         - Impact: -0.0033 (-0.34%)
         - MINIMAL - Negligible performance impact
      3. ⚪ activity_days:
         - Baseline: 0.9667 | Without: 0.9700
         - Impact: -0.0033 (-0.34%)
         - MINIMAL - Negligible performance impact
      4. ⚪ title_balance_ratio:
         - Baseline: 0.9667 | Without: 0.9700
         - Impact: -0.0033 (-0.34%)
         - MINIMAL - Negligible performance impact
      5. ⚪ strava_title_length:
         - Baseline: 0.9667 | Without: 0.9633
         - Impact: 0.0033 (0.34%)
         - MINIMAL - Negligible performance impact
      6. ⚪ strava_unique_words:
         - Baseline: 0.9667 | Without: 0.9667
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      7. ⚪ pomokit_title_length:
         - Baseline: 0.9667 | Without: 0.9667
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      8. ⚪ achievement_rate:
         - Baseline: 0.9667 | Without: 0.9667
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
      9. ⚪ consistency_score:
         - Baseline: 0.9667 | Without: 0.9667
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact
     10. ⚪ pomokit_title_count:
         - Baseline: 0.9667 | Without: 0.9667
         - Impact: 0.0000 (0.00%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • strava_title_count: 2.27% (score: 0.0017 ±0.0017)
     • total_cycles: 0.00% (score: 0.0000 ±0.0000)
     • activity_days: 2.27% (score: 0.0017 ±0.0017)
     • title_balance_ratio: 7.27% (score: 0.0053 ±0.0016)
     • strava_title_length: 13.18% (score: 0.0097 ±0.0023)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: Random Forest
   • Number of Features: 15
   • Selected Features:
      1. achievement_rate
      2. activity_days
      3. consistency_score
      4. gamification_balance
      5. pomokit_title_count
      6. pomokit_title_length
      7. pomokit_unique_words
      8. productivity_points
      9. strava_title_count
     10. strava_title_length
     11. strava_unique_words
     12. title_balance_ratio
     13. total_cycles
     14. total_time_minutes
     15. work_days
================================================================================