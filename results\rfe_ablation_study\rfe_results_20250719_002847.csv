algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,4,"['pomokit_title_count', 'productivity_points', 'work_days', 'total_cycles']",0.6399999999999999,0.04027681991198188,0.6284441724801699,0.047671060818102615,0.6427011756733119,0.64789932990494,0.6825,0.04250000000000009,success
logistic_regression,Logistic Regression,5,"['pomokit_title_count', 'productivity_points', 'work_days', 'total_cycles', 'pomokit_unique_words']",0.65,0.05055250296034364,0.6366202126302128,0.05408380167444042,0.6501204317549998,0.6534548854604956,0.685,0.03500000000000003,success
logistic_regression,Logistic Regression,9,"['pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'total_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio']",0.6566666666666666,0.04546060565661951,0.6355407861335417,0.05663860843876291,0.6463196532384519,0.6467882187938289,0.7158333333333334,0.05916666666666681,success
logistic_regression,Logistic Regression,10,"['pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'total_distance_km', 'strava_unique_words', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio']",0.6599999999999999,0.054365021434333596,0.6429047164989187,0.07497740945878181,0.6484715362093991,0.6578276453171263,0.7216666666666667,0.06166666666666676,success
logistic_regression,Logistic Regression,13,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6666666666666666,0.05055250296034365,0.6477817896498906,0.07312764375585032,0.6517866651245123,0.6622004051737572,0.7275,0.060833333333333406,success
logistic_regression,Logistic Regression,15,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6566666666666666,0.04294699575575042,0.6344341343435651,0.05821195325261284,0.6435519335167093,0.6467882187938289,0.73,0.07333333333333336,success
logistic_regression,Logistic Regression,18,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'activity_days', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
random_forest,Random Forest,4,"['pomokit_title_count', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6599999999999999,0.06463573143221771,0.6013850636273498,0.056582367201252454,0.6470516466181014,0.5844015895278168,1.0,0.3400000000000001,success
random_forest,Random Forest,5,"['pomokit_title_count', 'achievement_rate', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6666666666666667,0.04346134936801767,0.5993884696871701,0.06030117551051998,0.6414605591541076,0.5838234377434939,1.0,0.33333333333333326,success
random_forest,Random Forest,9,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'work_days', 'total_cycles', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6966666666666667,0.043969686527576386,0.6342195700296552,0.051314402550897566,0.686309338964335,0.6119908056724327,1.0,0.30333333333333334,success
random_forest,Random Forest,10,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'work_days', 'total_distance_km', 'total_cycles', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6766666666666666,0.04666666666666666,0.5866132117591933,0.06635349008374183,0.6330538409091471,0.5715786192925043,1.0,0.32333333333333336,success
random_forest,Random Forest,13,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6933333333333332,0.047842333648024434,0.6365400350192109,0.06223403316797511,0.6784353328987262,0.6210004675081814,1.0,0.30666666666666675,success
random_forest,Random Forest,15,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.7033333333333333,0.057154760664940824,0.6318173573639579,0.08654549352459939,0.66475588179936,0.6194054854293284,1.0,0.29666666666666675,success
random_forest,Random Forest,18,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'activity_days', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.71,0.06200358412579425,0.6410391432127976,0.06950013560339559,0.6814696102022176,0.6230029608851487,1.0,0.29000000000000004,success
gradient_boosting,Gradient Boosting,4,"['achievement_rate', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6366666666666667,0.06182412330330468,0.5893560335263398,0.07145369709083232,0.6071181314995358,0.5811625370110644,0.99,0.3533333333333333,success
gradient_boosting,Gradient Boosting,5,"['achievement_rate', 'work_days', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6566666666666666,0.07195677714974301,0.5997519820305925,0.0723802635930405,0.6166008006076208,0.5943743182172354,0.9950000000000001,0.3383333333333335,success
gradient_boosting,Gradient Boosting,9,"['total_time_minutes', 'achievement_rate', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'total_distance_km', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6733333333333333,0.01999999999999998,0.6329846866549235,0.05117595802859702,0.6742180725496415,0.6146696275518154,1.0,0.32666666666666666,success
gradient_boosting,Gradient Boosting,10,"['total_time_minutes', 'achievement_rate', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'total_distance_km', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6799999999999999,0.04760952285695232,0.6418263770996251,0.059368053945817435,0.724118173883171,0.6195714508337229,0.9991666666666668,0.3191666666666668,success
gradient_boosting,Gradient Boosting,13,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'total_distance_km', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6633333333333334,0.04521553322083511,0.631788091153674,0.03292902297930761,0.6684783137131334,0.6151986909770921,1.0,0.33666666666666656,success
gradient_boosting,Gradient Boosting,15,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6599999999999999,0.04546060565661951,0.6228764720830234,0.040043437594742305,0.6665407626811135,0.6034829359513791,0.9991666666666668,0.33916666666666684,success
gradient_boosting,Gradient Boosting,18,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'activity_days', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6566666666666666,0.052281290471193745,0.6114149850787782,0.04912176954131192,0.6893241936933326,0.592371824840268,0.9991666666666668,0.34250000000000014,success
svm,Support Vector Machine,4,"['achievement_rate', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio']",0.6233333333333333,0.062003584125794216,0.5974221144958471,0.06821188467561821,0.5964439075355884,0.6525315568022441,0.6741666666666667,0.0508333333333334,success
svm,Support Vector Machine,5,"['achievement_rate', 'total_cycles', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6233333333333333,0.06960204339273698,0.6070951922627947,0.0762611794667704,0.597427690405533,0.660188561633162,0.6816666666666666,0.05833333333333335,success
svm,Support Vector Machine,9,"['achievement_rate', 'avg_distance_km', 'productivity_points', 'total_distance_km', 'total_cycles', 'consistency_score', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.6633333333333333,0.049888765156985884,0.6368551051364209,0.05630121126350061,0.6254726564466431,0.684978182951535,0.7083333333333334,0.04500000000000004,success
svm,Support Vector Machine,10,"['achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'total_distance_km', 'total_cycles', 'consistency_score', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.65,0.05055250296034366,0.6242459805756857,0.05386430053480397,0.6130128449604323,0.6754846501480443,0.7058333333333333,0.05583333333333329,success
svm,Support Vector Machine,13,"['achievement_rate', 'avg_distance_km', 'productivity_points', 'work_days', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.65,0.05962847939999438,0.6223242045660032,0.0620510804868598,0.6122929961389891,0.6673437743493844,0.7108333333333333,0.060833333333333295,success
svm,Support Vector Machine,15,"['pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'productivity_points', 'work_days', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.64,0.060184900284225955,0.6126965618550358,0.057014497843119065,0.6034343024283348,0.6601932367149759,0.7091666666666667,0.06916666666666671,success
svm,Support Vector Machine,18,"['total_time_minutes', 'pomokit_title_count', 'achievement_rate', 'avg_distance_km', 'strava_title_count', 'activity_days', 'productivity_points', 'work_days', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'total_cycles', 'consistency_score', 'total_title_diversity', 'pomokit_unique_words', 'gamification_balance', 'title_balance_ratio']",0.64,0.0646357314322177,0.620656188525899,0.06576705985297739,0.6098080901196449,0.6601932367149759,0.7125000000000001,0.07250000000000012,success
