algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,4,"['total_cycles', 'pomokit_title_count', 'work_days', 'productivity_points']",0.6399999999999999,0.04027681991198188,0.6284441724801699,0.047671060818102615,0.6427011756733119,0.64789932990494,0.6825,0.04250000000000009,success
logistic_regression,Logistic Regression,5,"['total_cycles', 'pomokit_title_count', 'work_days', 'productivity_points', 'pomokit_unique_words']",0.65,0.05055250296034364,0.6366202126302128,0.05408380167444042,0.6501204317549998,0.6534548854604956,0.685,0.03500000000000003,success
logistic_regression,Logistic Regression,9,"['total_cycles', 'total_distance_km', 'pomokit_title_count', 'avg_distance_km', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio']",0.6566666666666666,0.04546060565661951,0.6355407861335417,0.05663860843876291,0.6463196532384519,0.6467882187938289,0.7158333333333334,0.05916666666666681,success
logistic_regression,Logistic Regression,10,"['total_cycles', 'total_distance_km', 'strava_unique_words', 'pomokit_title_count', 'avg_distance_km', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio']",0.6599999999999999,0.054365021434333596,0.6429047164989187,0.07497740945878181,0.6484715362093991,0.6578276453171263,0.7216666666666667,0.06166666666666676,success
logistic_regression,Logistic Regression,13,"['total_time_minutes', 'total_cycles', 'total_distance_km', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio']",0.6666666666666666,0.05055250296034365,0.6477817896498906,0.07312764375585032,0.6517866651245123,0.6622004051737572,0.7275,0.060833333333333406,success
logistic_regression,Logistic Regression,15,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count']",0.6566666666666666,0.04294699575575042,0.6344341343435651,0.05821195325261284,0.6435519335167093,0.6467882187938289,0.73,0.07333333333333336,success
logistic_regression,Logistic Regression,18,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'activity_days', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count', 'consistency_score']",0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
random_forest,Random Forest,4,"['pomokit_title_count', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.68,0.05811865258054231,0.6194109749141307,0.06583163244084024,0.676801944629269,0.5988008415147267,1.0,0.31999999999999995,success
random_forest,Random Forest,5,"['pomokit_title_count', 'achievement_rate', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.6599999999999999,0.0654896090146283,0.6108001331626998,0.10051685846749346,0.644179177175562,0.5964531712638305,1.0,0.3400000000000001,success
random_forest,Random Forest,9,"['total_cycles', 'avg_time_minutes', 'pomokit_title_count', 'avg_distance_km', 'achievement_rate', 'total_title_diversity', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6966666666666667,0.049888765156985884,0.6426983241874327,0.04759036469388281,0.6814697384352556,0.6251534985195575,1.0,0.30333333333333334,success
random_forest,Random Forest,10,"['total_cycles', 'avg_time_minutes', 'total_distance_km', 'pomokit_title_count', 'avg_distance_km', 'achievement_rate', 'total_title_diversity', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6799999999999999,0.055176484524156154,0.6224913332429635,0.07563147262112337,0.6764806407616406,0.6009287829203677,1.0,0.32000000000000006,success
random_forest,Random Forest,13,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'pomokit_title_count', 'avg_distance_km', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.69,0.048989794855663564,0.6229001253553682,0.06394817153590387,0.653929023528246,0.6105882811282531,1.0,0.31000000000000005,success
random_forest,Random Forest,15,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.6799999999999999,0.06446359868604572,0.6007904395265133,0.07481287354016947,0.6609031217163646,0.5788008415147265,1.0,0.32000000000000006,success
random_forest,Random Forest,18,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'activity_days', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count', 'consistency_score']",0.6833333333333333,0.0760116950066092,0.6031259372435842,0.08855745955121642,0.6618313411734464,0.5822549477949197,1.0,0.31666666666666665,success
gradient_boosting,Gradient Boosting,4,"['total_time_minutes', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio']",0.6366666666666667,0.06182412330330468,0.5893560335263398,0.07145369709083232,0.6071181314995358,0.5811625370110644,0.99,0.3533333333333333,success
gradient_boosting,Gradient Boosting,5,"['total_time_minutes', 'total_cycles', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio']",0.6533333333333333,0.055176484524156154,0.6111335303412517,0.053877192607644814,0.6323049852446404,0.6008976157082748,0.9950000000000001,0.3416666666666668,success
gradient_boosting,Gradient Boosting,9,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'avg_distance_km', 'achievement_rate', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio']",0.67,0.019436506316150983,0.6304302786763258,0.05002970835065824,0.6717896192824786,0.6117710768271778,1.0,0.32999999999999996,success
gradient_boosting,Gradient Boosting,10,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'avg_distance_km', 'achievement_rate', 'total_title_diversity', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.6733333333333335,0.042946995755750415,0.6370187734798405,0.04433949057839612,0.7192065143142218,0.6145714508337229,0.9991666666666668,0.3258333333333333,success
gradient_boosting,Gradient Boosting,13,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.6733333333333335,0.05011098792790966,0.6395710739545777,0.03813981298726241,0.6748175218264733,0.6229047841670563,1.0,0.32666666666666655,success
gradient_boosting,Gradient Boosting,15,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'strava_unique_words', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.6633333333333333,0.04988876515698586,0.6336069949255764,0.04531462233850238,0.6733904437018472,0.6151496026180459,0.9991666666666668,0.33583333333333343,success
gradient_boosting,Gradient Boosting,18,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'activity_days', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count', 'consistency_score']",0.6533333333333333,0.04876246279442598,0.6137475464839202,0.033169046344804685,0.6404090823904233,0.5997865045971638,0.9991666666666668,0.34583333333333344,success
svm,Support Vector Machine,4,"['achievement_rate', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6233333333333333,0.062003584125794216,0.5974221144958471,0.06821188467561821,0.5964439075355884,0.6525315568022441,0.6741666666666667,0.0508333333333334,success
svm,Support Vector Machine,5,"['gamification_balance', 'achievement_rate', 'work_days', 'pomokit_unique_words', 'title_balance_ratio']",0.6233333333333333,0.06960204339273698,0.6070951922627947,0.0762611794667704,0.597427690405533,0.660188561633162,0.6816666666666666,0.05833333333333335,success
svm,Support Vector Machine,9,"['total_distance_km', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.6633333333333333,0.049888765156985884,0.6368551051364209,0.05630121126350061,0.6254726564466431,0.684978182951535,0.7083333333333334,0.04500000000000004,success
svm,Support Vector Machine,10,"['total_distance_km', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.65,0.05055250296034366,0.6242459805756857,0.05386430053480397,0.6130128449604323,0.6754846501480443,0.7058333333333333,0.05583333333333329,success
svm,Support Vector Machine,13,"['total_distance_km', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'consistency_score']",0.65,0.05962847939999438,0.6223242045660032,0.0620510804868598,0.6122929961389891,0.6673437743493844,0.7108333333333333,0.060833333333333295,success
svm,Support Vector Machine,15,"['total_cycles', 'total_distance_km', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count', 'consistency_score']",0.64,0.060184900284225955,0.6126965618550358,0.057014497843119065,0.6034343024283348,0.6601932367149759,0.7091666666666667,0.06916666666666671,success
svm,Support Vector Machine,18,"['total_time_minutes', 'total_cycles', 'avg_time_minutes', 'total_distance_km', 'activity_days', 'strava_unique_words', 'activity_points', 'pomokit_title_count', 'avg_distance_km', 'gamification_balance', 'achievement_rate', 'total_title_diversity', 'work_days', 'productivity_points', 'pomokit_unique_words', 'title_balance_ratio', 'strava_title_count', 'consistency_score']",0.64,0.0646357314322177,0.620656188525899,0.06576705985297739,0.6098080901196449,0.6601932367149759,0.7125000000000001,0.07250000000000012,success
