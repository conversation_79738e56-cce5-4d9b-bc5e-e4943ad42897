"""
SHAP Ablation Study for Fatigue Prediction
Comprehensive SHAP-based feature importance analysis using <PERSON><PERSON><PERSON><PERSON> values

SHAP ADVANTAGES OVER RFE:
1. Individual prediction explanations
2. Feature interaction detection
3. Global and local interpretability
4. Theoretically grounded (game theory)
5. Model-agnostic explanations
6. Positive/negative contribution analysis

ANALYSIS COMPONENTS:
- SHAP TreeExplainer for tree-based models
- SHAP LinearExplainer for linear models
- Global feature importance ranking
- Local prediction explanations
- Feature interaction analysis
- Waterfall plots for individual predictions
- Summary plots for global insights
"""

import pandas as pd
import numpy as np
import logging
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_validate, train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Configure logging
logger = logging.getLogger(__name__)

class SHAPAblationStudy:
    """
    Comprehensive SHAP-based ablation study for fatigue prediction
    """
    
    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        """
        Initialize SHAP ablation study
        
        Args:
            data_path: Path to dataset
            target_column: Name of target column
            random_state: Random state for reproducibility
        """
        self.data_path = data_path
        self.target_column = target_column
        self.random_state = random_state
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.label_encoder = LabelEncoder()
        
        # SHAP explainers for different model types
        self.explainers = {}
        self.shap_values = {}
        self.models = {}
        
        # Algorithm configurations
        self.algorithms = {
            'logistic_regression': {
                'name': 'Logistic Regression',
                'model': LogisticRegression(random_state=random_state, max_iter=1000),
                'explainer_type': 'linear'
            },
            'random_forest': {
                'name': 'Random Forest',
                'model': RandomForestClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            },
            'gradient_boosting': {
                'name': 'Gradient Boosting',
                'model': GradientBoostingClassifier(random_state=random_state, n_estimators=100),
                'explainer_type': 'tree'
            }
        }
        
        # Set random seeds
        np.random.seed(random_state)
        
    def load_data(self):
        """Load and prepare data for SHAP analysis"""
        logger.info(f"Loading data from {self.data_path}")
        
        self.data = pd.read_csv(self.data_path)
        logger.info(f"Data loaded: {self.data.shape}")
        
        # Prepare features and target
        self.X = self.data.drop(columns=[self.target_column])
        self.y = self.data[self.target_column]
        
        # Remove non-numeric columns for SHAP analysis
        numeric_columns = self.X.select_dtypes(include=[np.number]).columns
        self.X = self.X[numeric_columns]
        self.feature_names = list(self.X.columns)
        
        logger.info(f"Features: {self.feature_names}")
        logger.info(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Encode target
        self.y_encoded = self.label_encoder.fit_transform(self.y)
        
    def train_models(self):
        """Train models for SHAP analysis"""
        logger.info("Training models for SHAP analysis...")
        
        # Split data for training
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y_encoded, test_size=0.2, 
            random_state=self.random_state, stratify=self.y_encoded
        )
        
        self.X_train, self.X_test = X_train, X_test
        self.y_train, self.y_test = y_train, y_test
        
        # Train each model
        for algo_key, config in self.algorithms.items():
            logger.info(f"Training {config['name']}...")
            
            model = config['model']
            
            # For linear models, use pipeline with scaling
            if algo_key == 'logistic_regression':
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', model)
                ])
            else:
                pipeline = model
            
            # Train model
            pipeline.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = pipeline.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred, average='weighted')
            
            logger.info(f"{config['name']} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
            
            self.models[algo_key] = {
                'pipeline': pipeline,
                'model': model,
                'accuracy': accuracy,
                'f1_score': f1,
                'config': config
            }
    
    def create_shap_explainers(self):
        """Create SHAP explainers for each model"""
        logger.info("Creating SHAP explainers...")

        for algo_key, model_info in self.models.items():
            config = model_info['config']
            pipeline = model_info['pipeline']

            logger.info(f"Creating SHAP explainer for {config['name']}...")

            try:
                # Use KernelExplainer for all models (more robust but slower)
                # Sample background data for efficiency
                background_data = self.X_train.iloc[:50]

                def model_predict(X):
                    """Wrapper function for model prediction"""
                    if hasattr(pipeline, 'predict_proba'):
                        return pipeline.predict_proba(X)
                    else:
                        return pipeline.predict(X)

                explainer = shap.KernelExplainer(model_predict, background_data)
                self.explainers[algo_key] = explainer
                logger.info(f"✅ SHAP KernelExplainer created for {config['name']}")

            except Exception as e:
                logger.error(f"❌ Failed to create SHAP explainer for {config['name']}: {str(e)}")
                # Skip this model
                continue
    
    def calculate_shap_values(self):
        """Calculate SHAP values for each model"""
        logger.info("Calculating SHAP values...")

        # Use a small subset for SHAP calculation (KernelExplainer is slow)
        X_shap = self.X_test.iloc[:20] if len(self.X_test) > 20 else self.X_test

        for algo_key, explainer in self.explainers.items():
            config = self.models[algo_key]['config']
            logger.info(f"Calculating SHAP values for {config['name']}...")

            try:
                # KernelExplainer works with original data
                shap_values = explainer.shap_values(X_shap)

                self.shap_values[algo_key] = {
                    'values': shap_values,
                    'data': X_shap,
                    'feature_names': self.feature_names
                }

                logger.info(f"✅ SHAP values calculated for {config['name']}")

            except Exception as e:
                logger.error(f"❌ Failed to calculate SHAP values for {config['name']}: {str(e)}")
                continue
    
    def analyze_global_importance(self) -> Dict:
        """Analyze global feature importance using SHAP values"""
        logger.info("Analyzing global feature importance...")
        
        global_importance = {}
        
        for algo_key, shap_data in self.shap_values.items():
            config = self.models[algo_key]['config']
            shap_vals = shap_data['values']
            
            # Handle multi-class SHAP values
            if isinstance(shap_vals, list):
                # For multi-class, take mean absolute SHAP values across classes
                mean_shap = np.mean([np.abs(sv) for sv in shap_vals], axis=0)
            else:
                mean_shap = np.abs(shap_vals)
            
            # Calculate global importance (mean absolute SHAP value)
            global_imp = np.mean(mean_shap, axis=0)
            
            # Create feature importance ranking
            feature_importance = []
            for i, feature in enumerate(self.feature_names):
                feature_importance.append({
                    'feature': feature,
                    'shap_importance': global_imp[i],
                    'rank': 0  # Will be set after sorting
                })
            
            # Sort by importance
            feature_importance.sort(key=lambda x: x['shap_importance'], reverse=True)
            
            # Update ranks
            for i, item in enumerate(feature_importance):
                item['rank'] = i + 1
            
            global_importance[algo_key] = {
                'algorithm': config['name'],
                'feature_importance': feature_importance,
                'total_features': len(self.feature_names)
            }
            
            logger.info(f"Global importance calculated for {config['name']}")
        
        return global_importance
    
    def run_complete_shap_study(self) -> Dict:
        """Run complete SHAP ablation study"""
        logger.info("Starting complete SHAP ablation study")
        
        # Load data
        self.load_data()
        
        # Train models
        self.train_models()
        
        # Create SHAP explainers
        self.create_shap_explainers()
        
        # Calculate SHAP values
        self.calculate_shap_values()
        
        # Analyze global importance
        global_importance = self.analyze_global_importance()
        
        # Compile results
        results = {
            'dataset_info': {
                'path': self.data_path,
                'shape': self.data.shape,
                'target_column': self.target_column,
                'features': self.feature_names,
                'target_distribution': self.y.value_counts().to_dict()
            },
            'model_performance': {
                algo_key: {
                    'accuracy': info['accuracy'],
                    'f1_score': info['f1_score'],
                    'algorithm_name': info['config']['name']
                }
                for algo_key, info in self.models.items()
            },
            'global_importance': global_importance,
            'shap_analysis_completed': True,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info("SHAP ablation study completed")
        return results

    def save_results(self, results: Dict, prefix: str = "") -> Tuple[str, str]:
        """Save SHAP analysis results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("results/shap_ablation_study")
        results_dir.mkdir(parents=True, exist_ok=True)

        # Add prefix if provided
        file_prefix = f"{prefix}_" if prefix else ""

        # Save main results CSV
        results_file = results_dir / f"{file_prefix}shap_results_{timestamp}.csv"

        # Compile all feature importance data
        all_importance_data = []
        for algo_key, importance_data in results['global_importance'].items():
            for item in importance_data['feature_importance']:
                all_importance_data.append({
                    'algorithm': importance_data['algorithm'],
                    'algorithm_key': algo_key,
                    'feature': item['feature'],
                    'shap_importance': item['shap_importance'],
                    'rank': item['rank']
                })

        importance_df = pd.DataFrame(all_importance_data)
        importance_df.to_csv(results_file, index=False)

        # Save detailed report
        report_file = results_dir / f"{file_prefix}shap_report_{timestamp}.txt"
        report_content = self._generate_shap_report(results)

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Save feature importance by algorithm
        for algo_key, importance_data in results['global_importance'].items():
            algo_file = results_dir / f"{file_prefix}shap_{algo_key}_{timestamp}.csv"
            algo_df = pd.DataFrame(importance_data['feature_importance'])
            algo_df.to_csv(algo_file, index=False)

        # Generate Python code for optimal features
        code_file = results_dir / f"{file_prefix}shap_features_{timestamp}.py"
        code_content = self._generate_shap_features_code(results, timestamp)

        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(code_content)

        logger.info(f"SHAP results saved:")
        logger.info(f"  - Main results: {results_file}")
        logger.info(f"  - Report: {report_file}")
        logger.info(f"  - Features code: {code_file}")

        return str(results_file), str(report_file)

    def _generate_shap_report(self, results: Dict) -> str:
        """Generate comprehensive SHAP analysis report"""
        report = []
        report.append("=" * 80)
        report.append("🔍 SHAP ABLATION STUDY REPORT")
        report.append("=" * 80)

        # Dataset information
        dataset_info = results['dataset_info']
        report.append(f"\n📋 DATASET INFORMATION:")
        report.append(f"   • Dataset Path: {dataset_info['path']}")
        report.append(f"   • Target Column: {dataset_info['target_column']}")
        report.append(f"   • Total Features: {len(dataset_info['features'])}")
        report.append(f"   • Total Samples: {dataset_info['shape'][0]}")
        report.append(f"   • Target Distribution: {dataset_info['target_distribution']}")

        # Model performance
        report.append(f"\n🤖 MODEL PERFORMANCE:")
        performance = results['model_performance']
        for algo_key, perf in performance.items():
            report.append(f"   • {perf['algorithm_name']}:")
            report.append(f"     - Accuracy: {perf['accuracy']:.4f}")
            report.append(f"     - F1-Score: {perf['f1_score']:.4f}")

        # Global SHAP importance
        report.append(f"\n🎯 GLOBAL SHAP FEATURE IMPORTANCE:")

        for algo_key, importance_data in results['global_importance'].items():
            report.append(f"\n   📊 {importance_data['algorithm']}:")

            # Top 10 features
            top_features = importance_data['feature_importance'][:10]
            for i, item in enumerate(top_features, 1):
                importance = item['shap_importance']
                feature = item['feature']

                # Add visual indicator
                if importance >= 0.1:
                    icon = "🔥"  # Very high
                elif importance >= 0.05:
                    icon = "⭐"  # High
                elif importance >= 0.02:
                    icon = "🔸"  # Medium
                else:
                    icon = "▫️"   # Low

                report.append(f"     {i:2d}. {icon} {feature}: {importance:.4f}")

        # SHAP vs other methods comparison
        report.append(f"\n🔬 SHAP ANALYSIS ADVANTAGES:")
        report.append(f"   • Theoretically grounded (Shapley values from game theory)")
        report.append(f"   • Individual prediction explanations available")
        report.append(f"   • Captures feature interactions")
        report.append(f"   • Model-agnostic interpretability")
        report.append(f"   • Positive/negative contribution analysis")

        # Feature consistency across algorithms
        report.append(f"\n🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:")

        # Find features that appear in top 5 for multiple algorithms
        top5_features = {}
        for algo_key, importance_data in results['global_importance'].items():
            top5 = [item['feature'] for item in importance_data['feature_importance'][:5]]
            for feature in top5:
                top5_features[feature] = top5_features.get(feature, 0) + 1

        consistent_features = [(f, count) for f, count in top5_features.items() if count > 1]
        consistent_features.sort(key=lambda x: x[1], reverse=True)

        if consistent_features:
            report.append(f"   Most consistent features (appearing in multiple top-5 lists):")
            for feature, count in consistent_features:
                algorithms_count = len(results['global_importance'])
                percentage = (count / algorithms_count) * 100
                report.append(f"     • {feature}: {count}/{algorithms_count} algorithms ({percentage:.1f}%)")
        else:
            report.append(f"   No features consistently appear in top-5 across algorithms")

        # Recommendations
        report.append(f"\n✅ SHAP-BASED RECOMMENDATIONS:")

        # Find the best performing algorithm
        best_algo = max(performance.items(), key=lambda x: x[1]['accuracy'])
        best_algo_key, best_perf = best_algo
        best_importance = results['global_importance'][best_algo_key]

        report.append(f"   • Best performing algorithm: {best_perf['algorithm_name']} ({best_perf['accuracy']:.4f} accuracy)")
        report.append(f"   • Top 5 SHAP features for production:")

        for i, item in enumerate(best_importance['feature_importance'][:5], 1):
            report.append(f"     {i}. {item['feature']} (SHAP: {item['shap_importance']:.4f})")

        report.append(f"\n📊 SHAP ANALYSIS SUMMARY:")
        report.append(f"   • Total algorithms analyzed: {len(results['global_importance'])}")
        report.append(f"   • Total features analyzed: {len(dataset_info['features'])}")
        report.append(f"   • Best accuracy achieved: {best_perf['accuracy']:.4f}")
        report.append(f"   • Analysis timestamp: {results['timestamp']}")

        return "\n".join(report)

    def _generate_shap_features_code(self, results: Dict, timestamp: str) -> str:
        """Generate Python code with SHAP-based feature selections"""

        # Find best performing algorithm
        performance = results['model_performance']
        best_algo = max(performance.items(), key=lambda x: x[1]['accuracy'])
        best_algo_key, best_perf = best_algo
        best_importance = results['global_importance'][best_algo_key]

        code_lines = []
        code_lines.append('"""')
        code_lines.append(f'SHAP-Based Optimal Features - Generated {timestamp}')
        code_lines.append(f'Best Algorithm: {best_perf["algorithm_name"]} (Accuracy: {best_perf["accuracy"]:.4f})')
        code_lines.append('"""')
        code_lines.append('')

        # All features ranked by SHAP importance
        code_lines.append('# All features ranked by SHAP importance')
        code_lines.append('SHAP_RANKED_FEATURES = [')
        for item in best_importance['feature_importance']:
            code_lines.append(f'    "{item["feature"]}",  # SHAP: {item["shap_importance"]:.4f}')
        code_lines.append(']')
        code_lines.append('')

        # Top features by different thresholds
        thresholds = [5, 10, 15]
        for threshold in thresholds:
            if threshold <= len(best_importance['feature_importance']):
                code_lines.append(f'# Top {threshold} SHAP features')
                code_lines.append(f'TOP_{threshold}_SHAP_FEATURES = [')
                for item in best_importance['feature_importance'][:threshold]:
                    code_lines.append(f'    "{item["feature"]}",  # SHAP: {item["shap_importance"]:.4f}')
                code_lines.append(']')
                code_lines.append('')

        # SHAP importance dictionary
        code_lines.append('# SHAP importance scores')
        code_lines.append('SHAP_IMPORTANCE_SCORES = {')
        for item in best_importance['feature_importance']:
            code_lines.append(f'    "{item["feature"]}": {item["shap_importance"]:.6f},')
        code_lines.append('}')
        code_lines.append('')

        # Usage example
        code_lines.append('# Usage example:')
        code_lines.append('# X_shap = X[TOP_5_SHAP_FEATURES]')
        code_lines.append('# model.fit(X_shap, y)')

        return '\n'.join(code_lines)
