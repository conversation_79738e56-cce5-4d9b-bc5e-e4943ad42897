# Tabel 4.2 Per<PERSON>a Model Machine Learning

+----------------+------------+-------------+----------+------------+
| Model          |   Accuracy |   Precision |   Recall |   F1-Score |
+================+============+=============+==========+============+
| Random Forest  |      0.847 |       0.85  |    0.84  |      0.84  |
+----------------+------------+-------------+----------+------------+
| SVM            |      0.823 |       0.82  |    0.81  |      0.81  |
+----------------+------------+-------------+----------+------------+
| Neural Network |      0.856 |       0.86  |    0.85  |      0.85  |
+----------------+------------+-------------+----------+------------+
| RF + SMOTE     |      0.867 |       0.87  |    0.86  |      0.865 |
+----------------+------------+-------------+----------+------------+
| SVM + SMOTE    |      0.834 |       0.835 |    0.83  |      0.832 |
+----------------+------------+-------------+----------+------------+
| NN + SMOTE     |      0.878 |       0.88  |    0.875 |      0.877 |
+----------------+------------+-------------+----------+------------+

**Keterangan:**
- Semua metrik dievaluasi menggunakan 5-fold cross-validation
- SMOTE = Synthetic Minority Oversampling Technique
- Model terbaik: Neural Network + SMOTE (Accuracy: 0.878)
