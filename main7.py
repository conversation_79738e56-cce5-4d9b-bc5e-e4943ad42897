"""
Title-Only Fatigue Classification Pipeline with SHAP Feature Analysis
Menggunakan title_only_fatigue_classifier.py dengan SHAP-based Feature Analysis untuk interpretability

BASED ON MAIN3.PY STRUCTURE:
- Uses main3.py as reference template
- Replaces clean_ablation_study with simple_shap_analysis
- Integrates SHAP with multiple algorithms (LR, RF)
- Maintains same title-only pipeline structure and modes
- Provides comprehensive SHAP analysis with title-only feature filtering

DESIGNED FOR TITLE_ONLY_FATIGUE_CLASSIFIER.PY:
- Integrates title_only_fatigue_classifier.py with feature_filter3.py
- Handles pure title analysis without behavioral features
- Prevents data leakage from title analysis process
- Provides safe datasets for SHAP training
- Supports multiple pipeline modes

PIPELINE MODES:
1. Complete Pipeline (default): Data Processing + Title-Only Fatigue Prediction + SHAP ML
2. Title-Only Only (--title-only-only): Title-Only Classification + Feature Filtering + SHAP ML
3. No ML (--no-ml): Data Processing only
4. ML Only (--ml-only): SHAP ML pipeline only

TITLE-ONLY FEATURES:
- Pure title analysis without behavioral data
- Keyword extraction and counting
- Linguistic feature analysis
- Emotional indicator detection
- Title complexity scoring

SHAP FEATURE ANALYSIS:
- Automatically removes title analysis features (title_*)
- Removes title indicators and scores
- Prevents data leakage from title analysis process
- Uses SHAP-style permutation importance for optimal feature interpretation
- Provides feature contribution analysis with individual prediction explanations
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from title_only_fatigue_classifier import TitleOnlyFatigueClassifier
from feature_filter3 import FeatureFilter3
from simple_shap_analysis import SimpleSHAPAnalysis


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('title_only_shap_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TitleOnlySHAPAnalysisPipeline:
    """
    Complete analysis pipeline for title-only fatigue prediction with SHAP feature analysis
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()
        self.feature_filter = FeatureFilter3()
        
        if include_ml:
            self.title_classifier = TitleOnlyFatigueClassifier()
        
        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("results/simple_shap_analysis").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_data_processing(self):
        """Run data processing only"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING")
        logger.info("="*60)

        # Step 1: Data Processing
        logger.info("Step 1: Processing raw data...")
        processed_data = self.data_processor.process_all()
        logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")

        return processed_data
    
    def run_title_only_fatigue_prediction(self, processed_data=None):
        """Run title-only fatigue prediction with SHAP feature analysis"""
        logger.info("="*60)
        logger.info("PHASE 2: TITLE-ONLY FATIGUE RISK PREDICTION WITH SHAP FEATURE ANALYSIS & ML MODELS")
        logger.info("="*60)
        
        # Step 1: Title-Only Fatigue Classification
        logger.info("Step 1: Running title-only fatigue classification...")

        # Process classification using the classifier instance
        data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
        classified_data = self.title_classifier.process_title_only_classification(data_path)
        report = self.title_classifier.generate_title_classification_report(classified_data)

        # Save results
        output_path = "dataset/processed/fatigue_classified_with_title_only.csv"
        classified_data.to_csv(output_path, index=False)
        logger.info(f"Title-only classification results saved to: {output_path}")
        
        # Generate summary for title-only results
        summary = self._generate_title_only_summary(classified_data, report)
        logger.info(f"✅ Title-only fatigue classification completed. Distribution: {summary['fatigue_percentages']}")
        
        # Step 2: Feature Filtering for ML Safety (Title-Only Specific)
        title_classified_path = "dataset/processed/fatigue_classified_with_title_only.csv"
        if Path(title_classified_path).exists():
            # Step 2a: Create safe dataset (remove title-based label creation features)
            logger.info("Step 2a: Applying title-only feature filtering to prevent data leakage...")
            safe_ml_path = "dataset/processed/safe_ml_title_only_dataset.csv"

            safe_path = self.feature_filter.create_safe_dataset_for_title_only_classifier(
                input_path=title_classified_path,
                output_path=safe_ml_path,
                target_column='title_fatigue_risk'
            )
            logger.info(f"✅ Title-only feature filtering completed. Safe dataset: {safe_path}")

            # Step 2b: Analyze Title-Only Features
            logger.info("Step 2b: Analyzing title-only features...")
            title_analysis = self.feature_filter.analyze_title_only_features(pd.read_csv(title_classified_path))
            logger.info(f"✅ Title-only analysis completed. "
                       f"Found {len(title_analysis['title_features'])} title features")

            # Step 3: SHAP ML Pipeline with Safe Features
            logger.info("Step 3: Running SHAP ML pipeline with title-only safe features...")

            # Check which safe datasets are available (prioritize current pipeline's output)
            safe_datasets = [
                {
                    'path': safe_ml_path,  # Current pipeline output (highest priority)
                    'target': 'title_fatigue_risk',
                    'name': 'Current Pipeline Output (Title-Only)',
                    'priority': 1
                },
                {
                    'path': 'dataset/processed/safe_ml_fatigue_dataset.csv',
                    'target': 'fatigue_risk',
                    'name': 'Regular Fatigue Classification',
                    'priority': 2
                },
                {
                    'path': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
                    'target': 'corrected_fatigue_risk',
                    'name': 'Bias-Corrected Fatigue Classification',
                    'priority': 3
                }
            ]

            # Find the first available safe dataset
            selected_dataset = None
            for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
                if Path(dataset['path']).exists():
                    selected_dataset = dataset
                    break

            if selected_dataset:
                logger.info(f"📁 Using safe dataset: {selected_dataset['path']}")
                logger.info(f"🎯 Target column: {selected_dataset['target']}")
                logger.info(f"📋 Dataset type: {selected_dataset['name']}")

                logger.info("Step 3a: Running SHAP analysis with title-only safe features...")
                shap_analyzer = SimpleSHAPAnalysis(
                    data_path=selected_dataset['path'],
                    target_column=selected_dataset['target'],
                    random_state=42
                )
                shap_results = shap_analyzer.run_analysis()

                # Save SHAP results
                results_file, report_file = shap_analyzer.save_results(shap_results, prefix="title_only_safe")
                logger.info(f"✅ SHAP analysis results saved:")
                logger.info(f"   • Results: {results_file}")
                logger.info(f"   • Report: {report_file}")
                
                # Extract optimal result from SHAP analysis
                best_model_name = None
                best_accuracy = 0.0
                best_features = []
                
                if 'model_performance' in shap_results:
                    # Find best performing model
                    best_model = max(shap_results['model_performance'].items(), key=lambda x: x[1]['accuracy'])
                    best_model_name = best_model[0]
                    best_accuracy = best_model[1]['accuracy']
                    
                    # Get top features from best model
                    if best_model_name in shap_results.get('feature_importance', {}):
                        importance_list = shap_results['feature_importance'][best_model_name]
                        best_features = [item['feature'] for item in importance_list[:5]]

                logger.info("Step 3b: Advanced model step skipped (clean_advanced_model removed)")
                # Advanced model functionality removed
                advanced_result = {
                    'accuracy_mean': 0.0,
                    'f1_mean': 0.0,
                    'precision_mean': 0.0,
                    'recall_mean': 0.0
                }

                # Determine best model (SHAP vs Advanced)
                final_best_accuracy = max(best_accuracy, advanced_result['accuracy_mean'])
                
                if final_best_accuracy == best_accuracy:
                    final_best_model = f"SHAP_Analysis_{best_model_name.replace('_', '_').title()}"
                else:
                    final_best_model = "Advanced_Model"

                return {
                    'classification_summary': summary,
                    'title_analysis': title_analysis,
                    'safe_dataset_path': selected_dataset['path'],
                    'safe_dataset_type': selected_dataset['name'],
                    'target_column': selected_dataset['target'],
                    'shap_results': shap_results,
                    'shap_best_model': best_model_name,
                    'shap_best_accuracy': best_accuracy,
                    'shap_top_features': best_features,
                    'advanced_result': advanced_result,
                    'best_accuracy': final_best_accuracy,
                    'best_model': f"{final_best_model}_Title_Only_Safe_Features",
                    'best_algorithm': best_model_name or 'Unknown',
                    'optimal_features_count': len(best_features),
                    'data_leakage_prevented': True,
                    'title_only_applied': True,
                    'shap_analysis_applied': True
                }
            else:
                logger.warning("❌ No safe dataset found!")
                logger.warning("Available safe datasets to check:")
                for dataset in safe_datasets:
                    status = "✅ EXISTS" if Path(dataset['path']).exists() else "❌ NOT FOUND"
                    logger.warning(f"   • {dataset['path']} - {status}")
                logger.warning("Please run the pipeline first to generate safe datasets")
                return {
                    'classification_summary': summary,
                    'title_analysis': title_analysis,
                    'safe_dataset_path': None,
                    'safe_dataset_type': None,
                    'target_column': None,
                    'best_accuracy': None,
                    'best_model': 'Title_Only_Classification_Only',
                    'data_leakage_prevented': False,
                    'title_only_applied': True,
                    'shap_analysis_applied': False
                }
        else:
            logger.warning(f"Title-only classified data not found at {title_classified_path}")
            return {
                'classification_summary': summary,
                'title_analysis': None,
                'safe_dataset_path': None,
                'best_accuracy': None,
                'best_model': 'Title_Only_Classification_Only',
                'data_leakage_prevented': False,
                'title_only_applied': True,
                'shap_analysis_applied': False
            }

    def run_title_only_only_pipeline(self):
        """Execute title-only classification with SHAP feature analysis only"""
        logger.info("🚀 STARTING TITLE-ONLY FATIGUE CLASSIFICATION PIPELINE WITH SHAP")
        logger.info("Includes: Title-Only Classification + Feature Filtering + SHAP ML Models")
        logger.info("="*80)

        try:
            # Check if processed data exists
            processed_data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            if not Path(processed_data_path).exists():
                logger.error(f"Processed data not found: {processed_data_path}")
                logger.error("Please run data processing first or use --complete flag")
                return None

            # Load processed data for summary
            processed_data = pd.read_csv(processed_data_path)
            logger.info(f"✅ Loaded processed data: {processed_data.shape}")

            # Run title-only prediction with SHAP feature analysis
            ml_results = self.run_title_only_fatigue_prediction(processed_data)

            # Print summary
            self._print_title_only_only_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 TITLE-ONLY CLASSIFICATION PIPELINE WITH SHAP FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Title-only pipeline failed: {str(e)}")
            raise

    def _generate_title_only_summary(self, classified_data, report):
        """Generate summary for title-only classification"""

        # Count fatigue risk distribution
        fatigue_counts = classified_data['title_fatigue_risk'].value_counts()
        total_count = len(classified_data)

        fatigue_percentages = {}
        for risk_level in ['high_risk', 'medium_risk', 'low_risk']:
            count = fatigue_counts.get(risk_level, 0)
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            fatigue_percentages[risk_level] = percentage

        # Title analysis summary
        title_analysis_summary = {}
        if 'title_analysis' in report:
            title_analysis_summary = report['title_analysis']

        return {
            'total_observations': total_count,
            'fatigue_counts': fatigue_counts.to_dict(),
            'fatigue_percentages': fatigue_percentages,
            'title_analysis_summary': title_analysis_summary
        }

    def _print_title_only_only_summary(self, data, ml_results):
        """Print summary for title-only only pipeline with SHAP"""

        print("\n" + "="*80)
        print("🎉 TITLE-ONLY FATIGUE CLASSIFICATION PIPELINE WITH SHAP SUMMARY")
        print("="*80)

        # Data Summary
        print(f"\n📊 DATA SUMMARY:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # Title-Only Analysis Summary
        if ml_results and 'title_analysis' in ml_results:
            title_analysis = ml_results['title_analysis']
            print(f"\n📝 TITLE-ONLY ANALYSIS:")
            print(f"   • Title features removed: {len(title_analysis.get('title_features', []))}")
            print(f"   • Title indicators removed: {len(title_analysis.get('title_indicators', []))}")
            print(f"   • Safe features retained: {title_analysis.get('safe_features_count', 0)}")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 TITLE-ONLY FATIGUE PREDICTION & SHAP ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Title-Only Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            # Title-only status
            if ml_results.get('title_only_applied'):
                print(f"   • 📝 Title-Only Analysis: ✅ APPLIED")
            else:
                print(f"   • 📝 Title-Only Analysis: ❌ NOT APPLIED")

            # SHAP analysis status
            if ml_results.get('shap_analysis_applied'):
                print(f"   • 🔍 SHAP Analysis: ✅ APPLIED")
                print(f"   • SHAP Best Model: {ml_results.get('shap_best_model', 'Unknown')}")
                print(f"   • SHAP Best Accuracy: {ml_results.get('shap_best_accuracy', 0.0):.4f}")

                # Show top SHAP features
                if ml_results.get('shap_top_features'):
                    print(f"   • Top SHAP Features: {', '.join(ml_results['shap_top_features'][:3])}")
            else:
                print(f"   • 🔍 SHAP Analysis: ❌ NOT APPLIED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best Overall Model: {ml_results['best_model']}")
                print(f"   • Best Algorithm: {ml_results.get('best_algorithm', 'Unknown')}")
                print(f"   • Optimal Features: {ml_results.get('optimal_features_count', 0)}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/fatigue_classified_with_title_only.csv")
        if ml_results and ml_results.get('safe_dataset_path'):
            print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
        if ml_results and ml_results.get('best_accuracy'):
            print(f"   • results/simple_shap_analysis/title_only_safe_simple_shap_results_*.csv (SHAP analysis)")
            print(f"   • results/simple_shap_analysis/title_only_safe_simple_shap_report_*.txt (SHAP report)")
            print(f"   • results/clean_production_model/*.pkl")
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Models trained with SHAP-analyzed title-only safe features")

        print(f"\n✅ Title-only fatigue classification with SHAP completed successfully!")
        print("="*80)

    def run_complete_pipeline(self):
        """Execute complete analysis pipeline with title-only and SHAP"""
        logger.info("🚀 STARTING COMPLETE TITLE-ONLY ANALYSIS PIPELINE WITH SHAP")
        logger.info("Includes: Data Processing + Title-Only Fatigue Prediction + SHAP ML Models")
        logger.info("="*80)

        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()

            # Phase 2: Title-Only Fatigue Prediction with SHAP (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_title_only_fatigue_prediction(processed_data)

            # Final Summary
            self._print_final_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 COMPLETE TITLE-ONLY PIPELINE WITH SHAP FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise

    def _print_final_summary(self, data, ml_results):
        """Print comprehensive final summary with title-only and SHAP"""

        print("\n" + "="*80)
        print("🎉 COMPLETE TITLE-ONLY ANALYSIS PIPELINE WITH SHAP SUMMARY")
        print("="*80)

        # Data Processing Summary
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # Title-Only Analysis Summary
        if ml_results and 'title_analysis' in ml_results:
            title_analysis = ml_results['title_analysis']
            print(f"\n📝 TITLE-ONLY ANALYSIS:")
            print(f"   • Title features removed: {len(title_analysis.get('title_features', []))}")
            print(f"   • Title indicators removed: {len(title_analysis.get('title_indicators', []))}")
            print(f"   • Safe features retained: {title_analysis.get('safe_features_count', 0)}")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 TITLE-ONLY FATIGUE PREDICTION & SHAP ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Title-Only Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            # Title-only status
            if ml_results.get('title_only_applied'):
                print(f"   • 📝 Title-Only Analysis: ✅ APPLIED")
            else:
                print(f"   • 📝 Title-Only Analysis: ❌ NOT APPLIED")

            # SHAP analysis status
            if ml_results.get('shap_analysis_applied'):
                print(f"   • 🔍 SHAP Analysis: ✅ APPLIED")
                print(f"   • SHAP Best Model: {ml_results.get('shap_best_model', 'Unknown')}")
                print(f"   • SHAP Best Accuracy: {ml_results.get('shap_best_accuracy', 0.0):.4f}")

                # Show top SHAP features
                if ml_results.get('shap_top_features'):
                    print(f"   • Top SHAP Features: {', '.join(ml_results['shap_top_features'][:3])}")
            else:
                print(f"   • 🔍 SHAP Analysis: ❌ NOT APPLIED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best Overall Model: {ml_results['best_model']}")
                print(f"   • Best Algorithm: {ml_results.get('best_algorithm', 'Unknown')}")
                print(f"   • Optimal Features: {ml_results.get('optimal_features_count', 0)}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

                # Model safety note
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • ✅ Model trained with SHAP-analyzed title-only safe features (no data leakage)")
                else:
                    print(f"   • ⚠️ Model may have data leakage risk")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if ml_results:
            print(f"   • dataset/processed/fatigue_classified_with_title_only.csv")
            if ml_results.get('safe_dataset_path'):
                print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
            if ml_results.get('best_accuracy'):
                print(f"   • results/simple_shap_analysis/title_only_safe_simple_shap_results_*.csv (SHAP analysis)")
                print(f"   • results/simple_shap_analysis/title_only_safe_simple_shap_report_*.txt (SHAP report)")
                print(f"   • results/clean_production_model/*.pkl")
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • 🛡️ Models trained with SHAP-analyzed title-only safe features")

        print(f"\n✅ All title-only analyses with SHAP completed successfully!")
        print("="*80)


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Complete Title-Only Analysis Pipeline with SHAP Feature Analysis')
    parser.add_argument('--no-ml', action='store_true',
                       help='Skip machine learning components (data processing only)')
    parser.add_argument('--ml-only', action='store_true',
                       help='Run only SHAP machine learning pipeline')
    parser.add_argument('--title-only-only', action='store_true',
                       help='Run only title-only classification with SHAP feature analysis')
    parser.add_argument('--complete', action='store_true',
                       help='Run complete pipeline with title-only and SHAP (default behavior)')

    args = parser.parse_args()

    try:
        # Check if raw data exists (unless ml-only)
        if not args.ml_only:
            raw_data_path = Path("dataset/raw")
            if not raw_data_path.exists():
                print("❌ Raw data directory not found!")
                print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
                return

            strava_file = raw_data_path / "strava.csv"
            pomokit_file = raw_data_path / "pomokit.csv"

            if not strava_file.exists() or not pomokit_file.exists():
                print("❌ Required data files not found!")
                print(f"Expected files:")
                print(f"  - {strava_file}")
                print(f"  - {pomokit_file}")
                return

        # Run appropriate pipeline
        if args.ml_only:
            # Create safe dataset first
            print("🛡️  Creating safe title-only ML dataset with SHAP analysis...")
            safe_ml_path = "dataset/processed/safe_ml_title_only_dataset.csv"

            # Check if safe dataset exists, if not create it
            if not Path(safe_ml_path).exists():
                from src.title_only_fatigue_classifier import TitleOnlyFatigueClassifier
                from src.feature_filter3 import FeatureFilter3

                # Need to run title-only classification first
                title_classifier = TitleOnlyFatigueClassifier()
                classified_data = title_classifier.process_title_only_classification(
                    'dataset/processed/weekly_merged_dataset_with_gamification.csv'
                )
                classified_output = "dataset/processed/fatigue_classified_with_title_only.csv"
                classified_data.to_csv(classified_output, index=False)

                # Then create safe dataset
                feature_filter = FeatureFilter3()
                feature_filter.create_safe_dataset_for_title_only_classifier(
                    classified_output,
                    safe_ml_path,
                    target_column='title_fatigue_risk'
                )

            # Run SHAP analysis with safe dataset
            print("🔍 Running SHAP Analysis with permutation importance on title-only data...")
            shap_analyzer = SimpleSHAPAnalysis(
                data_path=safe_ml_path,
                target_column='title_fatigue_risk',
                random_state=42
            )
            shap_results = shap_analyzer.run_analysis()

            # Save results
            results_file, report_file = shap_analyzer.save_results(shap_results, prefix="title_only_ml_only")

            print(f"\n🎉 Title-Only SHAP ML Pipeline Completed!")
            print(f"📋 Results: {results_file}")
            print(f"📋 Report: {report_file}")

            # Show best results
            if 'model_performance' in shap_results:
                best_model = max(shap_results['model_performance'].items(), key=lambda x: x[1]['accuracy'])
                best_model_name, best_perf = best_model
                print(f"\n🏆 Best SHAP Configuration:")
                print(f"   • Algorithm: {best_model_name.replace('_', ' ').title()}")
                print(f"   • Accuracy: {best_perf['accuracy']:.4f} ({best_perf['accuracy']*100:.2f}%)")
                print(f"   • F1-Score: {best_perf['f1_score']:.4f}")

                # Show top features
                if best_model_name in shap_results.get('feature_importance', {}):
                    importance_list = shap_results['feature_importance'][best_model_name]
                    print(f"   • Top 5 Features:")
                    for item in importance_list[:5]:
                        print(f"     {item['rank']}. {item['feature']}: {item['importance']:.4f}")

        elif args.title_only_only:
            # Run only title-only classification with SHAP feature analysis
            print("🚀 Running Title-Only Classification Pipeline with SHAP Feature Analysis...")
            pipeline = TitleOnlySHAPAnalysisPipeline(include_ml=True)
            results = pipeline.run_title_only_only_pipeline()
            print(f"\n🎉 Title-Only Classification Pipeline with SHAP Completed!")
        else:
            # Run complete pipeline with title-only and SHAP (default)
            include_ml = not args.no_ml
            pipeline = TitleOnlySHAPAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()

    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
