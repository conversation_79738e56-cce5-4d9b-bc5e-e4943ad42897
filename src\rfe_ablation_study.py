"""
RFE Ablation Study - Recursive Feature Elimination Analysis
Author: Research Team
Date: 2025-07-18
Purpose: Analyze feature importance using RFE with multiple algorithms (Logistic Regression, Ensemble, SVM)
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_validate, cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.feature_selection import RFE
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from sklearn.inspection import permutation_importance

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RFEAblationStudy:
    """
    RFE-based ablation study for feature importance analysis using multiple algorithms.
    
    This class performs Recursive Feature Elimination with different algorithms
    to understand feature importance and optimal feature sets.
    """
    
    def __init__(self, data_path: str, target_column: str, random_state: int = 42):
        """
        Initialize RFE ablation study.

        Args:
            data_path: Path to the dataset CSV file
            target_column: Name of the target variable column
            random_state: Random seed for reproducibility
        """
        self.data_path = Path(data_path)
        self.target_column = target_column
        self.random_state = random_state
        self.df = None
        self.features = None
        self.results = []

        # Set random seeds for reproducibility
        np.random.seed(random_state)

        # Also set random seed for any potential randomness in pandas/sklearn
        import random
        random.seed(random_state)
        
        # Validation parameters
        self.cv_folds = 5
        self.cv_shuffle = True
        
        # Algorithm configurations
        self.algorithms = {
            'logistic_regression': {
                'estimator': LogisticRegression(
                    random_state=random_state,
                    max_iter=1000,
                    class_weight='balanced',
                    solver='liblinear'
                ),
                'name': 'Logistic Regression'
            },
            'random_forest': {
                'estimator': RandomForestClassifier(
                    random_state=random_state,
                    n_estimators=100,
                    class_weight='balanced',
                    n_jobs=-1
                ),
                'name': 'Random Forest'
            },
            'gradient_boosting': {
                'estimator': GradientBoostingClassifier(
                    random_state=random_state,
                    n_estimators=100
                ),
                'name': 'Gradient Boosting'
            },
            'svm': {
                'estimator': SVC(
                    random_state=random_state,
                    class_weight='balanced',
                    kernel='linear'  # Linear kernel has coef_ attribute for RFE
                ),
                'name': 'Support Vector Machine'
            }
        }
    
    def load_data(self) -> None:
        """Load and validate dataset."""
        if not self.data_path.exists():
            raise FileNotFoundError(f"Dataset not found: {self.data_path}")
        
        logger.info(f"Loading dataset from {self.data_path}")
        self.df = pd.read_csv(self.data_path)
        
        # Validate target column
        if self.target_column not in self.df.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in dataset")
        
        # Get feature columns (exclude target)
        self.features = [col for col in self.df.columns if col != self.target_column]
        
        # Filter numeric features only
        numeric_features = []
        for feature in self.features:
            if self.df[feature].dtype in ['int64', 'float64', 'int32', 'float32']:
                numeric_features.append(feature)
        
        self.features = numeric_features
        
        logger.info(f"Dataset loaded: {len(self.df)} samples, {len(self.features)} features")
        logger.info(f"Features: {self.features}")
        
        # Log target distribution
        target_dist = self.df[self.target_column].value_counts()
        logger.info(f"Target distribution: {target_dist.to_dict()}")
    
    def prepare_data(self, features_to_use: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare data for modeling.
        
        Args:
            features_to_use: List of feature names to include
            
        Returns:
            Tuple of (X, y) arrays
        """
        # Select features and handle missing values
        X = self.df[features_to_use].fillna(0).values
        
        # Encode target variable
        le = LabelEncoder()
        y = le.fit_transform(self.df[self.target_column])
        
        return X, y
    
    def create_model_pipeline(self, algorithm_key: str) -> Pipeline:
        """Create standardized model pipeline for specific algorithm."""
        estimator = self.algorithms[algorithm_key]['estimator']
        return Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', estimator)
        ])
    
    def run_rfe_analysis(self, algorithm_key: str, n_features_range: List[int]) -> List[Dict]:
        """
        Run RFE analysis for specific algorithm with different feature counts.
        
        Args:
            algorithm_key: Key for algorithm configuration
            n_features_range: List of feature counts to test
            
        Returns:
            List of evaluation results for each feature count
        """
        logger.info(f"Running RFE analysis for {self.algorithms[algorithm_key]['name']}")
        
        # Prepare data
        X, y = self.prepare_data(self.features)
        
        # Create base estimator
        base_estimator = self.algorithms[algorithm_key]['estimator']
        
        results = []
        
        for n_features in n_features_range:
            if n_features > len(self.features):
                continue
                
            logger.info(f"  Testing with {n_features} features")
            
            try:
                # Create RFE selector
                rfe = RFE(estimator=base_estimator, n_features_to_select=n_features)
                
                # Create pipeline with RFE
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('rfe', rfe),
                    ('classifier', self.algorithms[algorithm_key]['estimator'])
                ])
                
                # Cross-validation setup
                cv = StratifiedKFold(
                    n_splits=self.cv_folds,
                    shuffle=self.cv_shuffle,
                    random_state=self.random_state
                )
                
                # Perform cross-validation
                scoring = ['accuracy', 'f1_macro', 'precision_macro', 'recall_macro']
                cv_results = cross_validate(
                    pipeline, X, y,
                    cv=cv,
                    scoring=scoring,
                    return_train_score=True,
                    n_jobs=-1
                )
                
                # Get selected features
                pipeline.fit(X, y)
                selected_features = [self.features[i] for i in range(len(self.features)) 
                                   if pipeline.named_steps['rfe'].support_[i]]
                
                # Compile results
                result = {
                    'algorithm': algorithm_key,
                    'algorithm_name': self.algorithms[algorithm_key]['name'],
                    'n_features_selected': n_features,
                    'selected_features': selected_features,
                    'accuracy_mean': cv_results['test_accuracy'].mean(),
                    'accuracy_std': cv_results['test_accuracy'].std(),
                    'f1_macro_mean': cv_results['test_f1_macro'].mean(),
                    'f1_macro_std': cv_results['test_f1_macro'].std(),
                    'precision_macro_mean': cv_results['test_precision_macro'].mean(),
                    'recall_macro_mean': cv_results['test_recall_macro'].mean(),
                    'train_accuracy_mean': cv_results['train_accuracy'].mean(),
                    'overfitting_score': cv_results['train_accuracy'].mean() - cv_results['test_accuracy'].mean(),
                    'status': 'success'
                }
                
                logger.info(f"    Result: {result['accuracy_mean']:.4f} ± {result['accuracy_std']:.4f}")
                results.append(result)
                
            except Exception as e:
                logger.error(f"    Failed: {str(e)}")
                result = {
                    'algorithm': algorithm_key,
                    'algorithm_name': self.algorithms[algorithm_key]['name'],
                    'n_features_selected': n_features,
                    'selected_features': [],
                    'accuracy_mean': 0.0,
                    'accuracy_std': 0.0,
                    'status': f'failed: {str(e)}'
                }
                results.append(result)
        
        return results

    def calculate_feature_importance(self, algorithm_key: str, selected_features: List[str]) -> Dict:
        """
        Calculate comprehensive feature importance including ablation-style analysis.

        Args:
            algorithm_key: Key for algorithm configuration
            selected_features: List of selected feature names

        Returns:
            Dictionary with comprehensive feature importance analysis
        """
        logger.info(f"Calculating comprehensive feature importance for {self.algorithms[algorithm_key]['name']}")

        try:
            # Prepare data with selected features only
            X, y = self.prepare_data(selected_features)

            # Create and train model
            pipeline = self.create_model_pipeline(algorithm_key)
            pipeline.fit(X, y)

            # Calculate baseline accuracy (with all features)
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
            baseline_scores = cross_val_score(pipeline, X, y, cv=cv, scoring='accuracy')
            baseline_accuracy = baseline_scores.mean()

            logger.info(f"Baseline accuracy with all {len(selected_features)} features: {baseline_accuracy:.4f}")

            # Calculate permutation importance
            perm_importance = permutation_importance(
                pipeline, X, y,
                n_repeats=10,
                random_state=self.random_state,
                scoring='accuracy'
            )

            # Create importance dictionary
            importance_scores = perm_importance.importances_mean
            importance_std = perm_importance.importances_std

            # Calculate percentages
            total_importance = np.sum(np.abs(importance_scores))
            if total_importance > 0:
                importance_percentages = (np.abs(importance_scores) / total_importance) * 100
            else:
                importance_percentages = np.zeros_like(importance_scores)

            # Calculate ablation-style metrics for each feature
            logger.info("Calculating ablation-style impact for each feature...")
            feature_importance = []

            for i, feature in enumerate(selected_features):
                # Create dataset without this feature
                features_without_current = [f for f in selected_features if f != feature]

                if len(features_without_current) > 0:
                    X_without, y_without = self.prepare_data(features_without_current)
                    pipeline_without = self.create_model_pipeline(algorithm_key)

                    # Calculate accuracy without this feature
                    scores_without = cross_val_score(pipeline_without, X_without, y_without, cv=cv, scoring='accuracy')
                    accuracy_without = scores_without.mean()

                    # Calculate impact when removed
                    impact_when_removed = baseline_accuracy - accuracy_without
                    impact_percentage = (impact_when_removed / baseline_accuracy) * 100 if baseline_accuracy > 0 else 0
                else:
                    accuracy_without = 0.0
                    impact_when_removed = baseline_accuracy
                    impact_percentage = 100.0

                # Generate interpretation
                if impact_percentage >= 15:
                    interpretation = "CRITICAL - Major performance drop when removed"
                elif impact_percentage >= 10:
                    interpretation = "HIGH - Significant performance impact"
                elif impact_percentage >= 5:
                    interpretation = "MEDIUM - Moderate performance impact"
                elif impact_percentage >= 1:
                    interpretation = "LOW - Minor performance impact"
                else:
                    interpretation = "MINIMAL - Negligible performance impact"

                feature_importance.append({
                    'feature': feature,
                    'baseline_accuracy': baseline_accuracy,
                    'accuracy_without_feature': accuracy_without,
                    'impact_when_removed': impact_when_removed,
                    'importance_score': importance_scores[i],
                    'importance_std': importance_std[i],
                    'importance_percentage': importance_percentages[i],
                    'impact_percentage': impact_percentage,
                    'interpretation': interpretation,
                    'rank': i + 1  # Will be re-ranked later
                })

            # Sort by impact when removed (descending)
            feature_importance.sort(key=lambda x: abs(x['impact_when_removed']), reverse=True)

            # Update ranks based on impact
            for i, item in enumerate(feature_importance):
                item['rank'] = i + 1

            return {
                'feature_importance': feature_importance,
                'baseline_accuracy': baseline_accuracy,
                'total_features': len(selected_features),
                'algorithm': algorithm_key,
                'algorithm_name': self.algorithms[algorithm_key]['name']
            }

        except Exception as e:
            logger.error(f"Failed to calculate feature importance: {str(e)}")
            return {
                'feature_importance': [],
                'baseline_accuracy': 0.0,
                'total_features': len(selected_features),
                'algorithm': algorithm_key,
                'algorithm_name': self.algorithms[algorithm_key]['name'],
                'error': str(e)
            }

    def run_complete_rfe_study(self) -> Dict:
        """
        Run complete RFE study across all algorithms.
        
        Returns:
            Dictionary with all results and analysis
        """
        logger.info("Starting complete RFE ablation study")
        
        # Define feature count range to test
        n_features_range = [
            5, 10, 15, 20, 25, 30,
            int(len(self.features) * 0.25),
            int(len(self.features) * 0.5),
            int(len(self.features) * 0.75),
            len(self.features)
        ]
        # Remove duplicates and sort
        n_features_range = sorted(list(set(n_features_range)))
        
        all_results = []
        
        # Run RFE for each algorithm
        for algorithm_key in self.algorithms.keys():
            algorithm_results = self.run_rfe_analysis(algorithm_key, n_features_range)
            all_results.extend(algorithm_results)
        
        # Store results
        self.results = all_results

        # Analyze results
        analysis = self.analyze_rfe_results(all_results)

        # Calculate feature importance for best overall result
        feature_importance_analysis = None
        if 'best_overall' in analysis and analysis['best_overall']:
            best_result = analysis['best_overall']
            best_algorithm = best_result['algorithm']
            best_features = best_result['selected_features']

            logger.info("Calculating feature importance for best model...")
            feature_importance_analysis = self.calculate_feature_importance(
                best_algorithm, best_features
            )

        logger.info("RFE ablation study completed")

        return {
            'results': all_results,
            'analysis': analysis,
            'feature_importance': feature_importance_analysis,
            'feature_count_range': n_features_range,
            'algorithms_tested': list(self.algorithms.keys())
        }

    def analyze_rfe_results(self, results: List[Dict]) -> Dict:
        """
        Analyze RFE results to find optimal configurations.

        Args:
            results: List of RFE evaluation results

        Returns:
            Dictionary with analysis results
        """
        logger.info("Analyzing RFE results")

        # Convert to DataFrame for easier analysis
        df = pd.DataFrame([r for r in results if r['status'] == 'success'])

        if df.empty:
            return {'error': 'No successful results to analyze'}

        analysis = {}

        # Best performance by algorithm
        analysis['best_by_algorithm'] = {}
        for algorithm in df['algorithm'].unique():
            algo_df = df[df['algorithm'] == algorithm]
            best_idx = algo_df['accuracy_mean'].idxmax()
            best_result = algo_df.loc[best_idx]

            analysis['best_by_algorithm'][algorithm] = {
                'algorithm_name': best_result['algorithm_name'],
                'n_features': best_result['n_features_selected'],
                'accuracy': best_result['accuracy_mean'],
                'accuracy_std': best_result['accuracy_std'],
                'f1_score': best_result['f1_macro_mean'],
                'selected_features': best_result['selected_features'],
                'overfitting_score': best_result.get('overfitting_score', 0.0),
                'train_accuracy': best_result.get('train_accuracy_mean', 0.0)
            }

        # Overall best performance
        best_overall_idx = df['accuracy_mean'].idxmax()
        best_overall = df.loc[best_overall_idx]

        analysis['best_overall'] = {
            'algorithm': best_overall['algorithm'],
            'algorithm_name': best_overall['algorithm_name'],
            'n_features': best_overall['n_features_selected'],
            'accuracy': best_overall['accuracy_mean'],
            'accuracy_std': best_overall['accuracy_std'],
            'f1_score': best_overall['f1_macro_mean'],
            'selected_features': best_overall['selected_features'],
            'overfitting_score': best_overall.get('overfitting_score', 0.0),
            'train_accuracy': best_overall.get('train_accuracy_mean', 0.0)
        }

        # Feature frequency analysis
        feature_frequency = {}
        for _, row in df.iterrows():
            for feature in row['selected_features']:
                if feature not in feature_frequency:
                    feature_frequency[feature] = 0
                feature_frequency[feature] += 1

        # Sort features by frequency
        sorted_features = sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)
        analysis['feature_importance_by_frequency'] = sorted_features

        # Most consistent features (appear in top results)
        top_results = df.nlargest(10, 'accuracy_mean')
        consistent_features = {}
        for _, row in top_results.iterrows():
            for feature in row['selected_features']:
                if feature not in consistent_features:
                    consistent_features[feature] = 0
                consistent_features[feature] += 1

        analysis['most_consistent_features'] = sorted(
            consistent_features.items(), key=lambda x: x[1], reverse=True
        )

        # Performance by feature count
        performance_by_count = df.groupby('n_features_selected').agg({
            'accuracy_mean': ['mean', 'std', 'max'],
            'f1_macro_mean': ['mean', 'std', 'max']
        }).round(4)

        analysis['performance_by_feature_count'] = performance_by_count.to_dict()

        return analysis

    def generate_rfe_report(self, study_results: Dict) -> str:
        """
        Generate comprehensive RFE analysis report.

        Args:
            study_results: Results from complete RFE study

        Returns:
            Formatted report string
        """
        results = study_results['results']
        analysis = study_results['analysis']

        report = []
        report.append("="*80)
        report.append("🔍 RFE ABLATION STUDY REPORT")
        report.append("="*80)

        # Dataset Information
        report.append(f"\n📋 DATASET INFORMATION:")
        report.append(f"   • Dataset Path: {self.data_path}")
        report.append(f"   • Target Column: {self.target_column}")
        report.append(f"   • Total Features: {len(self.features)}")
        report.append(f"   • Total Samples: {len(self.df)}")

        # Algorithms Tested
        report.append(f"\n🤖 ALGORITHMS TESTED:")
        for i, (key, config) in enumerate(self.algorithms.items(), 1):
            report.append(f"   {i}. {config['name']} ({key})")

        # Feature Count Range
        report.append(f"\n📊 FEATURE COUNT RANGE TESTED:")
        report.append(f"   • Range: {study_results['feature_count_range']}")
        report.append(f"   • Total Experiments: {len(results)}")

        # Best Overall Performance
        if 'best_overall' in analysis:
            best = analysis['best_overall']
            report.append(f"\n🏆 BEST OVERALL PERFORMANCE:")
            report.append(f"   • Algorithm: {best['algorithm_name']}")
            report.append(f"   • Features Used: {best['n_features']}")
            report.append(f"   • Accuracy: {best['accuracy']:.4f} ± {best['accuracy_std']:.4f}")
            report.append(f"   • F1-Score: {best['f1_score']:.4f}")

            # Add overfitting check
            if 'overfitting_score' in best:
                overfitting = best['overfitting_score']
                if overfitting > 0.1:  # 10% threshold
                    report.append(f"   • ⚠️ Overfitting Risk: {overfitting:.4f} (HIGH - Train-Test gap > 10%)")
                elif overfitting > 0.05:  # 5% threshold
                    report.append(f"   • ⚠️ Overfitting Risk: {overfitting:.4f} (MODERATE - Train-Test gap > 5%)")
                else:
                    report.append(f"   • ✅ Overfitting Risk: {overfitting:.4f} (LOW - Train-Test gap < 5%)")

        # Best by Algorithm
        if 'best_by_algorithm' in analysis:
            report.append(f"\n🎯 BEST PERFORMANCE BY ALGORITHM:")
            for algo_key, best in analysis['best_by_algorithm'].items():
                report.append(f"   • {best['algorithm_name']}:")
                report.append(f"     - Features: {best['n_features']}")
                report.append(f"     - Accuracy: {best['accuracy']:.4f} ± {best['accuracy_std']:.4f}")
                report.append(f"     - F1-Score: {best['f1_score']:.4f}")

                # Add overfitting check for each algorithm
                if 'overfitting_score' in best:
                    overfitting = best['overfitting_score']
                    if overfitting > 0.1:
                        report.append(f"     - ⚠️ Overfitting: {overfitting:.4f} (HIGH)")
                    elif overfitting > 0.05:
                        report.append(f"     - ⚠️ Overfitting: {overfitting:.4f} (MODERATE)")
                    else:
                        report.append(f"     - ✅ Overfitting: {overfitting:.4f} (LOW)")

        # Most Important Features
        if 'feature_importance_by_frequency' in analysis:
            report.append(f"\n⭐ MOST FREQUENTLY SELECTED FEATURES:")
            top_features = analysis['feature_importance_by_frequency'][:10]
            for i, (feature, count) in enumerate(top_features, 1):
                percentage = (count / len(results)) * 100
                report.append(f"   {i:2d}. {feature}: {count} times ({percentage:.1f}%)")

        # Most Consistent Features
        if 'most_consistent_features' in analysis:
            report.append(f"\n🎖️ MOST CONSISTENT FEATURES (in top 10 results):")
            consistent = analysis['most_consistent_features'][:10]
            for i, (feature, count) in enumerate(consistent, 1):
                report.append(f"   {i:2d}. {feature}: {count}/10 top results")

        # Overfitting Analysis
        report.append(f"\n🔍 OVERFITTING ANALYSIS:")
        if 'best_overall' in analysis:
            best_overfitting = analysis['best_overall']['overfitting_score']
            report.append(f"   • Best Model Overfitting Score: {best_overfitting:.4f}")

            if best_overfitting > 0.1:
                report.append(f"   • ⚠️ HIGH OVERFITTING RISK: Train-Test gap > 10%")
                report.append(f"   • Recommendation: Consider regularization or more data")
            elif best_overfitting > 0.05:
                report.append(f"   • ⚠️ MODERATE OVERFITTING RISK: Train-Test gap > 5%")
                report.append(f"   • Recommendation: Monitor performance on new data")
            else:
                report.append(f"   • ✅ LOW OVERFITTING RISK: Good generalization expected")

        # Overfitting by Algorithm
        if 'best_by_algorithm' in analysis:
            report.append(f"\n   📊 Overfitting by Algorithm:")
            for algo_key, best in analysis['best_by_algorithm'].items():
                overfitting = best.get('overfitting_score', 0.0)
                status = "HIGH" if overfitting > 0.1 else "MODERATE" if overfitting > 0.05 else "LOW"
                icon = "⚠️" if overfitting > 0.05 else "✅"
                report.append(f"     {icon} {best['algorithm_name']}: {overfitting:.4f} ({status})")

        # Feature Importance Analysis
        if study_results.get('feature_importance') and study_results['feature_importance'].get('feature_importance'):
            importance_data = study_results['feature_importance']
            report.append(f"\n📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:")
            report.append(f"   • Algorithm: {importance_data['algorithm_name']}")
            report.append(f"   • Method: Permutation Importance + Ablation Analysis")
            report.append(f"   • Total Features: {importance_data['total_features']}")
            report.append(f"   • Baseline Accuracy: {importance_data.get('baseline_accuracy', 0.0):.4f}")

            report.append(f"\n   🎯 Feature Impact Analysis (Top 10):")
            for item in importance_data['feature_importance'][:10]:  # Top 10
                feature = item['feature']
                baseline = item['baseline_accuracy']
                without_feature = item['accuracy_without_feature']
                impact = item['impact_when_removed']
                impact_pct = item.get('impact_percentage', 0.0)
                interpretation = item['interpretation']
                rank = item['rank']

                # Add visual indicator for impact level
                if impact_pct >= 15:
                    icon = "🔥"  # Critical
                elif impact_pct >= 10:
                    icon = "⭐"  # High
                elif impact_pct >= 5:
                    icon = "🔸"  # Medium
                elif impact_pct >= 1:
                    icon = "▫️"  # Low
                else:
                    icon = "⚪"  # Minimal

                report.append(f"     {rank:2d}. {icon} {feature}:")
                report.append(f"         - Baseline: {baseline:.4f} | Without: {without_feature:.4f}")
                report.append(f"         - Impact: {impact:.4f} ({impact_pct:.2f}%)")
                report.append(f"         - {interpretation}")

            # Add permutation importance summary
            report.append(f"\n   📊 Permutation Importance Summary:")
            for item in importance_data['feature_importance'][:5]:  # Top 5
                feature = item['feature']
                perm_pct = item['importance_percentage']
                perm_score = item['importance_score']
                perm_std = item['importance_std']

                report.append(f"     • {feature}: {perm_pct:.2f}% (score: {perm_score:.4f} ±{perm_std:.4f})")

        # Optimal Feature Set Recommendation
        if 'best_overall' in analysis:
            best = analysis['best_overall']
            report.append(f"\n✅ RECOMMENDED OPTIMAL FEATURE SET:")
            report.append(f"   • Algorithm: {best['algorithm_name']}")
            report.append(f"   • Number of Features: {best['n_features']}")
            report.append(f"   • Selected Features:")
            for i, feature in enumerate(sorted(best['selected_features']), 1):
                report.append(f"     {i:2d}. {feature}")

        report.append("="*80)

        return "\n".join(report)

    def save_results(self, study_results: Dict) -> Tuple[str, str]:
        """
        Save RFE ablation study results.

        Args:
            study_results: Complete study results

        Returns:
            Tuple of (results_file_path, report_file_path)
        """
        # Create results directory
        results_dir = Path("results/rfe_ablation_study")
        results_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save detailed results
        results_file = results_dir / f"rfe_results_{timestamp}.csv"
        results_df = pd.DataFrame(study_results['results'])
        results_df.to_csv(results_file, index=False)

        # Save analysis report
        report_file = results_dir / f"rfe_report_{timestamp}.txt"
        report_content = self.generate_rfe_report(study_results)
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Save optimal feature set code with importance
        if 'best_overall' in study_results['analysis']:
            best = study_results['analysis']['best_overall']
            code_file = results_dir / f"optimal_features_{timestamp}.py"

            code_content = f"""# Optimal Feature Set from RFE Analysis
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Best Algorithm: {best['algorithm_name']}
# Accuracy: {best['accuracy']:.4f} ± {best['accuracy_std']:.4f}

OPTIMAL_FEATURES = [
"""
            for feature in sorted(best['selected_features']):
                code_content += f"    '{feature}',\n"

            code_content += """]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]
"""

            # Add comprehensive feature importance if available
            if study_results.get('feature_importance') and study_results['feature_importance'].get('feature_importance'):
                importance_data = study_results['feature_importance']['feature_importance']
                baseline_acc = study_results['feature_importance'].get('baseline_accuracy', 0.0)

                code_content += f"""
# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = {baseline_acc:.4f}

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {{
"""
                for item in importance_data:
                    feature = item['feature']
                    impact = item['impact_when_removed']
                    impact_pct = item.get('impact_percentage', 0.0)
                    code_content += f"    '{feature}': {impact:.4f},  # {impact_pct:.2f}% impact\n"

                code_content += """}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
"""
                for item in importance_data:
                    feature = item['feature']
                    percentage = item['importance_percentage']
                    code_content += f"    '{feature}': {percentage:.2f},  # {percentage:.2f}%\n"

                code_content += """}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
"""
                for item in importance_data:
                    feature = item['feature']
                    impact_pct = item.get('impact_percentage', 0.0)
                    code_content += f"    '{feature}',  # {impact_pct:.2f}% impact when removed\n"

                code_content += """]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
"""
                for item in importance_data:
                    feature = item['feature']
                    percentage = item['importance_percentage']
                    code_content += f"    '{feature}',  # {percentage:.2f}% permutation importance\n"

                code_content += """]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
"""
                for item in importance_data:
                    feature = item['feature']
                    interpretation = item['interpretation']
                    code_content += f"    '{feature}': '{interpretation}',\n"

                code_content += """}
"""

            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(code_content)

        # Save comprehensive feature importance CSV if available
        if study_results.get('feature_importance') and study_results['feature_importance'].get('feature_importance'):
            importance_file = results_dir / f"feature_importance_{timestamp}.csv"
            importance_data = study_results['feature_importance']['feature_importance']

            # Create comprehensive DataFrame with all columns
            importance_df = pd.DataFrame(importance_data)

            # Reorder columns for better readability
            column_order = [
                'rank', 'feature', 'baseline_accuracy', 'accuracy_without_feature',
                'impact_when_removed', 'impact_percentage', 'importance_score',
                'importance_std', 'importance_percentage', 'interpretation'
            ]

            # Only include columns that exist
            available_columns = [col for col in column_order if col in importance_df.columns]
            importance_df = importance_df[available_columns]

            importance_df.to_csv(importance_file, index=False)
            logger.info(f"Comprehensive feature importance saved to {importance_file}")

        logger.info(f"Results saved to {results_file}")
        logger.info(f"Report saved to {report_file}")

        return str(results_file), str(report_file)


def main():
    """Main function to run RFE ablation study."""

    print("🔍 RFE Ablation Study - Recursive Feature Elimination Analysis")
    print("=" * 70)
    print("📋 Using SAFE dataset with multiple algorithms (LR, Ensemble, SVM)")
    print("=" * 70)

    # Check which safe datasets are available
    safe_datasets = [
        {
            'path': 'dataset/processed/safe_ml_fatigue_dataset.csv',
            'target': 'fatigue_risk',
            'name': 'Regular Fatigue Classification',
            'priority': 1
        },
        {
            'path': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
            'target': 'corrected_fatigue_risk',
            'name': 'Bias-Corrected Fatigue Classification',
            'priority': 2
        },
        {
            'path': 'dataset/processed/safe_ml_title_only_dataset.csv',
            'target': 'title_fatigue_risk',
            'name': 'Title-Only Fatigue Classification',
            'priority': 3
        }
    ]

    # Find the first available safe dataset
    selected_dataset = None
    for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
        if Path(dataset['path']).exists():
            selected_dataset = dataset
            break

    if not selected_dataset:
        print("❌ No safe dataset found!")
        print("Please run one of the main pipelines first:")
        print("  • python main1.py")
        print("  • python main2.py")
        print("  • python main3.py")
        return

    print(f"📁 Dataset: {selected_dataset['path']}")
    print(f"🎯 Target: {selected_dataset['target']}")
    print(f"📋 Type: {selected_dataset['name']}")
    print("=" * 70)

    # Initialize RFE ablation study
    study = RFEAblationStudy(
        data_path=selected_dataset['path'],
        target_column=selected_dataset['target'],
        random_state=42
    )

    # Load data
    study.load_data()

    # Run complete RFE study
    print("\n🚀 Starting RFE Analysis...")
    study_results = study.run_complete_rfe_study()

    # Display results
    print("\n" + study.generate_rfe_report(study_results))

    # Save results
    results_file, report_file = study.save_results(study_results)

    # Display summary
    print("\n🎉 RFE Ablation Study Completed!")
    print("=" * 70)

    if 'best_overall' in study_results['analysis']:
        best = study_results['analysis']['best_overall']
        print(f"\n🏆 BEST CONFIGURATION:")
        print(f"  • Algorithm: {best['algorithm_name']}")
        print(f"  • Features: {best['n_features']}")
        print(f"  • Accuracy: {best['accuracy']:.4f} ± {best['accuracy_std']:.4f}")
        print(f"  • F1-Score: {best['f1_score']:.4f}")

    print(f"\n📁 FILES SAVED:")
    print(f"  • Results: {results_file}")
    print(f"  • Report: {report_file}")

    return study, study_results


if __name__ == "__main__":
    main()
