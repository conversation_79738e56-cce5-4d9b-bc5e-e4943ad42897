#!/usr/bin/env python3
"""
Feature Filter 3 untuk title_only_fatigue_classifier.py
Memisahkan fitur pembuat label dari fitur yang aman untuk model ML

Modul ini dirancang khusus untuk bekerja dengan:
- title_only_fatigue_classifier.py (output: fatigue_classified_with_title_only.csv)

PRINSIP UTAMA:
1. Fitur yang digunakan untuk MEMBUAT LABEL tidak boleh digunakan untuk model ML
2. Hanya fitur yang benar-benar independen dari proses title analysis dan labeling yang aman
3. Fitur metadata hanya untuk identifikasi, bukan untuk prediksi

CONTOH PENGGUNAAN:
```python
from src.feature_filter3 import FeatureFilter3

# Untuk output title_only_fatigue_classifier.py
filter_obj = FeatureFilter3()
safe_dataset = filter_obj.create_safe_dataset_for_title_only_classifier(
    'dataset/processed/fatigue_classified_with_title_only.csv',
    'dataset/processed/safe_ml_title_only_dataset.csv',
    target_column='title_fatigue_risk'
)

# Validasi fitur sebelum training
features = ['total_distance_km', 'avg_distance_km', 'activity_points']
validation = filter_obj.validate_features_for_ml(features)
if validation['is_safe']:
    print("✅ Fitur aman untuk ML")
else:
    print(f"🚨 Fitur berbahaya: {validation['dangerous_features']}")
```
"""

import pandas as pd
import logging
from typing import List, Dict, Tuple

logger = logging.getLogger(__name__)

class FeatureFilter3:
    """
    Filter khusus untuk title_only_fatigue_classifier.py
    Memisahkan fitur pembuat label dari fitur model
    """
    
    def __init__(self):
        """Initialize feature filter dengan definisi fitur untuk title-only classifier"""
        
        # Fitur yang digunakan untuk MEMBUAT LABEL (TIDAK BOLEH digunakan untuk model)
        self.label_creation_features = {
            # Title-only features (dari extract_title_features)
            'title_stress_count',
            'title_workload_count',
            'title_negative_emotion_count',
            'title_recovery_count',
            'title_time_pressure_count',
            'title_exhaustion_count',
            'title_total_words',
            'title_unique_words',
            'title_title_length',
            'title_activity_count',
            'title_avg_word_length',
            'title_exclamation_count',
            'title_question_count',
            'title_caps_ratio',
            'title_number_count',
            
            # Calculated scores dan classification (dari calculate_title_based_fatigue_score & classify_fatigue_risk_title_only)
            'title_fatigue_score',      # SKOR YANG DIGUNAKAN UNTUK MEMBUAT LABEL
            'title_fatigue_risk',       # TARGET VARIABLE
            
            # Additional title-based indicators (dari process_title_only_classification)
            'title_stress_indicator',
            'title_workload_indicator',
            'title_negative_indicator',
            'title_recovery_indicator',
            'title_exhaustion_indicator',
            'title_complexity_score',
            'title_emotional_intensity',
            
            # Original uncorrected features (jika ada dari classifier lain)
            'stress_count',
            'workload_count',
            'negative_emotion_count',
            'recovery_count',
            'time_pressure_count',
            'exhaustion_count',
            'fatigue_risk_score',
            'fatigue_risk',
            
            # Bias-corrected features (jika ada dari bias_corrected_title_classifier.py)
            'corrected_stress_count',
            'corrected_workload_count', 
            'corrected_negative_emotion_count',
            'corrected_recovery_count',
            'corrected_time_pressure_count',
            'corrected_exhaustion_count',
            'corrected_total_words',
            'corrected_unique_words',
            'corrected_title_length',
            'corrected_activity_count',
            'corrected_linguistic_complexity',
            'corrected_emotional_intensity',
            'corrected_fatigue_score',
            'corrected_fatigue_risk',
            'language_pattern',
            'activity_type',
            
            # Behavioral features (jika ada dari fatigue_classifier.py)
            'work_intensity',
            'work_life_imbalance',
            'activity_deficit',
            'consistency_deficit',
            'workaholic_pattern',
            'recovery_deficit',
            'chronic_stress_pattern',
            'time_pressure_syndrome'
        }
        
        # Fitur metadata (untuk identifikasi, bukan untuk model)
        self.metadata_features = {
            'identity',
            'year_week',
            'strava_activities_titles',
            'pomokit_activities_titles',
            'combined_titles'
        }
        
        # Fitur yang AMAN untuk model ML
        self.safe_model_features = {
            # Physical activity features (dari raw data Strava)
            'total_distance_km',
            'avg_distance_km', 
            'total_time_minutes',
            'avg_time_minutes',
            'total_activities',
            'avg_activities_per_week',
            
            # Basic title statistics (non-semantic, structural only)
            'strava_title_count',
            'strava_unique_words',
            'pomokit_title_count',
            'pomokit_unique_words',
            'total_title_diversity',
            'title_balance_ratio',
            'avg_title_length',
            'total_unique_words',
            
            # Gamification features (dari sistem, bukan dari analisis title)
            'activity_points',
            'productivity_points',
            'achievement_rate',
            'gamification_balance',
            'total_points',
            'points_balance',
            
            # Raw behavioral data (INPUT untuk classifier, bukan hasil perhitungan)
            'work_days',            # Input: jumlah hari kerja (dari data asli)
            'activity_days',        # Input: jumlah hari aktivitas fisik (dari data asli)
            'total_cycles',         # Input: total siklus kerja (dari data asli)
            'consistency_score',    # Input: skor konsistensi (dari data asli)
            
            # Temporal features (aman karena tidak semantic)
            'week_number',
            'month',
            'quarter',
            'is_weekend_heavy',
            'activity_frequency',
            'activity_regularity'
        }
    
    def filter_features_for_ml(self, df: pd.DataFrame, target_column: str = 'title_fatigue_risk') -> Tuple[pd.DataFrame, List[str], Dict[str, List[str]]]:
        """
        Filter dataset untuk ML, menghilangkan fitur pembuat label
        
        Args:
            df: DataFrame input
            target_column: Nama kolom target
            
        Returns:
            Tuple of (filtered_df, safe_features, removed_features_info)
        """
        logger.info("🔍 Filtering features untuk mencegah data leakage (title-only)...")
        
        # Identifikasi fitur yang ada di dataset
        available_features = set(df.columns)
        
        # Kategorisasi fitur
        found_label_features = available_features.intersection(self.label_creation_features)
        found_metadata_features = available_features.intersection(self.metadata_features)
        found_safe_features = available_features.intersection(self.safe_model_features)
        
        # Fitur yang tidak terkategorikan (perlu review manual)
        uncategorized_features = available_features - found_label_features - found_metadata_features - found_safe_features
        if target_column in uncategorized_features:
            uncategorized_features.remove(target_column)
        
        # Fitur yang akan digunakan untuk model
        model_features = list(found_safe_features)
        
        # Buat filtered dataset
        columns_to_keep = model_features + [target_column]
        filtered_df = df[columns_to_keep].copy()
        
        # Info tentang fitur yang dihilangkan
        removed_features_info = {
            'label_creation_features': list(found_label_features),
            'metadata_features': list(found_metadata_features),
            'uncategorized_features': list(uncategorized_features)
        }
        
        # Log hasil filtering
        logger.info(f"📊 Feature filtering results (title-only):")
        logger.info(f"   • Total features in dataset: {len(available_features)}")
        logger.info(f"   • Safe model features: {len(model_features)}")
        logger.info(f"   • Label creation features (REMOVED): {len(found_label_features)}")
        logger.info(f"   • Metadata features (REMOVED): {len(found_metadata_features)}")
        logger.info(f"   • Uncategorized features (REMOVED): {len(uncategorized_features)}")
        logger.info(f"   • Final dataset shape: {filtered_df.shape}")
        
        if found_label_features:
            logger.warning(f"⚠️  REMOVED label creation features: {sorted(found_label_features)}")
        
        if uncategorized_features:
            logger.warning(f"⚠️  REMOVED uncategorized features: {sorted(uncategorized_features)}")
            logger.warning(f"   Please review these features manually to determine if they're safe for ML")
        
        return filtered_df, model_features, removed_features_info
    
    def validate_features_for_ml(self, features: List[str]) -> Dict[str, List[str]]:
        """
        Validasi daftar fitur untuk memastikan tidak ada data leakage
        
        Args:
            features: List fitur yang akan digunakan
            
        Returns:
            Dict dengan hasil validasi
        """
        features_set = set(features)
        
        # Cek fitur berbahaya
        dangerous_features = features_set.intersection(self.label_creation_features)
        metadata_features = features_set.intersection(self.metadata_features)
        safe_features = features_set.intersection(self.safe_model_features)
        uncategorized_features = features_set - dangerous_features - metadata_features - safe_features
        
        validation_result = {
            'safe_features': list(safe_features),
            'dangerous_features': list(dangerous_features),
            'metadata_features': list(metadata_features),
            'uncategorized_features': list(uncategorized_features),
            'is_safe': len(dangerous_features) == 0
        }
        
        return validation_result
    
    def get_feature_explanation(self, feature_name: str) -> str:
        """
        Berikan penjelasan tentang kategori fitur
        
        Args:
            feature_name: Nama fitur
            
        Returns:
            Penjelasan kategori fitur
        """
        if feature_name in self.label_creation_features:
            return "🚨 LABEL CREATION - Fitur ini digunakan untuk membuat label, TIDAK BOLEH digunakan untuk model ML"
        elif feature_name in self.metadata_features:
            return "🏷️ METADATA - Fitur identifikasi, tidak untuk model ML"
        elif feature_name in self.safe_model_features:
            return "✅ SAFE - Fitur aman untuk model ML"
        else:
            return "❓ UNCATEGORIZED - Perlu review manual untuk menentukan keamanan"

    def create_safe_dataset_for_title_only_classifier(self, input_path: str, output_path: str, target_column: str = 'title_fatigue_risk') -> str:
        """
        Buat dataset yang aman untuk ML dari output title_only_fatigue_classifier.py

        Args:
            input_path: Path file input (output dari title_only_fatigue_classifier.py)
            output_path: Path file output yang aman untuk ML
            target_column: Nama kolom target (default: 'title_fatigue_risk')

        Returns:
            Path file output yang dibuat
        """
        logger.info(f"📂 Creating safe ML dataset from title-only classifier output: {input_path}")

        # Load dataset
        df = pd.read_csv(input_path)
        logger.info(f"📊 Loaded dataset: {df.shape}")

        # Filter features untuk title-only classifier output
        safe_df, safe_features, removed_info = self.filter_features_for_ml(df, target_column)

        # Save safe dataset
        safe_df.to_csv(output_path, index=False)
        logger.info(f"💾 Safe dataset saved to: {output_path}")
        logger.info(f"📊 Safe dataset shape: {safe_df.shape}")

        # Print summary
        print(f"\n🛡️  SAFE ML DATASET CREATED (from title_only_fatigue_classifier)")
        print(f"📁 Input: {input_path}")
        print(f"📁 Output: {output_path}")
        print(f"📊 Shape: {df.shape} → {safe_df.shape}")
        print(f"✅ Safe features: {len(safe_features)}")
        print(f"🚨 Removed dangerous features: {len(removed_info['label_creation_features'])}")
        print(f"🎯 Target column: {target_column}")

        return output_path

    def create_safe_dataset(self, input_path: str, output_path: str, target_column: str = 'title_fatigue_risk') -> str:
        """
        Buat dataset yang aman untuk ML dari file input (alias untuk compatibility)

        Args:
            input_path: Path file input
            output_path: Path file output
            target_column: Nama kolom target

        Returns:
            Path file output yang dibuat
        """
        return self.create_safe_dataset_for_title_only_classifier(input_path, output_path, target_column)

    def analyze_title_only_features(self, df: pd.DataFrame) -> Dict:
        """
        Analisis fitur title-only yang digunakan untuk labeling

        Args:
            df: DataFrame dengan fitur title-only

        Returns:
            Dict dengan analisis fitur title-only
        """
        analysis = {
            'title_features': [],
            'title_indicators': [],
            'title_scores': [],
            'title_classification': []
        }

        available_features = set(df.columns)

        # Kategorisasi fitur title-only
        title_features = [f for f in available_features if f.startswith('title_') and not f.endswith('_indicator')]
        title_indicators = [f for f in available_features if f.startswith('title_') and f.endswith('_indicator')]
        title_scores = [f for f in available_features if 'title_fatigue_score' in f or 'title_complexity_score' in f or 'title_emotional_intensity' in f]
        title_classification = [f for f in available_features if 'title_fatigue_risk' in f]

        analysis['title_features'] = title_features
        analysis['title_indicators'] = title_indicators
        analysis['title_scores'] = title_scores
        analysis['title_classification'] = title_classification

        # Statistik distribusi
        if 'title_fatigue_risk' in df.columns:
            risk_dist = df['title_fatigue_risk'].value_counts()
            analysis['risk_distribution'] = risk_dist.to_dict()
            analysis['risk_percentages'] = (risk_dist / len(df) * 100).round(1).to_dict()

        if 'title_fatigue_score' in df.columns:
            analysis['score_statistics'] = {
                'mean': df['title_fatigue_score'].mean(),
                'std': df['title_fatigue_score'].std(),
                'min': df['title_fatigue_score'].min(),
                'max': df['title_fatigue_score'].max()
            }

        # Analisis prevalensi fitur title
        title_feature_prevalence = {}
        for feature in ['title_stress_count', 'title_workload_count', 'title_negative_emotion_count',
                       'title_recovery_count', 'title_exhaustion_count']:
            if feature in df.columns:
                title_feature_prevalence[feature] = (df[feature] > 0).sum()

        analysis['title_feature_prevalence'] = title_feature_prevalence

        return analysis


def main():
    """Test feature filter untuk title-only classifier"""
    filter_obj = FeatureFilter3()

    print("🧪 Testing Feature Filter 3 untuk title_only_fatigue_classifier.py...")

    # Test 1: Untuk title_only_fatigue_classifier.py output
    print("\n1️⃣ Testing dengan output title_only_fatigue_classifier.py:")
    title_input = "dataset/processed/fatigue_classified_with_title_only.csv"
    title_output = "dataset/processed/safe_ml_title_only_dataset.csv"

    try:
        safe_path = filter_obj.create_safe_dataset_for_title_only_classifier(
            title_input, title_output, target_column='title_fatigue_risk'
        )
        print(f"✅ Safe ML dataset created: {safe_path}")
    except Exception as e:
        print(f"❌ Error with title-only output: {e}")

    # Test 2: Validasi fitur khusus title-only
    print("\n2️⃣ Testing feature validation untuk title-only:")
    test_features = [
        'total_distance_km', 'activity_points',        # Safe
        'title_stress_count', 'title_fatigue_score',   # Dangerous (title-only)
        'title_stress_indicator', 'title_complexity_score',  # Dangerous (indicators)
        'identity', 'combined_titles',                 # Metadata
        'unknown_title_feature'                        # Uncategorized
    ]

    validation = filter_obj.validate_features_for_ml(test_features)
    print(f"🔍 Validation results:")
    print(f"   ✅ Safe: {validation['safe_features']}")
    print(f"   🚨 Dangerous: {validation['dangerous_features']}")
    print(f"   🏷️ Metadata: {validation['metadata_features']}")
    print(f"   ❓ Uncategorized: {validation['uncategorized_features']}")
    print(f"   🛡️ Is safe for ML: {validation['is_safe']}")

    # Test 3: Penjelasan fitur title-only
    print("\n3️⃣ Testing feature explanations:")
    title_features = [
        'title_stress_count',
        'title_fatigue_risk',
        'total_distance_km',
        'combined_titles'
    ]

    for feature in title_features:
        explanation = filter_obj.get_feature_explanation(feature)
        print(f"   {feature}: {explanation}")

    # Test 4: Analisis fitur title-only (jika file ada)
    print("\n4️⃣ Testing title-only features analysis:")
    try:
        import pandas as pd
        df = pd.read_csv(title_input)
        analysis = filter_obj.analyze_title_only_features(df)

        print(f"📊 Title-only analysis:")
        print(f"   📝 Title features: {len(analysis['title_features'])}")
        print(f"   🎯 Title indicators: {analysis['title_indicators']}")
        print(f"   📈 Title scores: {analysis['title_scores']}")
        print(f"   🏷️ Title classification: {analysis['title_classification']}")

        if 'risk_percentages' in analysis:
            print(f"   📊 Risk distribution: {analysis['risk_percentages']}")

        if 'score_statistics' in analysis:
            stats = analysis['score_statistics']
            print(f"   📈 Score stats: mean={stats['mean']:.1f}, std={stats['std']:.1f}")

    except Exception as e:
        print(f"❌ Error in title-only analysis: {e}")


if __name__ == "__main__":
    main()
