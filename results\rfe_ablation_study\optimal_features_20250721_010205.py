# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-21 01:02:05
# Best Algorithm: Random Forest
# Accuracy: 0.9667 ± 0.0183

OPTIMAL_FEATURES = [
    'achievement_rate',
    'activity_days',
    'consistency_score',
    'gamification_balance',
    'pomokit_title_count',
    'pomokit_title_length',
    'pomokit_unique_words',
    'productivity_points',
    'strava_title_count',
    'strava_title_length',
    'strava_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_time_minutes',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.9667

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'strava_title_count': -0.0033,  # -0.34% impact
    'total_cycles': -0.0033,  # -0.34% impact
    'activity_days': -0.0033,  # -0.34% impact
    'title_balance_ratio': -0.0033,  # -0.34% impact
    'strava_title_length': 0.0033,  # 0.34% impact
    'strava_unique_words': 0.0000,  # 0.00% impact
    'pomokit_title_length': 0.0000,  # 0.00% impact
    'achievement_rate': 0.0000,  # 0.00% impact
    'consistency_score': 0.0000,  # 0.00% impact
    'pomokit_title_count': 0.0000,  # 0.00% impact
    'total_time_minutes': 0.0000,  # 0.00% impact
    'work_days': 0.0000,  # 0.00% impact
    'pomokit_unique_words': 0.0000,  # 0.00% impact
    'productivity_points': 0.0000,  # 0.00% impact
    'gamification_balance': 0.0000,  # 0.00% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'strava_title_count': 2.27,  # 2.27%
    'total_cycles': 0.00,  # 0.00%
    'activity_days': 2.27,  # 2.27%
    'title_balance_ratio': 7.27,  # 7.27%
    'strava_title_length': 13.18,  # 13.18%
    'strava_unique_words': 6.82,  # 6.82%
    'pomokit_title_length': 15.45,  # 15.45%
    'achievement_rate': 4.09,  # 4.09%
    'consistency_score': 29.09,  # 29.09%
    'pomokit_title_count': 0.00,  # 0.00%
    'total_time_minutes': 5.00,  # 5.00%
    'work_days': 0.00,  # 0.00%
    'pomokit_unique_words': 9.55,  # 9.55%
    'productivity_points': 0.00,  # 0.00%
    'gamification_balance': 5.00,  # 5.00%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'strava_title_count',  # -0.34% impact when removed
    'total_cycles',  # -0.34% impact when removed
    'activity_days',  # -0.34% impact when removed
    'title_balance_ratio',  # -0.34% impact when removed
    'strava_title_length',  # 0.34% impact when removed
    'strava_unique_words',  # 0.00% impact when removed
    'pomokit_title_length',  # 0.00% impact when removed
    'achievement_rate',  # 0.00% impact when removed
    'consistency_score',  # 0.00% impact when removed
    'pomokit_title_count',  # 0.00% impact when removed
    'total_time_minutes',  # 0.00% impact when removed
    'work_days',  # 0.00% impact when removed
    'pomokit_unique_words',  # 0.00% impact when removed
    'productivity_points',  # 0.00% impact when removed
    'gamification_balance',  # 0.00% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'strava_title_count',  # 2.27% permutation importance
    'total_cycles',  # 0.00% permutation importance
    'activity_days',  # 2.27% permutation importance
    'title_balance_ratio',  # 7.27% permutation importance
    'strava_title_length',  # 13.18% permutation importance
    'strava_unique_words',  # 6.82% permutation importance
    'pomokit_title_length',  # 15.45% permutation importance
    'achievement_rate',  # 4.09% permutation importance
    'consistency_score',  # 29.09% permutation importance
    'pomokit_title_count',  # 0.00% permutation importance
    'total_time_minutes',  # 5.00% permutation importance
    'work_days',  # 0.00% permutation importance
    'pomokit_unique_words',  # 9.55% permutation importance
    'productivity_points',  # 0.00% permutation importance
    'gamification_balance',  # 5.00% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'strava_title_count': 'MINIMAL - Negligible performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'activity_days': 'MINIMAL - Negligible performance impact',
    'title_balance_ratio': 'MINIMAL - Negligible performance impact',
    'strava_title_length': 'MINIMAL - Negligible performance impact',
    'strava_unique_words': 'MINIMAL - Negligible performance impact',
    'pomokit_title_length': 'MINIMAL - Negligible performance impact',
    'achievement_rate': 'MINIMAL - Negligible performance impact',
    'consistency_score': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
    'total_time_minutes': 'MINIMAL - Negligible performance impact',
    'work_days': 'MINIMAL - Negligible performance impact',
    'pomokit_unique_words': 'MINIMAL - Negligible performance impact',
    'productivity_points': 'MINIMAL - Negligible performance impact',
    'gamification_balance': 'MINIMAL - Negligible performance impact',
}
