#!/usr/bin/env python3
"""
Generate Actual Methodology Flowchart for Cardiovascular Activity Research
Based on the real implementation and pipeline structure
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

def create_methodology_flowchart():
    """Create comprehensive methodology flowchart based on actual project implementation"""
    
    # Create figure with larger size for detailed flowchart
    fig, ax = plt.subplots(1, 1, figsize=(16, 20))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 24)
    ax.axis('off')
    
    # Define colors for different phases
    colors = {
        'data_collection': '#E3F2FD',      # Light Blue
        'preprocessing': '#F3E5F5',        # Light Purple  
        'labeling': '#FFF3E0',             # Light Orange
        'pipeline': '#E8F5E8',             # Light Green
        'validation': '#FFEBEE',           # Light Red
        'results': '#F1F8E9'               # Light Lime
    }
    
    # Helper function to create rounded rectangle
    def create_box(x, y, width, height, text, color, fontsize=9):
        box = FancyBboxPatch(
            (x, y), width, height,
            boxstyle="round,pad=0.1",
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        ax.add_patch(box)
        ax.text(x + width/2, y + height/2, text, 
                ha='center', va='center', fontsize=fontsize, 
                weight='bold', wrap=True)
    
    # Helper function to create diamond (decision)
    def create_diamond(x, y, width, height, text, color, fontsize=8):
        diamond = mpatches.RegularPolygon(
            (x + width/2, y + height/2), 4, 
            radius=width/2, orientation=np.pi/4,
            facecolor=color, edgecolor='black', linewidth=1.5
        )
        ax.add_patch(diamond)
        ax.text(x + width/2, y + height/2, text, 
                ha='center', va='center', fontsize=fontsize, 
                weight='bold', wrap=True)
    
    # Helper function to create arrow
    def create_arrow(x1, y1, x2, y2, text=''):
        arrow = ConnectionPatch((x1, y1), (x2, y2), "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5,
                              mutation_scale=20, fc="black")
        ax.add_patch(arrow)
        if text:
            mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
            ax.text(mid_x + 0.2, mid_y, text, fontsize=7, ha='left')
    
    # Title
    ax.text(5, 23, 'ALUR METODOLOGI PENELITIAN\nKLASIFIKASI FATIGUE BERBASIS AKTIVITAS KARDIOVASKULAR',
            ha='center', va='center', fontsize=14, weight='bold')

    # Tahapan 1: Data Collection
    create_box(1, 21.5, 3.5, 1, '1. DATA COLLECTION\n• Data Strava (Aktivitas Kardiovaskular)\n• Data Pomokit (Produktivitas Akademik)\n• Ethical clearance & informed consent',
               colors['data_collection'], 8)

    # Tahapan 2: Data Preprocessing
    create_box(5.5, 21.5, 3, 1, '2. DATA PREPROCESSING\n• Data cleaning & validation\n• Data integration & harmonization\n• Format standardization',
               colors['preprocessing'], 8)

    create_arrow(4.5, 22, 5.5, 22)

    # Tahapan 3: Feature Engineering
    create_box(1, 19.5, 2.5, 1, '3. FEATURE ENGINEERING\n• Weekly aggregation\n• Text-based features\n• Gamification variables',
               colors['preprocessing'], 8)

    create_arrow(2.75, 21.5, 2.25, 20.5)
    create_arrow(6.75, 21.5, 2.25, 20.5)

    # Tahapan 4: Labeling Strategy
    create_box(4.5, 19.5, 3, 1, '4. LABELING STRATEGY\n• Independent external labeling\n• Multiple labeling approaches\n• Target recreation testing',
               colors['labeling'], 8)

    create_arrow(3.5, 20, 4.5, 20)

    # Tahapan 5: Feature Selection
    create_box(8, 19.5, 2.5, 1, '5. FEATURE SELECTION\n• RFE analysis\n• SHAP-based importance\n• Data leakage prevention',
               colors['validation'], 8)

    create_arrow(7.5, 20, 8, 20)
    
    # Tahapan 6: Training Model
    create_box(1, 17.5, 2.5, 1, '6. TRAINING MODEL\n• Multiple algorithms\n• Pipeline implementation\n• Hyperparameter optimization',
               colors['pipeline'], 8)

    create_arrow(2.25, 19.5, 2.25, 18.5)
    create_arrow(6, 19.5, 2.25, 18.5)
    create_arrow(9.25, 19.5, 2.25, 18.5)

    # Tahapan 7: Evaluation Model
    create_box(4.5, 17.5, 3, 1, '7. EVALUATION MODEL\n• Comprehensive metrics\n• Cross-validation strategy\n• Model comparison',
               colors['validation'], 8)

    create_arrow(3.5, 18, 4.5, 18)

    # Tahapan 8: Results & Analysis
    create_box(8, 17.5, 2.5, 1, '8. RESULTS & ANALYSIS\n• Feature importance\n• Business actionability\n• Production deployment',
               colors['results'], 8)

    create_arrow(7.5, 18, 8, 18)
    
    # Output Results Section
    create_box(3.5, 15.5, 3, 1, 'OUTPUT RESULTS\n• Optimal feature set\n• Feature importance rankings\n• Business recommendations',
               colors['results'], 8)

    create_arrow(2.25, 17.5, 4.5, 16.5)
    create_arrow(6, 17.5, 5.5, 16.5)
    create_arrow(9.25, 17.5, 6, 16.5)

    # Key Findings Section
    create_box(1, 13.5, 8.5, 1.5, 'KEY FINDINGS & CONTRIBUTIONS\n• Data leakage prevention methodology\n• SHAP analysis superiority (68.33% accuracy)\n• Physical activity dominance (23.84% SHAP importance)\n• Production-ready pipeline (main7.py)',
               colors['results'], 8)

    create_arrow(5, 15.5, 5.25, 15)
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color=colors['data_collection'], label='1. Data Collection'),
        mpatches.Patch(color=colors['preprocessing'], label='2-3. Preprocessing & Feature Engineering'),
        mpatches.Patch(color=colors['labeling'], label='4. Labeling Strategy'),
        mpatches.Patch(color=colors['validation'], label='5. Feature Selection'),
        mpatches.Patch(color=colors['pipeline'], label='6. Training Model'),
        mpatches.Patch(color=colors['validation'], label='7. Evaluation Model'),
        mpatches.Patch(color=colors['results'], label='8. Results & Analysis')
    ]

    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98), fontsize=8)
    
    plt.tight_layout()
    return fig

def main():
    """Generate and save the methodology flowchart"""
    print("Generating actual methodology flowchart...")
    
    # Create the flowchart
    fig = create_methodology_flowchart()
    
    # Save the figure
    output_path = 'laporan-akhir/figures/gambar_3_1_alur_metodologi_penelitian.png'
    fig.savefig(output_path, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print(f"Flowchart saved to: {output_path}")

    # Also save to current directory for easy access
    fig.savefig('alur_metodologi_penelitian.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("Flowchart also saved to: alur_metodologi_penelitian.png")
    
    # Show the plot
    plt.show()

if __name__ == "__main__":
    main()
