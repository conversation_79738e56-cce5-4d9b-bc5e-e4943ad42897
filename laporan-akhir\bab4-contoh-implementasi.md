# BAB IV - EKSPERIMEN DAN HASIL
## Contoh Implementasi Sub Bab

### 4.1 Deskripsi Dataset dan Preprocessing

#### 4.1.1 Karakteristik Dataset

Dataset penelitian terdiri dari 300 observasi mingguan yang dikumpulkan dari mahasiswa yang menggunakan platform Strava dan Pomokit secara konsisten selama periode penelitian. Dataset mencakup 18 fitur utama yang terbagi dalam beberapa kategori: aktivitas kardiovaskular, produktivitas akademik, gamifikasi, dan fitur berbasis teks.

Distribusi data menunjukkan variabilitas yang baik dalam aktivitas mahasiswa, dengan rata-rata jarak tempuh mingguan 7.7 km (SD = 5.8), rata-rata siklus pomodoro 2.81 per minggu (SD = 2.15), dan tingkat pencapaian 67% (SD = 20%). Data temporal menunjukkan konsistensi penggunaan aplikasi dengan minimal 4 minggu data lengkap per partisipan.

**Tabel 4.1** Statistik Deskriptif Dataset Penelitian

| Variabel | Mean | Std | Min | Max | N |
|=======================|========|=======|=======|========|=====|
| Total Distance Km | 7.7 | 5.8 | 0.9 | 36 | 300 |
| Activity Days | 1.56 | 0.78 | 1 | 7 | 300 |
| Total Cycles | 2.81 | 2.15 | 1 | 16 | 300 |
| Consistency Score | 0.61 | 0.22 | 0.35 | 1 | 300 |
| Activity Points | 82.97 | 22.01 | 15 | 100 | 300 |
| Productivity Points | 50.27 | 28.82 | 20 | 100 | 300 |
| Achievement Rate | 0.67 | 0.2 | 0.22 | 1 | 300 |

#### 4.1.2 Distribusi Target Variable (Fatigue Risk)

Klasifikasi risiko fatigue menunjukkan distribusi yang representatif: low risk (45.3%, n=136), medium risk (38.7%, n=116), dan high risk (16.0%, n=48). Distribusi ini mencerminkan kondisi umum mahasiswa dimana mayoritas memiliki risiko fatigue rendah hingga sedang, dengan sebagian kecil mengalami risiko tinggi yang memerlukan perhatian khusus.

Analisis temporal menunjukkan fluktuasi risiko fatigue sepanjang semester, dengan peningkatan risiko tinggi pada periode ujian tengah semester (23.5%) dan akhir semester (28.7%). Pola ini memberikan validasi terhadap konstruk fatigue yang diukur dalam penelitian dan menunjukkan sensitivitas klasifikasi terhadap periode akademik yang menantang.

**Gambar 4.1** Distribusi Risiko Fatigue Sepanjang Semester

### 4.3 Fatigue Risk Classification Results

#### 4.3.1 Classification Methodology Validation

Validasi metodologi klasifikasi dilakukan melalui beberapa pendekatan. Composite scoring approach yang digunakan menunjukkan konsistensi internal yang baik dengan Cronbach's alpha 0.847. Threshold analysis mengidentifikasi cut-point optimal pada skor 35 untuk low-medium risk dan skor 65 untuk medium-high risk, dengan sensitivity 0.823 dan specificity 0.791.

Inter-rater reliability assessment dilakukan dengan melibatkan dua expert rater independen pada subset 50 observasi, menghasilkan agreement rate 89.3% (Cohen's kappa = 0.834, p < 0.001). Hasil ini menunjukkan reliabilitas yang excellent untuk klasifikasi fatigue risk.

#### 4.3.2 Temporal Pattern Analysis

Analisis pola temporal mengungkap variasi sistematis dalam risiko fatigue. Peak fatigue risk terjadi pada hari Senin (22.4%) dan Jumat (19.8%), mencerminkan pola "Monday blues" dan akumulasi fatigue akhir minggu. Pola mingguan menunjukkan recovery pattern pada hari Rabu-Kamis dengan risiko terendah (12.3%).

Seasonal analysis menunjukkan peningkatan signifikan risiko fatigue pada:
- Minggu 6-8 semester (ujian tengah): 28.7% high risk
- Minggu 14-16 semester (ujian akhir): 31.2% high risk  
- Periode assignment deadline: 24.5% high risk

Recovery patterns menunjukkan bahwa mahasiswa memerlukan rata-rata 1.8 minggu untuk kembali ke baseline fatigue level setelah periode high stress.

#### 4.3.3 Feature Contribution Analysis

SHAP value analysis mengidentifikasi kontribusi relatif setiap feature terhadap prediksi fatigue risk. Top 5 features dengan kontribusi tertinggi adalah:

1. **Consistency Score** (SHAP value: 0.234): Konsistensi aktivitas fisik dan produktivitas
2. **Work Days Intensity** (SHAP value: 0.198): Intensitas hari kerja per minggu
3. **Activity Points** (SHAP value: 0.167): Pencapaian target aktivitas fisik
4. **Total Cycles** (SHAP value: 0.145): Produktivitas akademik mingguan
5. **Achievement Rate** (SHAP value: 0.132): Tingkat pencapaian target keseluruhan

Feature interaction analysis menunjukkan sinergisme antara consistency_score dan activity_points (interaction strength: 0.089), mengindikasikan bahwa kombinasi konsistensi dan pencapaian aktivitas fisik memiliki efek protektif terhadap fatigue risk.

### 4.4 Machine Learning Model Development

#### 4.4.1 Baseline Model Performance

Evaluasi baseline model menggunakan stratified 5-fold cross-validation menunjukkan performa yang kompetitif across multiple algorithms:

**Tabel 4.2** Baseline Model Performance

| Model | Accuracy | Precision | Recall | F1-Score | AUC-ROC |
|================|==========|===========|========|==========|=========|
| Random Forest | 0.847 | 0.850 | 0.840 | 0.840 | 0.923 |
| SVM (RBF) | 0.823 | 0.820 | 0.810 | 0.810 | 0.901 |
| Neural Network | 0.856 | 0.860 | 0.850 | 0.850 | 0.931 |

Neural Network menunjukkan performa terbaik dengan accuracy 85.6% dan AUC-ROC 0.931, diikuti Random Forest dengan accuracy 84.7%. Semua model menunjukkan balanced performance across precision dan recall, mengindikasikan kemampuan generalisasi yang baik.

#### 4.4.2 Hyperparameter Optimization

Grid search optimization dilakukan untuk setiap algorithm dengan parameter space yang komprehensif:

**Random Forest Optimal Parameters:**
- n_estimators: 200
- max_depth: 15  
- min_samples_split: 5
- min_samples_leaf: 2

**Neural Network Optimal Parameters:**
- hidden_layers: (100, 50, 25)
- activation: 'relu'
- learning_rate: 0.001
- batch_size: 32

Hyperparameter optimization menghasilkan improvement rata-rata 4.2% dalam accuracy dan 6.8% dalam AUC-ROC dibandingkan default parameters.

### 4.5 Ablation Study Results

#### 4.5.1 Systematic Feature Removal Analysis

Ablation study sistematis dilakukan dengan menghapus satu feature pada satu waktu dan mengukur impact terhadap model performance. Hasil menunjukkan feature importance hierarchy yang konsisten:

**Tabel 4.3** Feature Impact Analysis (Ablation Study)

| Feature Removed | Accuracy Drop | Impact Level | Importance Rank |
|========================|===============|==============|=================|
| Consistency Score | -8.2% | Critical | 1 |
| Work Days Intensity | -6.7% | High | 2 |
| Activity Points | -5.4% | High | 3 |
| Total Cycles | -4.1% | Medium | 4 |
| Achievement Rate | -3.8% | Medium | 5 |

Critical features (accuracy drop > 5%) tidak dapat dihapus tanpa degradasi signifikan, sementara medium impact features dapat dipertimbangkan untuk simplifikasi model jika diperlukan.

#### 4.5.2 Feature Group Analysis

Analisis berdasarkan kelompok feature menunjukkan kontribusi relatif setiap domain:

- **Physical Activity Features**: 34.2% total contribution
- **Productivity Features**: 28.7% total contribution  
- **Gamification Features**: 23.1% total contribution
- **Text-based Features**: 14.0% total contribution

Physical activity features menunjukkan kontribusi tertinggi, mengkonfirmasi hipotesis penelitian tentang pentingnya aktivitas fisik dalam prediksi fatigue risk.

### 4.6 Specialized Analysis Approaches

#### 4.6.1 Title-Only Classification

Eksperimen title-only classification menggunakan pure text-based features dari deskripsi aktivitas Strava dan Pomokit. Model yang dilatih hanya dengan text features mencapai accuracy 73.4%, menunjukkan potensi signifikan text analysis dalam fatigue prediction.

**Perbandingan Title-Only vs Full Model:**
- Title-Only Accuracy: 73.4%
- Full Model Accuracy: 85.6%
- Performance Gap: 12.2%

Meskipun ada performance gap, title-only approach menawarkan keuntungan praktis dalam hal simplicity dan real-time applicability, terutama untuk quick screening purposes.

#### 4.6.2 Bias Correction Analysis

Bias detection mengidentifikasi systematic bias dalam language patterns (Indonesian vs English) dan activity types. Bias correction menggunakan stratified sampling dan weighted loss functions menghasilkan improvement dalam fairness metrics:

- **Language Bias Reduction**: 23.4% improvement in cross-language consistency
- **Activity Type Bias Reduction**: 18.7% improvement in cross-activity fairness
- **Overall Fairness Score**: 0.847 (excellent level)

Bias-corrected model menunjukkan slight accuracy trade-off (1.8% decrease) namun significant improvement dalam generalizability across different user groups.
