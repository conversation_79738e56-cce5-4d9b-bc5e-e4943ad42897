# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-19 00:23:30
# Best Algorithm: Random Forest
# Accuracy: 0.7067 ± 0.0646

OPTIMAL_FEATURES = [
    'achievement_rate',
    'pomokit_title_count',
    'pomokit_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_distance_km',
    'total_time_minutes',
    'total_title_diversity',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.6933

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'pomokit_unique_words': -0.0233,  # -3.37% impact
    'title_balance_ratio': 0.0200,  # 2.88% impact
    'total_title_diversity': -0.0167,  # -2.40% impact
    'achievement_rate': -0.0167,  # -2.40% impact
    'total_distance_km': -0.0167,  # -2.40% impact
    'pomokit_title_count': -0.0133,  # -1.92% impact
    'total_cycles': -0.0133,  # -1.92% impact
    'total_time_minutes': 0.0100,  # 1.44% impact
    'work_days': -0.0100,  # -1.44% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'pomokit_unique_words': 14.29,  # 14.29%
    'title_balance_ratio': 23.13,  # 23.13%
    'total_title_diversity': 20.59,  # 20.59%
    'achievement_rate': 12.72,  # 12.72%
    'total_distance_km': 11.91,  # 11.91%
    'pomokit_title_count': 3.30,  # 3.30%
    'total_cycles': 2.43,  # 2.43%
    'total_time_minutes': 9.77,  # 9.77%
    'work_days': 1.85,  # 1.85%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'pomokit_unique_words',  # -3.37% impact when removed
    'title_balance_ratio',  # 2.88% impact when removed
    'total_title_diversity',  # -2.40% impact when removed
    'achievement_rate',  # -2.40% impact when removed
    'total_distance_km',  # -2.40% impact when removed
    'pomokit_title_count',  # -1.92% impact when removed
    'total_cycles',  # -1.92% impact when removed
    'total_time_minutes',  # 1.44% impact when removed
    'work_days',  # -1.44% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'pomokit_unique_words',  # 14.29% permutation importance
    'title_balance_ratio',  # 23.13% permutation importance
    'total_title_diversity',  # 20.59% permutation importance
    'achievement_rate',  # 12.72% permutation importance
    'total_distance_km',  # 11.91% permutation importance
    'pomokit_title_count',  # 3.30% permutation importance
    'total_cycles',  # 2.43% permutation importance
    'total_time_minutes',  # 9.77% permutation importance
    'work_days',  # 1.85% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'pomokit_unique_words': 'MINIMAL - Negligible performance impact',
    'title_balance_ratio': 'LOW - Minor performance impact',
    'total_title_diversity': 'MINIMAL - Negligible performance impact',
    'achievement_rate': 'MINIMAL - Negligible performance impact',
    'total_distance_km': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'total_time_minutes': 'LOW - Minor performance impact',
    'work_days': 'MINIMAL - Negligible performance impact',
}
