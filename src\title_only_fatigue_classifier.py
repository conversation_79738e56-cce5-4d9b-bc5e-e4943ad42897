"""
Title-Only Fatigue Risk Classification System
Pure title-based classification for ablation study research design
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TitleOnlyFatigueClassifier:
    """
    Pure title-based fatigue risk classifier for ablation study research
    """
    
    def __init__(self):
        """Initialize the title-only fatigue risk classifier"""
        self.stress_keywords = self._define_stress_keywords()
        self.workload_keywords = self._define_workload_keywords()
        self.negative_emotion_keywords = self._define_negative_emotion_keywords()
        self.recovery_keywords = self._define_recovery_keywords()
        self.time_pressure_keywords = self._define_time_pressure_keywords()
        self.exhaustion_keywords = self._define_exhaustion_keywords()
        
    def _define_stress_keywords(self) -> List[str]:
        """Define stress-related keywords"""
        return [
            'stress', 'stressed', 'stres', 'tertekan', 'pressure', 'tekanan',
            'overwhelm', 'overwhelmed', 'kewalahan', 'anxiety', 'anxious', 
            'cemas', 'khawatir', 'panic', 'panik', 'worried', 'worry'
        ]
    
    def _define_workload_keywords(self) -> List[str]:
        """Define high workload keywords"""
        return [
            'tugas', 'assignment', 'kerja', 'work', 'job', 'pekerjaan',
            'project', 'proyek', 'meeting', 'rapat', 'presentasi', 'presentation',
            'laporan', 'report', 'skripsi', 'thesis', 'ujian', 'exam', 'test',
            'quiz', 'homework', 'pr', 'study', 'belajar', 'research', 'riset',
            'overtime', 'lembur', 'extra', 'tambahan', 'additional', 'more',
            'multiple', 'banyak', 'several', 'beberapa', 'various', 'macam'
        ]
    
    def _define_negative_emotion_keywords(self) -> List[str]:
        """Define negative emotion keywords"""
        return [
            'frustrated', 'frustasi', 'angry', 'marah', 'upset', 'kesal',
            'annoyed', 'irritated', 'jengkel', 'sad', 'sedih', 'down',
            'depressed', 'depresi', 'disappointed', 'kecewa', 'hopeless',
            'putus asa', 'bad', 'buruk', 'terrible', 'awful', 'horrible',
            'failed', 'gagal', 'wrong', 'salah', 'mistake', 'kesalahan',
            'lonely', 'kesepian', 'isolated', 'alone', 'sendiri', 'empty',
            'kosong', 'meaningless', 'tidak bermakna', 'bored', 'bosan'
        ]
    
    def _define_recovery_keywords(self) -> List[str]:
        """Define recovery and positive coping keywords"""
        return [
            'rest', 'istirahat', 'relax', 'santai', 'rileks', 'break', 'rehat',
            'vacation', 'liburan', 'holiday', 'cuti', 'fun', 'enjoy', 'senang',
            'happy', 'bahagia', 'gembira', 'good', 'bagus', 'great', 'hebat',
            'excellent', 'amazing', 'wonderful', 'meditation', 'meditasi',
            'mindful', 'peaceful', 'damai', 'tenang', 'calm', 'refresh',
            'recharge', 'recovery', 'pemulihan', 'friends', 'teman', 'family',
            'keluarga', 'together', 'bersama', 'support', 'dukungan'
        ]
    
    def _define_time_pressure_keywords(self) -> List[str]:
        """Define time pressure and urgency keywords"""
        return [
            'urgent', 'mendesak', 'rush', 'terburu', 'hurry', 'buru-buru',
            'quick', 'cepat', 'fast', 'immediately', 'segera', 'deadline',
            'late', 'terlambat', 'behind', 'tertinggal', 'time', 'waktu',
            'schedule', 'jadwal', 'again', 'lagi', 'repeat', 'ulang',
            'redo', 'mengulang', 'continue', 'lanjut', 'ongoing', 'berlanjut'
        ]
    
    def _define_exhaustion_keywords(self) -> List[str]:
        """Define physical and mental exhaustion keywords"""
        return [
            'tired', 'capek', 'lelah', 'exhausted', 'kelelahan', 'fatigue',
            'fatigue', 'drained', 'terkuras', 'sleepy', 'mengantuk',
            'insomnia', 'susah tidur', 'restless', 'gelisah', 'weak', 'lemah',
            'sick', 'sakit', 'headache', 'sakit kepala', 'dizzy', 'pusing'
        ]
    
    def extract_title_features(self, combined_title: str) -> Dict[str, int]:
        """
        Extract comprehensive features from title text only
        
        Args:
            combined_title: Combined title string from activities
            
        Returns:
            Dictionary with title-based features
        """
        if pd.isna(combined_title) or combined_title == '':
            return {
                'stress_count': 0, 'workload_count': 0, 'negative_emotion_count': 0,
                'recovery_count': 0, 'time_pressure_count': 0, 'exhaustion_count': 0,
                'total_words': 0, 'unique_words': 0, 'title_length': 0,
                'activity_count': 0, 'avg_word_length': 0, 'exclamation_count': 0,
                'question_count': 0, 'caps_ratio': 0, 'number_count': 0
            }
        
        title_lower = str(combined_title).lower()
        words = title_lower.split()
        
        # Keyword counts
        stress_count = sum(1 for keyword in self.stress_keywords if keyword in title_lower)
        workload_count = sum(1 for keyword in self.workload_keywords if keyword in title_lower)
        negative_count = sum(1 for keyword in self.negative_emotion_keywords if keyword in title_lower)
        recovery_count = sum(1 for keyword in self.recovery_keywords if keyword in title_lower)
        time_pressure_count = sum(1 for keyword in self.time_pressure_keywords if keyword in title_lower)
        exhaustion_count = sum(1 for keyword in self.exhaustion_keywords if keyword in title_lower)
        
        # Linguistic features
        total_words = len(words)
        unique_words = len(set(words))
        title_length = len(combined_title)
        activity_count = len(combined_title.split(' | '))
        avg_word_length = np.mean([len(word) for word in words]) if words else 0
        
        # Emotional indicators
        exclamation_count = combined_title.count('!')
        question_count = combined_title.count('?')
        caps_ratio = sum(1 for c in combined_title if c.isupper()) / len(combined_title) if combined_title else 0
        number_count = sum(1 for c in combined_title if c.isdigit())
        
        return {
            'stress_count': stress_count,
            'workload_count': workload_count,
            'negative_emotion_count': negative_count,
            'recovery_count': recovery_count,
            'time_pressure_count': time_pressure_count,
            'exhaustion_count': exhaustion_count,
            'total_words': total_words,
            'unique_words': unique_words,
            'title_length': title_length,
            'activity_count': activity_count,
            'avg_word_length': avg_word_length,
            'exclamation_count': exclamation_count,
            'question_count': question_count,
            'caps_ratio': caps_ratio,
            'number_count': number_count
        }
    
    def calculate_title_based_fatigue_score(self, features: Dict[str, int]) -> float:
        """
        Calculate fatigue risk score based ONLY on title features
        
        Args:
            features: Dictionary of title features
            
        Returns:
            Fatigue risk score (0-100)
        """
        # Pure title-based scoring (no behavioral indicators)
        score = (
            # Negative indicators (higher = more risk)
            features['stress_count'] * 15 +
            features['workload_count'] * 8 +
            features['negative_emotion_count'] * 12 +
            features['time_pressure_count'] * 10 +
            features['exhaustion_count'] * 18 +
            
            # Workload intensity indicators
            (features['activity_count'] - 1) * 3 +  # Multiple activities
            (features['total_words'] / 10) * 2 +    # Verbose descriptions
            features['exclamation_count'] * 5 +     # Emotional intensity
            features['caps_ratio'] * 20 +           # Caps = stress/urgency
            
            # Recovery factors (negative contribution)
            - features['recovery_count'] * 12 -
            - (features['unique_words'] / features['total_words'] * 10) if features['total_words'] > 0 else 0
        )
        
        # Normalize to 0-100 scale
        return max(0, min(100, score))
    
    def classify_fatigue_risk_title_only(self, score: float, features: Dict[str, int]) -> str:
        """
        Classify fatigue risk based ONLY on title analysis
        
        Args:
            score: Title-based fatigue score
            features: Title features dictionary
            
        Returns:
            Risk classification: 'low_risk', 'medium_risk', 'high_risk'
        """
        # High risk criteria (title-only)
        high_risk_conditions = [
            score >= 40,
            features['stress_count'] >= 2,
            features['exhaustion_count'] >= 2,
            features['negative_emotion_count'] >= 2,
            (features['workload_count'] >= 3) and (features['recovery_count'] == 0),
            features['time_pressure_count'] >= 3
        ]
        
        if any(high_risk_conditions):
            return 'high_risk'
        
        # Medium risk criteria (title-only)
        medium_risk_conditions = [
            score >= 20,
            features['stress_count'] >= 1,
            features['exhaustion_count'] >= 1,
            features['negative_emotion_count'] >= 1,
            features['workload_count'] >= 2,
            features['time_pressure_count'] >= 2,
            (features['workload_count'] >= 1) and (features['recovery_count'] == 0)
        ]
        
        if any(medium_risk_conditions):
            return 'medium_risk'
        
        # Low risk (default)
        return 'low_risk'
    
    def process_title_only_classification(self, data_path: str) -> pd.DataFrame:
        """
        Complete title-only fatigue risk classification pipeline
        
        Args:
            data_path: Path to the processed dataset
            
        Returns:
            DataFrame with title-only fatigue risk classification
        """
        logger.info("Starting title-only fatigue risk classification...")
        
        # Load data
        df = pd.read_csv(data_path)
        logger.info(f"Loaded dataset with {len(df)} observations")
        
        # Extract title features
        logger.info("Extracting title features...")
        title_features_list = df['combined_titles'].apply(self.extract_title_features)
        
        # Convert to separate columns
        feature_names = ['stress_count', 'workload_count', 'negative_emotion_count',
                        'recovery_count', 'time_pressure_count', 'exhaustion_count',
                        'total_words', 'unique_words', 'title_length', 'activity_count',
                        'avg_word_length', 'exclamation_count', 'question_count',
                        'caps_ratio', 'number_count']
        
        for feature in feature_names:
            df[f'title_{feature}'] = [features[feature] for features in title_features_list]
        
        # Calculate title-based fatigue score
        logger.info("Calculating title-based fatigue scores...")
        df['title_fatigue_score'] = [
            self.calculate_title_based_fatigue_score(features) 
            for features in title_features_list
        ]
        
        # Classify based on title only
        logger.info("Classifying fatigue risk (title-only)...")
        df['title_fatigue_risk'] = [
            self.classify_fatigue_risk_title_only(score, features)
            for score, features in zip(df['title_fatigue_score'], title_features_list)
        ]
        
        # Add title-based indicators for ablation study
        df['title_stress_indicator'] = (df['title_stress_count'] > 0).astype(int)
        df['title_workload_indicator'] = (df['title_workload_count'] > 0).astype(int)
        df['title_negative_indicator'] = (df['title_negative_emotion_count'] > 0).astype(int)
        df['title_recovery_indicator'] = (df['title_recovery_count'] > 0).astype(int)
        df['title_exhaustion_indicator'] = (df['title_exhaustion_count'] > 0).astype(int)
        
        # Title complexity indicators
        df['title_complexity_score'] = (
            df['title_unique_words'] / df['title_total_words'].replace(0, 1) * 
            df['title_avg_word_length']
        )
        
        df['title_emotional_intensity'] = (
            df['title_exclamation_count'] + df['title_question_count'] + 
            df['title_caps_ratio'] * 10
        )
        
        logger.info("Title-only classification completed successfully")
        
        return df
    
    def generate_title_classification_report(self, data: pd.DataFrame) -> Dict:
        """
        Generate comprehensive report for title-only classification
        
        Args:
            data: DataFrame with title-only classification
            
        Returns:
            Dictionary with classification statistics
        """
        report = {
            'total_observations': len(data),
            'title_fatigue_distribution': data['title_fatigue_risk'].value_counts().to_dict(),
            'title_fatigue_percentages': (data['title_fatigue_risk'].value_counts(normalize=True) * 100).round(1).to_dict(),
            'average_title_score': data['title_fatigue_score'].mean(),
            'title_score_std': data['title_fatigue_score'].std(),
            'title_feature_prevalence': {
                'stress_present': (data['title_stress_count'] > 0).sum(),
                'workload_present': (data['title_workload_count'] > 0).sum(),
                'negative_emotion_present': (data['title_negative_emotion_count'] > 0).sum(),
                'recovery_present': (data['title_recovery_count'] > 0).sum(),
                'exhaustion_present': (data['title_exhaustion_count'] > 0).sum()
            },
            'title_linguistic_stats': {
                'avg_words_per_title': data['title_total_words'].mean(),
                'avg_activities_per_week': data['title_activity_count'].mean(),
                'avg_title_length': data['title_title_length'].mean(),
                'emotional_intensity_avg': data['title_emotional_intensity'].mean()
            }
        }
        
        return report


def main():
    """Main function for title-only classification"""
    # Initialize classifier
    classifier = TitleOnlyFatigueClassifier()
    
    # Process classification
    data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Dataset not found: {data_path}")
        print("Please run the main pipeline first to generate the processed dataset.")
        return
    
    # Run title-only classification
    classified_data = classifier.process_title_only_classification(data_path)
    
    # Generate report
    report = classifier.generate_title_classification_report(classified_data)
    
    # Save results
    output_path = "dataset/processed/fatigue_classified_with_title_only.csv"
    classified_data.to_csv(output_path, index=False)
    
    print("🎉 Title-Only Fatigue Classification Completed!")
    print(f"📊 Results saved to: {output_path}")
    print(f"📈 Dataset shape: {classified_data.shape}")
    print(f"🔍 Title-only distribution: {report['title_fatigue_percentages']}")
    print(f"📝 Average title score: {report['average_title_score']:.1f}")
    
    return classified_data, report


if __name__ == "__main__":
    main()
