# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-19 00:26:11
# Best Algorithm: Random Forest
# Accuracy: 0.6900 ± 0.0750

OPTIMAL_FEATURES = [
    'achievement_rate',
    'activity_days',
    'activity_points',
    'avg_distance_km',
    'avg_time_minutes',
    'consistency_score',
    'gamification_balance',
    'pomokit_title_count',
    'pomokit_unique_words',
    'productivity_points',
    'strava_title_count',
    'strava_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_distance_km',
    'total_time_minutes',
    'total_title_diversity',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.6900

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'avg_distance_km': -0.0367,  # -5.31% impact
    'productivity_points': 0.0200,  # 2.90% impact
    'total_cycles': -0.0200,  # -2.90% impact
    'pomokit_unique_words': -0.0200,  # -2.90% impact
    'title_balance_ratio': 0.0167,  # 2.42% impact
    'total_distance_km': -0.0167,  # -2.42% impact
    'work_days': -0.0133,  # -1.93% impact
    'total_time_minutes': 0.0100,  # 1.45% impact
    'gamification_balance': -0.0100,  # -1.45% impact
    'consistency_score': -0.0100,  # -1.45% impact
    'strava_unique_words': -0.0100,  # -1.45% impact
    'pomokit_title_count': -0.0067,  # -0.97% impact
    'achievement_rate': -0.0067,  # -0.97% impact
    'avg_time_minutes': -0.0067,  # -0.97% impact
    'strava_title_count': 0.0033,  # 0.48% impact
    'activity_points': 0.0033,  # 0.48% impact
    'activity_days': 0.0000,  # 0.00% impact
    'total_title_diversity': 0.0000,  # 0.00% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'avg_distance_km': 5.33,  # 5.33%
    'productivity_points': 1.48,  # 1.48%
    'total_cycles': 0.00,  # 0.00%
    'pomokit_unique_words': 21.33,  # 21.33%
    'title_balance_ratio': 13.33,  # 13.33%
    'total_distance_km': 1.04,  # 1.04%
    'work_days': 3.70,  # 3.70%
    'total_time_minutes': 5.78,  # 5.78%
    'gamification_balance': 2.96,  # 2.96%
    'consistency_score': 1.48,  # 1.48%
    'strava_unique_words': 2.67,  # 2.67%
    'pomokit_title_count': 2.96,  # 2.96%
    'achievement_rate': 6.96,  # 6.96%
    'avg_time_minutes': 6.81,  # 6.81%
    'strava_title_count': 0.00,  # 0.00%
    'activity_points': 0.00,  # 0.00%
    'activity_days': 0.00,  # 0.00%
    'total_title_diversity': 24.15,  # 24.15%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'avg_distance_km',  # -5.31% impact when removed
    'productivity_points',  # 2.90% impact when removed
    'total_cycles',  # -2.90% impact when removed
    'pomokit_unique_words',  # -2.90% impact when removed
    'title_balance_ratio',  # 2.42% impact when removed
    'total_distance_km',  # -2.42% impact when removed
    'work_days',  # -1.93% impact when removed
    'total_time_minutes',  # 1.45% impact when removed
    'gamification_balance',  # -1.45% impact when removed
    'consistency_score',  # -1.45% impact when removed
    'strava_unique_words',  # -1.45% impact when removed
    'pomokit_title_count',  # -0.97% impact when removed
    'achievement_rate',  # -0.97% impact when removed
    'avg_time_minutes',  # -0.97% impact when removed
    'strava_title_count',  # 0.48% impact when removed
    'activity_points',  # 0.48% impact when removed
    'activity_days',  # 0.00% impact when removed
    'total_title_diversity',  # 0.00% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'avg_distance_km',  # 5.33% permutation importance
    'productivity_points',  # 1.48% permutation importance
    'total_cycles',  # 0.00% permutation importance
    'pomokit_unique_words',  # 21.33% permutation importance
    'title_balance_ratio',  # 13.33% permutation importance
    'total_distance_km',  # 1.04% permutation importance
    'work_days',  # 3.70% permutation importance
    'total_time_minutes',  # 5.78% permutation importance
    'gamification_balance',  # 2.96% permutation importance
    'consistency_score',  # 1.48% permutation importance
    'strava_unique_words',  # 2.67% permutation importance
    'pomokit_title_count',  # 2.96% permutation importance
    'achievement_rate',  # 6.96% permutation importance
    'avg_time_minutes',  # 6.81% permutation importance
    'strava_title_count',  # 0.00% permutation importance
    'activity_points',  # 0.00% permutation importance
    'activity_days',  # 0.00% permutation importance
    'total_title_diversity',  # 24.15% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'avg_distance_km': 'MINIMAL - Negligible performance impact',
    'productivity_points': 'LOW - Minor performance impact',
    'total_cycles': 'MINIMAL - Negligible performance impact',
    'pomokit_unique_words': 'MINIMAL - Negligible performance impact',
    'title_balance_ratio': 'LOW - Minor performance impact',
    'total_distance_km': 'MINIMAL - Negligible performance impact',
    'work_days': 'MINIMAL - Negligible performance impact',
    'total_time_minutes': 'LOW - Minor performance impact',
    'gamification_balance': 'MINIMAL - Negligible performance impact',
    'consistency_score': 'MINIMAL - Negligible performance impact',
    'strava_unique_words': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
    'achievement_rate': 'MINIMAL - Negligible performance impact',
    'avg_time_minutes': 'MINIMAL - Negligible performance impact',
    'strava_title_count': 'MINIMAL - Negligible performance impact',
    'activity_points': 'MINIMAL - Negligible performance impact',
    'activity_days': 'MINIMAL - Negligible performance impact',
    'total_title_diversity': 'MINIMAL - Negligible performance impact',
}
