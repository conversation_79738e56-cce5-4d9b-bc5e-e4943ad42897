# Optimal Feature Set from RFE Analysis
# Generated on 2025-07-19 00:28:47
# Best Algorithm: Random Forest
# Accuracy: 0.7100 ± 0.0620

OPTIMAL_FEATURES = [
    'achievement_rate',
    'activity_days',
    'activity_points',
    'avg_distance_km',
    'avg_time_minutes',
    'consistency_score',
    'gamification_balance',
    'pomokit_title_count',
    'pomokit_unique_words',
    'productivity_points',
    'strava_title_count',
    'strava_unique_words',
    'title_balance_ratio',
    'total_cycles',
    'total_distance_km',
    'total_time_minutes',
    'total_title_diversity',
    'work_days',
]

# Usage example:
# X_optimal = X[OPTIMAL_FEATURES]

# Comprehensive Feature Importance Analysis
BASELINE_ACCURACY = 0.7100

# Feature Impact When Removed (Ablation-Style)
FEATURE_IMPACT = {
    'consistency_score': 0.0467,  # 6.57% impact
    'total_title_diversity': 0.0467,  # 6.57% impact
    'gamification_balance': 0.0433,  # 6.10% impact
    'pomokit_unique_words': 0.0333,  # 4.69% impact
    'title_balance_ratio': 0.0333,  # 4.69% impact
    'strava_unique_words': 0.0300,  # 4.23% impact
    'avg_time_minutes': 0.0267,  # 3.76% impact
    'activity_points': 0.0233,  # 3.29% impact
    'total_cycles': 0.0233,  # 3.29% impact
    'achievement_rate': 0.0200,  # 2.82% impact
    'strava_title_count': 0.0167,  # 2.35% impact
    'activity_days': 0.0167,  # 2.35% impact
    'total_time_minutes': 0.0100,  # 1.41% impact
    'work_days': 0.0100,  # 1.41% impact
    'avg_distance_km': -0.0067,  # -0.94% impact
    'total_distance_km': -0.0033,  # -0.47% impact
    'productivity_points': -0.0033,  # -0.47% impact
    'pomokit_title_count': 0.0000,  # 0.00% impact
}

# Feature Importance (Permutation Importance)
FEATURE_IMPORTANCE = {
    'consistency_score': 1.60,  # 1.60%
    'total_title_diversity': 21.50,  # 21.50%
    'gamification_balance': 1.20,  # 1.20%
    'pomokit_unique_words': 21.36,  # 21.36%
    'title_balance_ratio': 23.23,  # 23.23%
    'strava_unique_words': 3.34,  # 3.34%
    'avg_time_minutes': 6.94,  # 6.94%
    'activity_points': 0.00,  # 0.00%
    'total_cycles': 0.53,  # 0.53%
    'achievement_rate': 4.81,  # 4.81%
    'strava_title_count': 0.00,  # 0.00%
    'activity_days': 0.00,  # 0.00%
    'total_time_minutes': 5.61,  # 5.61%
    'work_days': 1.34,  # 1.34%
    'avg_distance_km': 6.54,  # 6.54%
    'total_distance_km': 1.74,  # 1.74%
    'productivity_points': 0.13,  # 0.13%
    'pomokit_title_count': 0.13,  # 0.13%
}

# Sorted by impact when removed (descending)
FEATURES_BY_IMPACT = [
    'consistency_score',  # 6.57% impact when removed
    'total_title_diversity',  # 6.57% impact when removed
    'gamification_balance',  # 6.10% impact when removed
    'pomokit_unique_words',  # 4.69% impact when removed
    'title_balance_ratio',  # 4.69% impact when removed
    'strava_unique_words',  # 4.23% impact when removed
    'avg_time_minutes',  # 3.76% impact when removed
    'activity_points',  # 3.29% impact when removed
    'total_cycles',  # 3.29% impact when removed
    'achievement_rate',  # 2.82% impact when removed
    'strava_title_count',  # 2.35% impact when removed
    'activity_days',  # 2.35% impact when removed
    'total_time_minutes',  # 1.41% impact when removed
    'work_days',  # 1.41% impact when removed
    'avg_distance_km',  # -0.94% impact when removed
    'total_distance_km',  # -0.47% impact when removed
    'productivity_points',  # -0.47% impact when removed
    'pomokit_title_count',  # 0.00% impact when removed
]

# Sorted by permutation importance (descending)
FEATURES_BY_IMPORTANCE = [
    'consistency_score',  # 1.60% permutation importance
    'total_title_diversity',  # 21.50% permutation importance
    'gamification_balance',  # 1.20% permutation importance
    'pomokit_unique_words',  # 21.36% permutation importance
    'title_balance_ratio',  # 23.23% permutation importance
    'strava_unique_words',  # 3.34% permutation importance
    'avg_time_minutes',  # 6.94% permutation importance
    'activity_points',  # 0.00% permutation importance
    'total_cycles',  # 0.53% permutation importance
    'achievement_rate',  # 4.81% permutation importance
    'strava_title_count',  # 0.00% permutation importance
    'activity_days',  # 0.00% permutation importance
    'total_time_minutes',  # 5.61% permutation importance
    'work_days',  # 1.34% permutation importance
    'avg_distance_km',  # 6.54% permutation importance
    'total_distance_km',  # 1.74% permutation importance
    'productivity_points',  # 0.13% permutation importance
    'pomokit_title_count',  # 0.13% permutation importance
]

# Feature Interpretations
FEATURE_INTERPRETATIONS = {
    'consistency_score': 'MEDIUM - Moderate performance impact',
    'total_title_diversity': 'MEDIUM - Moderate performance impact',
    'gamification_balance': 'MEDIUM - Moderate performance impact',
    'pomokit_unique_words': 'LOW - Minor performance impact',
    'title_balance_ratio': 'LOW - Minor performance impact',
    'strava_unique_words': 'LOW - Minor performance impact',
    'avg_time_minutes': 'LOW - Minor performance impact',
    'activity_points': 'LOW - Minor performance impact',
    'total_cycles': 'LOW - Minor performance impact',
    'achievement_rate': 'LOW - Minor performance impact',
    'strava_title_count': 'LOW - Minor performance impact',
    'activity_days': 'LOW - Minor performance impact',
    'total_time_minutes': 'LOW - Minor performance impact',
    'work_days': 'LOW - Minor performance impact',
    'avg_distance_km': 'MINIMAL - Negligible performance impact',
    'total_distance_km': 'MINIMAL - Negligible performance impact',
    'productivity_points': 'MINIMAL - Negligible performance impact',
    'pomokit_title_count': 'MINIMAL - Negligible performance impact',
}
