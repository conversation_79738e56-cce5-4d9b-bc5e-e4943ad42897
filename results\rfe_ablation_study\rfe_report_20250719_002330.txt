================================================================================
🔍 RFE ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 18
   • Total Samples: 300

🤖 ALGORITHMS TESTED:
   1. Logistic Regression (logistic_regression)
   2. Random Forest (random_forest)
   3. Gradient Boosting (gradient_boosting)
   4. Support Vector Machine (svm)

📊 FEATURE COUNT RANGE TESTED:
   • Range: [4, 5, 9, 10, 13, 15, 18, 20, 25, 30]
   • Total Experiments: 28

🏆 BEST OVERALL PERFORMANCE:
   • Algorithm: Random Forest
   • Features Used: 9
   • Accuracy: 0.7067 ± 0.0646
   • F1-Score: 0.6589
   • ⚠️ Overfitting Risk: 0.2933 (HIGH - Train-Test gap > 10%)

🎯 BEST PERFORMANCE BY ALGORITHM:
   • Logistic Regression:
     - Features: 13
     - Accuracy: 0.6667 ± 0.0506
     - F1-Score: 0.6478
     - ⚠️ Overfitting: 0.0608 (MODERATE)
   • Random Forest:
     - Features: 9
     - Accuracy: 0.7067 ± 0.0646
     - F1-Score: 0.6589
     - ⚠️ Overfitting: 0.2933 (HIGH)
   • Gradient Boosting:
     - Features: 10
     - Accuracy: 0.6833 ± 0.0527
     - F1-Score: 0.6442
     - ⚠️ Overfitting: 0.3158 (HIGH)
   • Support Vector Machine:
     - Features: 9
     - Accuracy: 0.6633 ± 0.0499
     - F1-Score: 0.6369
     - ✅ Overfitting: 0.0450 (LOW)

⭐ MOST FREQUENTLY SELECTED FEATURES:
    1. pomokit_unique_words: 27 times (96.4%)
    2. title_balance_ratio: 26 times (92.9%)
    3. achievement_rate: 25 times (89.3%)
    4. work_days: 23 times (82.1%)
    5. pomokit_title_count: 22 times (78.6%)
    6. total_distance_km: 20 times (71.4%)
    7. total_cycles: 19 times (67.9%)
    8. avg_distance_km: 18 times (64.3%)
    9. total_title_diversity: 18 times (64.3%)
   10. productivity_points: 16 times (57.1%)

🎖️ MOST CONSISTENT FEATURES (in top 10 results):
    1. total_title_diversity: 10/10 top results
    2. achievement_rate: 10/10 top results
    3. title_balance_ratio: 10/10 top results
    4. pomokit_title_count: 10/10 top results
    5. pomokit_unique_words: 10/10 top results
    6. total_time_minutes: 9/10 top results
    7. total_distance_km: 9/10 top results
    8. work_days: 8/10 top results
    9. avg_time_minutes: 8/10 top results
   10. total_cycles: 7/10 top results

🔍 OVERFITTING ANALYSIS:
   • Best Model Overfitting Score: 0.2933
   • ⚠️ HIGH OVERFITTING RISK: Train-Test gap > 10%
   • Recommendation: Consider regularization or more data

   📊 Overfitting by Algorithm:
     ⚠️ Logistic Regression: 0.0608 (MODERATE)
     ⚠️ Random Forest: 0.2933 (HIGH)
     ⚠️ Gradient Boosting: 0.3158 (HIGH)
     ✅ Support Vector Machine: 0.0450 (LOW)

📊 COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS:
   • Algorithm: Random Forest
   • Method: Permutation Importance + Ablation Analysis
   • Total Features: 9
   • Baseline Accuracy: 0.6933

   🎯 Feature Impact Analysis (Top 10):
      1. ⚪ pomokit_unique_words:
         - Baseline: 0.6933 | Without: 0.7167
         - Impact: -0.0233 (-3.37%)
         - MINIMAL - Negligible performance impact
      2. ▫️ title_balance_ratio:
         - Baseline: 0.6933 | Without: 0.6733
         - Impact: 0.0200 (2.88%)
         - LOW - Minor performance impact
      3. ⚪ total_title_diversity:
         - Baseline: 0.6933 | Without: 0.7100
         - Impact: -0.0167 (-2.40%)
         - MINIMAL - Negligible performance impact
      4. ⚪ achievement_rate:
         - Baseline: 0.6933 | Without: 0.7100
         - Impact: -0.0167 (-2.40%)
         - MINIMAL - Negligible performance impact
      5. ⚪ total_distance_km:
         - Baseline: 0.6933 | Without: 0.7100
         - Impact: -0.0167 (-2.40%)
         - MINIMAL - Negligible performance impact
      6. ⚪ pomokit_title_count:
         - Baseline: 0.6933 | Without: 0.7067
         - Impact: -0.0133 (-1.92%)
         - MINIMAL - Negligible performance impact
      7. ⚪ total_cycles:
         - Baseline: 0.6933 | Without: 0.7067
         - Impact: -0.0133 (-1.92%)
         - MINIMAL - Negligible performance impact
      8. ▫️ total_time_minutes:
         - Baseline: 0.6933 | Without: 0.6833
         - Impact: 0.0100 (1.44%)
         - LOW - Minor performance impact
      9. ⚪ work_days:
         - Baseline: 0.6933 | Without: 0.7033
         - Impact: -0.0100 (-1.44%)
         - MINIMAL - Negligible performance impact

   📊 Permutation Importance Summary:
     • pomokit_unique_words: 14.29% (score: 0.0823 ±0.0070)
     • title_balance_ratio: 23.13% (score: 0.1333 ±0.0092)
     • total_title_diversity: 20.59% (score: 0.1187 ±0.0138)
     • achievement_rate: 12.72% (score: 0.0733 ±0.0084)
     • total_distance_km: 11.91% (score: 0.0687 ±0.0118)

✅ RECOMMENDED OPTIMAL FEATURE SET:
   • Algorithm: Random Forest
   • Number of Features: 9
   • Selected Features:
      1. achievement_rate
      2. pomokit_title_count
      3. pomokit_unique_words
      4. title_balance_ratio
      5. total_cycles
      6. total_distance_km
      7. total_time_minutes
      8. total_title_diversity
      9. work_days
================================================================================