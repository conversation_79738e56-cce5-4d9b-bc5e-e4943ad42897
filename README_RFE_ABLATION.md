# RFE Ablation Study dan Advanced Feature Selection Pipeline

## Overview

Proyek ini telah diperluas dengan dua file baru yang mengimplementasikan analisis fitur menggunakan Recursive Feature Elimination (RFE) dan perbandingan dengan metode eliminasi standar:

1. **`src/rfe_ablation_study.py`** - RFE Ablation Study dengan multiple algorithms
2. **`main4.py`** - Advanced Feature Selection Pipeline yang mengintegrasikan kedua metode

## File Baru

### 1. `src/rfe_ablation_study.py`

**Fitur:**
- Recursive Feature Elimination (RFE) dengan 4 algoritma:
  - Logistic Regression
  - Random Forest (Ensemble)
  - Gradient Boosting (Ensemble)
  - Support Vector Machine (SVM)
- Analisis performa dengan berbagai jumlah fitur
- Analisis frekuensi dan konsistensi fitur
- <PERSON>poran komprehensif dengan rekomendasi

**Cara Penggunaan:**
```bash
# Jalankan RFE ablation study secara standalone
python src/rfe_ablation_study.py
```

**Output:**
- `results/rfe_ablation_study/rfe_results_YYYYMMDD_HHMMSS.csv` - Hasil detail
- `results/rfe_ablation_study/rfe_report_YYYYMMDD_HHMMSS.txt` - Laporan analisis
- `results/rfe_ablation_study/optimal_features_YYYYMMDD_HHMMSS.py` - Kode fitur optimal

### 2. `main4.py`

**Fitur:**
- Integrasi `clean_ablation_study.py` (eliminasi standar) dengan `rfe_ablation_study.py`
- Perbandingan hasil dari kedua metode
- Multiple pipeline modes
- Analisis komparatif yang komprehensif

**Pipeline Modes:**
1. **Complete Pipeline** (default): Data Processing + Both Ablation Studies + Comparison
2. **RFE Only** (`--rfe-only`): RFE Analysis only
3. **Standard Only** (`--standard-only`): Standard Ablation Study only
4. **Comparison Only** (`--comparison-only`): Compare existing results
5. **ML Only** (`--ml-only`): Skip data processing

**Cara Penggunaan:**

```bash
# Jalankan complete pipeline (default)
python main4.py

# Jalankan hanya RFE analysis
python main4.py --rfe-only

# Jalankan hanya standard ablation study
python main4.py --standard-only

# Skip data processing, hanya analisis fitur
python main4.py --ml-only

# Skip machine learning components
python main4.py --no-ml
```

## Algoritma yang Digunakan

### RFE Ablation Study (`rfe_ablation_study.py`)

1. **Logistic Regression**
   - Solver: liblinear
   - Class weight: balanced
   - Max iterations: 1000

2. **Random Forest** (Ensemble)
   - N estimators: 100
   - Class weight: balanced
   - N jobs: -1 (parallel processing)

3. **Gradient Boosting** (Ensemble)
   - N estimators: 100
   - Learning rate: default (0.1)

4. **Support Vector Machine (SVM)**
   - Kernel: RBF
   - Class weight: balanced

### Standard Ablation Study (`clean_ablation_study.py`)

- **Logistic Regression** dengan eliminasi satu fitur per waktu

## Output dan Hasil

### RFE Ablation Study Output:
- **Best Overall Performance**: Algoritma dan konfigurasi fitur terbaik
- **Best by Algorithm**: Performa terbaik untuk setiap algoritma
- **Feature Importance by Frequency**: Fitur yang paling sering dipilih
- **Most Consistent Features**: Fitur yang konsisten muncul di hasil terbaik
- **Optimal Feature Set**: Rekomendasi set fitur optimal

### Comparison Analysis Output:
- **Performance Comparison**: Perbandingan akurasi antara metode
- **Feature Set Comparison**: Analisis overlap dan perbedaan fitur
- **Winner Determination**: Metode mana yang memberikan hasil terbaik
- **Recommendations**: Rekomendasi implementasi

## Struktur Directory Output

```
results/
├── rfe_ablation_study/
│   ├── rfe_results_YYYYMMDD_HHMMSS.csv
│   ├── rfe_report_YYYYMMDD_HHMMSS.txt
│   └── optimal_features_YYYYMMDD_HHMMSS.py
├── clean_ablation_study/
│   ├── ablation_results_YYYYMMDD_HHMMSS.csv
│   ├── feature_importance_YYYYMMDD_HHMMSS.csv
│   └── feature_impact_report_YYYYMMDD_HHMMSS.txt
└── feature_selection_comparison/
    └── feature_selection_comparison_YYYYMMDD_HHMMSS.txt
```

## Prerequisites

Pastikan dataset safe sudah tersedia dengan menjalankan salah satu pipeline berikut terlebih dahulu:
- `python main1.py` (Regular pipeline)
- `python main2.py` (Bias-corrected pipeline)  
- `python main3.py` (Title-only pipeline)

## Contoh Workflow

1. **Persiapan Data:**
   ```bash
   python main1.py  # Atau main2.py/main3.py
   ```

2. **Analisis Fitur Lengkap:**
   ```bash
   python main4.py
   ```

3. **Atau Analisis Spesifik:**
   ```bash
   # Hanya RFE
   python main4.py --rfe-only
   
   # Hanya standard ablation
   python main4.py --standard-only
   ```

## Interpretasi Hasil

### RFE Analysis:
- **Accuracy**: Akurasi cross-validation
- **F1-Score**: F1-score macro average
- **Feature Count**: Jumlah fitur yang dipilih
- **Algorithm**: Algoritma yang digunakan

### Comparison Analysis:
- **Winner**: Metode yang memberikan akurasi terbaik
- **Improvement**: Selisih akurasi antara metode
- **Overlap Percentage**: Persentase fitur yang sama antara kedua metode
- **Common Features**: Fitur yang dipilih oleh kedua metode (paling robust)

## Keunggulan

1. **Multiple Algorithms**: Menggunakan 4 algoritma berbeda untuk RFE
2. **Comprehensive Analysis**: Analisis mendalam dengan berbagai metrik
3. **Comparative Study**: Perbandingan langsung antara metode eliminasi
4. **Robust Feature Selection**: Identifikasi fitur yang konsisten dipilih
5. **Automated Pipeline**: Workflow otomatis dari data processing hingga analisis
6. **Detailed Reporting**: Laporan komprehensif dengan rekomendasi implementasi

## Troubleshooting

**Error: "No safe dataset found"**
- Jalankan salah satu main pipeline (main1.py, main2.py, atau main3.py) terlebih dahulu

**Error: "Raw data directory not found"**
- Pastikan folder `dataset/raw/` berisi `strava.csv` dan `pomokit.csv`

**Memory Issues:**
- Kurangi jumlah algoritma di `self.algorithms` dalam `rfe_ablation_study.py`
- Gunakan `--ml-only` untuk skip data processing jika dataset sudah ada
