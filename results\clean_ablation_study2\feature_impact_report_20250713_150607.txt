================================================================================
📊 FEATURE IMPACT ANALYSIS REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Samples: N/A
   • Total Features: 18

📝 ALL FEATURES USED IN ANALYSIS:
    1. achievement_rate
    2. activity_days
    3. activity_points
    4. avg_distance_km
    5. avg_time_minutes
    6. consistency_score
    7. gamification_balance
    8. pomokit_title_count
    9. pomokit_unique_words
   10. productivity_points
   11. strava_title_count
   12. strava_unique_words
   13. title_balance_ratio
   14. total_cycles
   15. total_distance_km
   16. total_time_minutes
   17. total_title_diversity
   18. work_days

❌ RECOMMENDED FEATURES TO REMOVE (6):
   • total_distance_km: +1.00% impact - 🟢 Netral
   • total_time_minutes: +1.00% impact - 🟢 Netral
   • title_balance_ratio: +1.00% impact - 🟢 Netral
   • avg_time_minutes: +1.00% impact - 🟢 Netral
   • productivity_points: +1.00% impact - 🟢 Netral
   • gamification_balance: +1.00% impact - 🟢 Netral

🟢 NEUTRAL FEATURES (Optional - 12):
   • total_cycles: -0.33% impact - 🟢 Netral
   • work_days: -0.33% impact - 🟢 Netral
   • pomokit_title_count: -0.33% impact - 🟢 Netral
   • activity_points: +0.00% impact - 🟢 Netral
   • activity_days: +0.00% impact - 🟢 Netral
   • total_title_diversity: +0.00% impact - 🟢 Netral
   • pomokit_unique_words: +0.00% impact - 🟢 Netral
   • strava_title_count: +0.00% impact - 🟢 Netral
   • avg_distance_km: +0.33% impact - 🟢 Netral
   • strava_unique_words: +0.33% impact - 🟢 Netral
   • achievement_rate: +0.67% impact - 🟢 Netral
   • consistency_score: +0.67% impact - 🟢 Netral

📈 BASELINE MODEL PERFORMANCE:
   • Baseline Accuracy: 0.6567 (65.67%)
   • Total Features: 18

🎯 FEATURE CATEGORIES:
   • 🔴 Critical Features (hurt when removed): 0
   • ⚠️ Noise Features (help when removed): 6
   • 🟢 Neutral Features: 12

🏆 TOP 5 MOST IMPORTANT FEATURES:
   1. total_cycles
      • Impact when removed: -0.33%
      • Accuracy without: 0.6533
      • Status: 🟢 Netral
   2. work_days
      • Impact when removed: -0.33%
      • Accuracy without: 0.6533
      • Status: 🟢 Netral
   3. pomokit_title_count
      • Impact when removed: -0.33%
      • Accuracy without: 0.6533
      • Status: 🟢 Netral
   4. activity_points
      • Impact when removed: +0.00%
      • Accuracy without: 0.6567
      • Status: 🟢 Netral
   5. activity_days
      • Impact when removed: +0.00%
      • Accuracy without: 0.6567
      • Status: 🟢 Netral

⚠️ POTENTIAL NOISE FEATURES (Bottom 5):
   14. total_time_minutes
      • Impact when removed: +1.00%
      • Accuracy without: 0.6667
      • Status: 🟢 Netral
   15. title_balance_ratio
      • Impact when removed: +1.00%
      • Accuracy without: 0.6667
      • Status: 🟢 Netral
   16. avg_time_minutes
      • Impact when removed: +1.00%
      • Accuracy without: 0.6667
      • Status: 🟢 Netral
   17. productivity_points
      • Impact when removed: +1.00%
      • Accuracy without: 0.6667
      • Status: 🟢 Netral
   18. gamification_balance
      • Impact when removed: +1.00%
      • Accuracy without: 0.6667
      • Status: 🟢 Netral

💡 RECOMMENDATIONS:
   • Consider removing 6 noise features:
     - total_distance_km (improves by +1.00%)
     - total_time_minutes (improves by +1.00%)
     - title_balance_ratio (improves by +1.00%)
     - avg_time_minutes (improves by +1.00%)
     - productivity_points (improves by +1.00%)
     - gamification_balance (improves by +1.00%)

🎯 OPTIMAL FEATURE SET (18 features):
    1. achievement_rate (+0.67%)
    2. activity_days (+0.00%)
    3. activity_points (+0.00%)
    4. avg_distance_km (+0.33%)
    5. avg_time_minutes (+1.00%)
    6. consistency_score (+0.67%)
    7. gamification_balance (+1.00%)
    8. pomokit_title_count (-0.33%)
    9. pomokit_unique_words (+0.00%)
   10. productivity_points (+1.00%)
   11. strava_title_count (+0.00%)
   12. strava_unique_words (+0.33%)
   13. title_balance_ratio (+1.00%)
   14. total_cycles (-0.33%)
   15. total_distance_km (+1.00%)
   16. total_time_minutes (+1.00%)
   17. total_title_diversity (+0.00%)
   18. work_days (-0.33%)

🚀 IMPROVEMENT POTENTIAL:
   • Current Accuracy: 0.6567 (65.67%)
   • Potential Accuracy: 0.7167 (71.67%)
   • Potential Improvement: +6.00%

📊 FEATURE SELECTION SUMMARY:
   • Original Features: 18
   • Optimal Features: 18
   • Features Removed: 0
   • Reduction: 0.0%

💻 IMPLEMENTATION GUIDE:
   1. Remove 6 noise features for immediate improvement
   2. Use optimal feature set with 18 features
   3. Expected accuracy improvement: +6.00%
   4. Model complexity reduction: 0.0%
================================================================================