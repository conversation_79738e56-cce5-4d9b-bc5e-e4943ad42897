# 📊 PANDUAN GAMBAR DAN TABEL UNTUK LAPORAN PENELITIAN

## 🎯 **RINGKASAN GAMBAR YANG DIBUTUHKAN**

### **Total: 12 Gambar + 4 Tabel**
- ✅ **4 Gambar sudah ada** (dari `results/visualizations/`)
- ✅ **7 Gambar baru dibuat** (di `laporan-akhir/figures/`)
- 📋 **4 Tabel** (dari hasil analisis)

---

## 📍 **PENEMPATAN GAMBAR PER BAB**

### **BAB 2 - LANDASAN TEORI**

#### **Gambar 2.1 - Kerangka Teoritis**
- **File**: `laporan-akhir/figures/gambar_2_1_kerangka_teoritis.png`
- **Lokasi**: Bab 2.9.1 (Model Hubungan Aktivitas Fisik dan Produktivitas)
- **Caption**: "Gambar 2.1 Kerangka Teoritis Hubungan Aktivitas Fisik, Produktivitas, dan Fatigue"
- **Deskripsi**: Diagram konseptual menunjukkan hubungan antar variabel dengan koefisien korelasi

---

### **BAB 3 - METODOLOGI PENELITIAN**

#### **Gambar 3.1 - Diagram Alur Metodologi**
- **File**: `laporan-akhir/figures/gambar_3_1_metodologi_flowchart.png`
- **Lokasi**: Bab 3.1 (Desain Penelitian)
- **Caption**: "Gambar 3.1 Diagram Alur Metodologi Penelitian"
- **Deskripsi**: Flowchart lengkap dari data collection hingga results analysis

#### **Gambar 3.2 - Arsitektur ML Pipeline**
- **File**: `laporan-akhir/figures/gambar_3_2_ml_pipeline.png`
- **Lokasi**: Bab 3.8 (Machine Learning Pipeline)
- **Caption**: "Gambar 3.2 Arsitektur Machine Learning Pipeline"
- **Deskripsi**: Diagram arsitektur pipeline ML dengan ablation study branch

---

### **BAB 4 - EKSPERIMEN DAN HASIL**

#### **Gambar 4.1 - Temuan Utama Korelasi**
- **File**: `results/visualizations/01_main_finding.png`
- **Lokasi**: Bab 4.3.1 (Korelasi Aktivitas Fisik dan Produktivitas)
- **Caption**: "Gambar 4.1 Korelasi Terkuat antara Productivity Points dan Total Cycles (r=0.911)"
- **Deskripsi**: Scatter plot dengan trend line menunjukkan korelasi terkuat

#### **Gambar 4.2 - Efek Gamifikasi**
- **File**: `results/visualizations/02_gamification_effects.png`
- **Lokasi**: Bab 4.8.1 (Achievement Rate dan Konsistensi Aktivitas)
- **Caption**: "Gambar 4.2 Efek Gamifikasi terhadap Produktivitas Mahasiswa"
- **Deskripsi**: Comparison plot menunjukkan dampak gamifikasi

#### **Gambar 4.3 - Perbandingan Achievement**
- **File**: `results/visualizations/03_achievement_comparison.png`
- **Lokasi**: Bab 4.8.1 (Achievement Rate dan Konsistensi Aktivitas)
- **Caption**: "Gambar 4.3 Produktivitas berdasarkan Tingkat Achievement"
- **Deskripsi**: Bar chart dengan kategori low/medium/high achievement

#### **Gambar 4.4 - Distribusi Produktivitas**
- **File**: `results/visualizations/04_productivity_distribution.png`
- **Lokasi**: Bab 4.1.1 (Karakteristik Dataset)
- **Caption**: "Gambar 4.4 Distribusi Produktivitas Mahasiswa (Total Cycles)"
- **Deskripsi**: Histogram dan box plot dengan statistik deskriptif

#### **Gambar 4.5 - Confusion Matrix**
- **File**: `laporan-akhir/figures/gambar_4_5_confusion_matrix.png`
- **Lokasi**: Bab 4.4.3 (Advanced Model with SMOTE)
- **Caption**: "Gambar 4.5 Confusion Matrix Model Random Forest dengan SMOTE"
- **Deskripsi**: Heatmap 3x3 untuk klasifikasi low/medium/high risk

#### **Gambar 4.6 - Feature Importance**
- **File**: `laporan-akhir/figures/gambar_4_6_feature_importance.png`
- **Lokasi**: Bab 4.9.1 (Feature Importance Visualization)
- **Caption**: "Gambar 4.6 Feature Importance berdasarkan SHAP Values"
- **Deskripsi**: Horizontal bar chart dengan SHAP importance scores

#### **Gambar 4.7 - Perbandingan Model**
- **File**: `laporan-akhir/figures/gambar_4_7_model_comparison.png`
- **Lokasi**: Bab 4.4.1 (Baseline Model Results)
- **Caption**: "Gambar 4.7 Perbandingan Performa Model Machine Learning"
- **Deskripsi**: Grouped bar chart untuk RF, SVM, Neural Network

#### **Gambar 4.8 - Ablation Study**
- **File**: `laporan-akhir/figures/gambar_4_8_ablation_study.png`
- **Lokasi**: Bab 4.5.1 (Individual Feature Impact Analysis)
- **Caption**: "Gambar 4.8 Hasil Ablation Study: Dampak Removal Feature"
- **Deskripsi**: Bar chart dengan color coding berdasarkan impact level

---

## 📋 **TABEL YANG DIBUTUHKAN**

### **Tabel 4.1 - Korelasi Signifikan**
- **Source**: `results/reports/significant_correlations.csv`
- **Lokasi**: Bab 4.3.1 (Korelasi Aktivitas Fisik dan Produktivitas)
- **Caption**: "Tabel 4.1 Korelasi Signifikan antar Variabel Penelitian"
- **Kolom**: Variable 1, Variable 2, Correlation, P-value, Significance

### **Tabel 4.2 - Performa Model**
- **Source**: Hasil evaluasi model dari pipeline
- **Lokasi**: Bab 4.4.3 (Advanced Model with SMOTE)
- **Caption**: "Tabel 4.2 Performa Model Machine Learning"
- **Kolom**: Model, Accuracy, Precision, Recall, F1-Score

### **Tabel 4.3 - Ablation Study Results**
- **Source**: `results/clean_ablation_study*/feature_importance_*.csv`
- **Lokasi**: Bab 4.5.1 (Individual Feature Impact Analysis)
- **Caption**: "Tabel 4.3 Hasil Systematic Ablation Study"
- **Kolom**: Feature, Baseline Accuracy, Accuracy Without, Impact, Importance Score

### **Tabel 4.4 - Karakteristik Dataset**
- **Source**: Statistik deskriptif dari dataset
- **Lokasi**: Bab 4.1.1 (Karakteristik Dataset)
- **Caption**: "Tabel 4.4 Statistik Deskriptif Dataset Penelitian"
- **Kolom**: Variable, Mean, Std, Min, Max, N

---

## 🎨 **FORMAT DAN KUALITAS GAMBAR**

### **Spesifikasi Teknis:**
- **Resolution**: 300 DPI (print quality)
- **Format**: PNG dengan transparansi
- **Size**: Optimal untuk A4 (12x8 inch max)
- **Font**: Readable, minimum 10pt

### **Style Guidelines:**
- **Color Palette**: Professional (blues, grays, accent colors)
- **Grid**: Subtle grid lines (alpha=0.3)
- **Labels**: Clear axis labels dan titles
- **Legend**: Positioned appropriately
- **Annotations**: Value labels where needed

---

## 📝 **CARA MENGGUNAKAN GAMBAR DALAM LAPORAN**

### **Format Citation dalam Teks:**
```markdown
Seperti yang ditunjukkan pada Gambar 4.1, terdapat korelasi positif yang kuat 
antara productivity points dan total cycles (r=0.911, p<0.001).
```

### **Format Caption:**
```markdown
**Gambar 4.1** Korelasi Terkuat antara Productivity Points dan Total Cycles (r=0.911)
```

### **Penempatan:**
- Gambar ditempatkan setelah paragraf yang mereferensikannya
- Jarak yang cukup dari teks (1-2 baris)
- Caption di bawah gambar
- Nomor gambar berurutan per bab

---

## ✅ **CHECKLIST IMPLEMENTASI**

### **Gambar Sudah Siap:**
- [x] Gambar 2.1 - Kerangka Teoritis
- [x] Gambar 3.1 - Metodologi Flowchart  
- [x] Gambar 3.2 - ML Pipeline Architecture
- [x] Gambar 4.1 - Main Finding (existing)
- [x] Gambar 4.2 - Gamification Effects (existing)
- [x] Gambar 4.3 - Achievement Comparison (existing)
- [x] Gambar 4.4 - Productivity Distribution (existing)
- [x] Gambar 4.5 - Confusion Matrix
- [x] Gambar 4.6 - Feature Importance
- [x] Gambar 4.7 - Model Comparison
- [x] Gambar 4.8 - Ablation Study

### **Tabel Perlu Dibuat:**
- [ ] Tabel 4.1 - Korelasi Signifikan
- [ ] Tabel 4.2 - Performa Model
- [ ] Tabel 4.3 - Ablation Study Results
- [ ] Tabel 4.4 - Karakteristik Dataset

### **Integrasi ke Laporan:**
- [ ] Insert gambar ke file markdown
- [ ] Tambahkan caption dan referensi
- [ ] Update cross-references dalam teks
- [ ] Verify gambar quality dan readability

---

## 🚀 **NEXT STEPS**

1. **Copy gambar** dari `results/visualizations/` dan `laporan-akhir/figures/` ke folder laporan
2. **Buat tabel** dari file CSV yang ada
3. **Insert gambar** ke dalam file markdown masing-masing bab
4. **Update referensi** gambar dalam teks
5. **Review kualitas** dan konsistensi visual

**Total gambar siap: 12/12 ✅**
**Total tabel perlu dibuat: 4/4 📋**
