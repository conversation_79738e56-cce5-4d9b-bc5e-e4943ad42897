algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,4,"['productivity_points', 'pomokit_title_count', 'total_cycles', 'work_days']",0.6399999999999999,0.04027681991198188,0.6284441724801699,0.047671060818102615,0.6427011756733119,0.64789932990494,0.6825,0.04250000000000009,success
logistic_regression,Logistic Regression,5,"['productivity_points', 'pomokit_title_count', 'total_cycles', 'pomokit_unique_words', 'work_days']",0.65,0.05055250296034364,0.6366202126302128,0.05408380167444042,0.6501204317549998,0.6534548854604956,0.685,0.03500000000000003,success
logistic_regression,Logistic Regression,9,"['achievement_rate', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6566666666666666,0.04546060565661951,0.6355407861335417,0.05663860843876291,0.6463196532384519,0.6467882187938289,0.7158333333333334,0.05916666666666681,success
logistic_regression,Logistic Regression,10,"['achievement_rate', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6599999999999999,0.054365021434333596,0.6429047164989187,0.07497740945878181,0.6484715362093991,0.6578276453171263,0.7216666666666667,0.06166666666666676,success
logistic_regression,Logistic Regression,13,"['total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'strava_unique_words', 'avg_distance_km', 'gamification_balance', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6666666666666666,0.05055250296034365,0.6477817896498906,0.07312764375585032,0.6517866651245123,0.6622004051737572,0.7275,0.060833333333333406,success
logistic_regression,Logistic Regression,15,"['total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6566666666666666,0.04294699575575042,0.6344341343435651,0.05821195325261284,0.6435519335167093,0.6467882187938289,0.73,0.07333333333333336,success
logistic_regression,Logistic Regression,18,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'activity_days', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
random_forest,Random Forest,4,"['total_title_diversity', 'title_balance_ratio', 'pomokit_title_count', 'pomokit_unique_words']",0.6533333333333333,0.07408703590297623,0.6057092231594634,0.07528886212473458,0.6427111981120179,0.5897374162381175,1.0,0.3466666666666667,success
random_forest,Random Forest,5,"['total_title_diversity', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'pomokit_unique_words']",0.6866666666666668,0.08259674462242578,0.6384993513984958,0.10159871293677082,0.6644009305864194,0.6242083528128408,1.0,0.31333333333333324,success
random_forest,Random Forest,9,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.7066666666666667,0.06463573143221771,0.6589477251836433,0.08405734643898215,0.7121432020298452,0.6364352501168772,1.0,0.29333333333333333,success
random_forest,Random Forest,10,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'avg_time_minutes', 'total_cycles', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6766666666666666,0.06960204339273701,0.5972098505349308,0.05005472479641746,0.6443846141150062,0.5804184198223469,1.0,0.32333333333333336,success
random_forest,Random Forest,13,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'avg_distance_km', 'avg_time_minutes', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6900000000000001,0.05734883511361751,0.6233721478094714,0.0926004044218003,0.6570747440132747,0.6105165965404395,1.0,0.30999999999999994,success
random_forest,Random Forest,15,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'strava_unique_words', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6799999999999999,0.06616477747093069,0.6090008958943629,0.09738113719099314,0.6516812772787588,0.5917710768271778,1.0,0.32000000000000006,success
random_forest,Random Forest,18,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'activity_days', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6799999999999999,0.06616477747093072,0.6170633631354498,0.09072067136423868,0.6593595573156836,0.598509428081658,1.0,0.32000000000000006,success
gradient_boosting,Gradient Boosting,4,"['total_title_diversity', 'achievement_rate', 'title_balance_ratio', 'pomokit_unique_words']",0.6466666666666667,0.05312459150169743,0.5868729048248641,0.051989653977002406,0.6083199930535821,0.5789894031478884,0.9941666666666666,0.3474999999999999,success
gradient_boosting,Gradient Boosting,5,"['total_title_diversity', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'pomokit_unique_words']",0.6666666666666666,0.059628479399994376,0.6165647615668803,0.07123517098590675,0.6303526980599259,0.6095940470624903,0.9950000000000001,0.3283333333333335,success
gradient_boosting,Gradient Boosting,9,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'avg_distance_km', 'avg_time_minutes', 'total_distance_km', 'pomokit_unique_words']",0.67,0.02449489742783178,0.6293502843597212,0.05843755537004678,0.6593437245173988,0.6124474053295932,1.0,0.32999999999999996,success
gradient_boosting,Gradient Boosting,10,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'avg_distance_km', 'avg_time_minutes', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6833333333333333,0.052704627669472995,0.6442420929151415,0.05423249717007811,0.7265246961324036,0.6217936730559451,0.9991666666666668,0.3158333333333334,success
gradient_boosting,Gradient Boosting,13,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'title_balance_ratio', 'pomokit_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6833333333333333,0.062360956446232345,0.6391397103069528,0.06617677040375253,0.6862525809493881,0.6212381175003896,1.0,0.31666666666666665,success
gradient_boosting,Gradient Boosting,15,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'pomokit_title_count', 'strava_unique_words', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6766666666666667,0.045460605656619524,0.6429265647686345,0.03608213244128211,0.685549404781861,0.6240875798659811,0.9991666666666668,0.3225,success
gradient_boosting,Gradient Boosting,18,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'activity_days', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.65,0.05055250296034368,0.6134715530627655,0.0476359496029745,0.6473522535983005,0.5975642823749415,0.9991666666666668,0.34916666666666674,success
svm,Support Vector Machine,4,"['achievement_rate', 'title_balance_ratio', 'pomokit_unique_words', 'work_days']",0.6233333333333333,0.062003584125794216,0.5974221144958471,0.06821188467561821,0.5964439075355884,0.6525315568022441,0.6741666666666667,0.0508333333333334,success
svm,Support Vector Machine,5,"['achievement_rate', 'title_balance_ratio', 'gamification_balance', 'pomokit_unique_words', 'work_days']",0.6233333333333333,0.06960204339273698,0.6070951922627947,0.0762611794667704,0.597427690405533,0.660188561633162,0.6816666666666666,0.05833333333333335,success
svm,Support Vector Machine,9,"['achievement_rate', 'title_balance_ratio', 'productivity_points', 'avg_distance_km', 'gamification_balance', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.6633333333333333,0.049888765156985884,0.6368551051364209,0.05630121126350061,0.6254726564466431,0.684978182951535,0.7083333333333334,0.04500000000000004,success
svm,Support Vector Machine,10,"['achievement_rate', 'title_balance_ratio', 'productivity_points', 'avg_distance_km', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.65,0.05055250296034366,0.6242459805756857,0.05386430053480397,0.6130128449604323,0.6754846501480443,0.7058333333333333,0.05583333333333329,success
svm,Support Vector Machine,13,"['total_title_diversity', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'strava_unique_words', 'avg_distance_km', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.65,0.05962847939999438,0.6223242045660032,0.0620510804868598,0.6122929961389891,0.6673437743493844,0.7108333333333333,0.060833333333333295,success
svm,Support Vector Machine,15,"['total_title_diversity', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.64,0.060184900284225955,0.6126965618550358,0.057014497843119065,0.6034343024283348,0.6601932367149759,0.7091666666666667,0.06916666666666671,success
svm,Support Vector Machine,18,"['total_title_diversity', 'total_time_minutes', 'achievement_rate', 'activity_points', 'title_balance_ratio', 'productivity_points', 'activity_days', 'pomokit_title_count', 'strava_unique_words', 'strava_title_count', 'avg_distance_km', 'avg_time_minutes', 'gamification_balance', 'total_cycles', 'consistency_score', 'total_distance_km', 'pomokit_unique_words', 'work_days']",0.64,0.0646357314322177,0.620656188525899,0.06576705985297739,0.6098080901196449,0.6601932367149759,0.7125000000000001,0.07250000000000012,success
