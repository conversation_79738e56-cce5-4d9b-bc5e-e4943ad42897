"""
Comprehensive SHAP Ablation Study Pipeline for Fatigue Prediction
Uses SHAP_ABLATION_STUDY for deep interpretability analysis with true Shapley values
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from title_only_fatigue_classifier import TitleOnlyFatigueClassifier
from feature_filter3 import FeatureFilter3
from shap_ablation_study import SHAPAblationStudy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_shap_analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ComprehensiveSHAPAnalysisPipeline:
    """
    Complete pipeline for comprehensive SHAP ablation study
    """
    
    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        
        # Initialize components
        self.data_processor = DataProcessor()
        self.visualizer = Visualizer()
        self.title_classifier = TitleOnlyFatigueClassifier()
        
        logger.info("🔍 Comprehensive SHAP Analysis Pipeline initialized")
        logger.info(f"   • ML Analysis: {'Enabled' if include_ml else 'Disabled'}")
        logger.info(f"   • SHAP Method: True Shapley Values with multiple explainers")
    
    def run_data_processing(self):
        """Run data processing phase"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING")
        logger.info("="*60)
        
        try:
            # Use the complete data processing pipeline
            logger.info("Running complete data processing pipeline...")
            processed_data = self.data_processor.process_all()
            logger.info(f"✅ Data processing completed: {len(processed_data)} records")
            
            # Save processed data
            output_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            processed_data.to_csv(output_path, index=False)
            logger.info(f"✅ Processed data saved: {output_path}")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ Data processing failed: {str(e)}")
            logger.info("Trying alternative approach...")
            
            # Alternative: Load raw data and process step by step
            try:
                # Step 1: Load raw data
                logger.info("Step 1: Loading raw data...")
                strava_data, pomokit_data = self.data_processor.load_raw_data()
                logger.info(f"✅ Raw data loaded: Strava {len(strava_data)}, Pomokit {len(pomokit_data)} records")
                
                # Step 2: Clean data
                logger.info("Step 2: Cleaning data...")
                clean_strava = self.data_processor.clean_strava_data(strava_data)
                clean_pomokit = self.data_processor.clean_pomokit_data(pomokit_data)
                logger.info(f"✅ Data cleaned: Strava {len(clean_strava)}, Pomokit {len(clean_pomokit)} records")
                
                # Step 3: Create weekly aggregation
                logger.info("Step 3: Creating weekly aggregation...")
                weekly_data = self.data_processor.create_weekly_aggregation(clean_strava, clean_pomokit)
                logger.info(f"✅ Weekly aggregation created: {len(weekly_data)} records")
                
                # Step 4: Add gamification variables
                logger.info("Step 4: Adding gamification variables...")
                processed_data = self.data_processor.add_gamification_variables(weekly_data)
                logger.info(f"✅ Gamification variables added: {len(processed_data)} records")
                
                # Save processed data
                output_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
                processed_data.to_csv(output_path, index=False)
                logger.info(f"✅ Processed data saved: {output_path}")
                
                return processed_data
                
            except Exception as e2:
                logger.error(f"❌ Alternative processing also failed: {str(e2)}")
                raise
    
    def run_comprehensive_shap_analysis(self, processed_data=None):
        """Run comprehensive SHAP analysis with true Shapley values"""
        logger.info("="*60)
        logger.info("PHASE 2: COMPREHENSIVE SHAP ANALYSIS WITH TRUE SHAPLEY VALUES")
        logger.info("="*60)
        
        # Step 1: Title-Only Classification
        logger.info("Step 1: Running title-only fatigue classification...")
        classified_data = self.title_classifier.process_title_only_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )
        report = self.title_classifier.generate_title_classification_report(classified_data)
        
        # Save classified data
        classified_output = "dataset/processed/fatigue_classified_with_title_only.csv"
        classified_data.to_csv(classified_output, index=False)
        logger.info(f"✅ Title-only classification completed: {classified_output}")
        
        # Step 2: Create safe dataset for SHAP analysis
        logger.info("Step 2: Creating safe dataset for SHAP analysis...")
        feature_filter = FeatureFilter3()
        safe_output = "dataset/processed/safe_ml_title_only_dataset.csv"
        
        safe_path = feature_filter.create_safe_dataset_for_title_only_classifier(
            classified_output,
            safe_output,
            target_column='title_fatigue_risk'
        )
        logger.info(f"✅ Safe dataset created: {safe_path}")
        
        # Step 3: Comprehensive SHAP Ablation Study
        logger.info("Step 3: Running comprehensive SHAP ablation study...")
        logger.info("🔍 Using TRUE SHAP VALUES with multiple explainer types:")
        logger.info("   • SHAP TreeExplainer for Random Forest & Gradient Boosting")
        logger.info("   • SHAP LinearExplainer for Logistic Regression")
        logger.info("   • SHAP KernelExplainer for model-agnostic analysis")
        
        # Initialize SHAP Ablation Study
        shap_study = SHAPAblationStudy(
            data_path=safe_output,
            target_column='title_fatigue_risk',
            random_state=42
        )
        
        # Run comprehensive SHAP study
        logger.info("🚀 Starting comprehensive SHAP ablation study...")
        logger.info("⚠️  Note: SHAP analysis may take several minutes due to Shapley value calculations")
        
        shap_results = shap_study.run_complete_shap_study()
        
        # Save SHAP results
        results_file, report_file = shap_study.save_results(
            shap_results, 
            prefix="comprehensive_title_only"
        )
        
        logger.info(f"✅ Comprehensive SHAP analysis completed!")
        logger.info(f"📊 Results saved:")
        logger.info(f"   • SHAP Results: {results_file}")
        logger.info(f"   • SHAP Report: {report_file}")
        
        return {
            'classification_summary': report,
            'safe_dataset_path': safe_output,
            'shap_results': shap_results,
            'analysis_type': 'Comprehensive SHAP with True Shapley Values'
        }
    
    def run_complete_pipeline(self):
        """Run complete pipeline with data processing and SHAP analysis"""
        logger.info("🚀 Starting Complete Comprehensive SHAP Analysis Pipeline...")
        
        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()
            
            # Phase 2: Comprehensive SHAP Analysis (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_comprehensive_shap_analysis(processed_data)
            
            logger.info("="*80)
            logger.info("🎉 COMPREHENSIVE SHAP ANALYSIS PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Comprehensive SHAP Analysis Pipeline')
    parser.add_argument('--shap-only', action='store_true', 
                       help='Run only SHAP analysis on existing safe dataset')
    parser.add_argument('--no-ml', action='store_true', 
                       help='Skip SHAP analysis, only process data')
    
    args = parser.parse_args()
    
    try:
        if args.shap_only:
            # Run only SHAP analysis on existing safe dataset
            print("🔍 Running Comprehensive SHAP Analysis on Existing Safe Dataset...")
            pipeline = ComprehensiveSHAPAnalysisPipeline(include_ml=True)
            results = pipeline.run_comprehensive_shap_analysis()
            print(f"\n🎉 Comprehensive SHAP Analysis Completed!")
        else:
            # Run complete pipeline (default)
            include_ml = not args.no_ml
            pipeline = ComprehensiveSHAPAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()
    
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
