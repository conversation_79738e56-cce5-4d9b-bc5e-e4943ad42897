"""
Comprehensive SHAP Ablation Study Pipeline for Fatigue Prediction
Uses SHAP_ABLATION_STUDY for deep interpretability analysis with true Shapley values
"""

import logging
import sys
import argparse
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from title_only_fatigue_classifier import TitleOnlyFatigueClassifier
from feature_filter3 import FeatureFilter3
from shap_ablation_study import SHAPAblationStudy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main8_new.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ComprehensiveSHAPAnalysisPipeline:
    """
    Complete pipeline for comprehensive SHAP ablation study
    """
    
    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        
        # Initialize components
        self.data_processor = DataProcessor()
        self.visualizer = Visualizer()
        self.title_classifier = TitleOnlyFatigueClassifier()
        
        logger.info("🔍 NEW Comprehensive SHAP Analysis Pipeline initialized")
        logger.info(f"   • ML Analysis: {'Enabled' if include_ml else 'Disabled'}")
        logger.info(f"   • SHAP Method: True Shapley Values with multiple explainers")
    
    def run_data_processing(self):
        """Run data processing phase"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING (NEW VERSION)")
        logger.info("="*60)
        
        try:
            # Use the complete data processing pipeline
            logger.info("Running complete data processing pipeline...")
            processed_data = self.data_processor.process_all()
            logger.info(f"✅ Data processing completed: {len(processed_data)} records")
            
            # Save processed data
            output_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            processed_data.to_csv(output_path, index=False)
            logger.info(f"✅ Processed data saved: {output_path}")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ Data processing failed: {str(e)}")
            logger.info("Trying alternative approach...")
            
            # Alternative: Load raw data and process step by step
            try:
                # Step 1: Load raw data
                logger.info("Step 1: Loading raw data...")
                strava_data, pomokit_data = self.data_processor.load_raw_data()
                logger.info(f"✅ Raw data loaded: Strava {len(strava_data)}, Pomokit {len(pomokit_data)} records")
                
                # Step 2: Clean data
                logger.info("Step 2: Cleaning data...")
                clean_strava = self.data_processor.clean_strava_data(strava_data)
                clean_pomokit = self.data_processor.clean_pomokit_data(pomokit_data)
                logger.info(f"✅ Data cleaned: Strava {len(clean_strava)}, Pomokit {len(clean_pomokit)} records")
                
                # Step 3: Create weekly aggregation
                logger.info("Step 3: Creating weekly aggregation...")
                weekly_data = self.data_processor.create_weekly_aggregation(clean_strava, clean_pomokit)
                logger.info(f"✅ Weekly aggregation created: {len(weekly_data)} records")
                
                # Step 4: Add gamification variables
                logger.info("Step 4: Adding gamification variables...")
                processed_data = self.data_processor.add_gamification_variables(weekly_data)
                logger.info(f"✅ Gamification variables added: {len(processed_data)} records")
                
                # Save processed data
                output_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
                processed_data.to_csv(output_path, index=False)
                logger.info(f"✅ Processed data saved: {output_path}")
                
                return processed_data
                
            except Exception as e2:
                logger.error(f"❌ Alternative processing also failed: {str(e2)}")
                raise
    
    def run_complete_pipeline(self):
        """Run complete pipeline with data processing and SHAP analysis"""
        logger.info("🚀 Starting NEW Complete Comprehensive SHAP Analysis Pipeline...")
        
        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()
            
            logger.info("="*80)
            logger.info("🎉 NEW COMPREHENSIVE SHAP ANALYSIS PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {
                'processed_data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='NEW Comprehensive SHAP Analysis Pipeline')
    parser.add_argument('--no-ml', action='store_true', 
                       help='Skip SHAP analysis, only process data')
    
    args = parser.parse_args()
    
    try:
        # Run complete pipeline (default)
        include_ml = not args.no_ml
        pipeline = ComprehensiveSHAPAnalysisPipeline(include_ml=include_ml)
        pipeline.run_complete_pipeline()
    
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
