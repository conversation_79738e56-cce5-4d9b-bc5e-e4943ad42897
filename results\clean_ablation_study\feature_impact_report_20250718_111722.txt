================================================================================
📊 FEATURE IMPACT ANALYSIS REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset\processed\safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Samples: N/A
   • Total Features: 20

📝 ALL FEATURES USED IN ANALYSIS:
    1. achievement_rate
    2. activity_days
    3. activity_points
    4. avg_distance_km
    5. avg_time_minutes
    6. consistency_score
    7. gamification_balance
    8. pomokit_title_count
    9. pomokit_title_length
   10. pomokit_unique_words
   11. productivity_points
   12. strava_title_count
   13. strava_title_length
   14. strava_unique_words
   15. title_balance_ratio
   16. total_cycles
   17. total_distance_km
   18. total_time_minutes
   19. total_title_diversity
   20. work_days

✅ RECOMMENDED FEATURES TO KEEP (2):
   • title_balance_ratio: -3.00% impact - 🟢 Netral
   • consistency_score: -3.00% impact - 🟢 Netral

❌ RECOMMENDED FEATURES TO REMOVE (1):
   • avg_distance_km: +2.00% impact - 🟢 Netral

🟢 NEUTRAL FEATURES (Optional - 19):
   • title_balance_ratio: -3.00% impact - 🟢 Netral
   • consistency_score: -3.00% impact - 🟢 Netral
   • gamification_balance: -1.67% impact - 🟢 Netral
   • pomokit_title_length: -0.67% impact - 🟢 Netral
   • activity_days: +0.00% impact - 🟢 Netral
   • strava_title_count: +0.00% impact - 🟢 Netral
   • total_cycles: +0.00% impact - 🟢 Netral
   • total_title_diversity: +0.00% impact - 🟢 Netral
   • pomokit_title_count: +0.00% impact - 🟢 Netral
   • achievement_rate: +0.00% impact - 🟢 Netral
   • pomokit_unique_words: +0.00% impact - 🟢 Netral
   • work_days: +0.00% impact - 🟢 Netral
   • productivity_points: +0.33% impact - 🟢 Netral
   • total_time_minutes: +0.33% impact - 🟢 Netral
   • strava_unique_words: +0.33% impact - 🟢 Netral
   • activity_points: +0.33% impact - 🟢 Netral
   • strava_title_length: +0.67% impact - 🟢 Netral
   • avg_time_minutes: +0.67% impact - 🟢 Netral
   • total_distance_km: +1.00% impact - 🟢 Netral

📈 BASELINE MODEL PERFORMANCE:
   • Baseline Accuracy: 0.6700 (67.00%)
   • Total Features: 20

🎯 FEATURE CATEGORIES:
   • 🔴 Critical Features (hurt when removed): 0
   • ⚠️ Noise Features (help when removed): 1
   • 🟢 Neutral Features: 19

🏆 TOP 5 MOST IMPORTANT FEATURES:
   1. title_balance_ratio
      • Impact when removed: -3.00%
      • Accuracy without: 0.6400
      • Status: 🟢 Netral
   2. consistency_score
      • Impact when removed: -3.00%
      • Accuracy without: 0.6400
      • Status: 🟢 Netral
   3. gamification_balance
      • Impact when removed: -1.67%
      • Accuracy without: 0.6533
      • Status: 🟢 Netral
   4. pomokit_title_length
      • Impact when removed: -0.67%
      • Accuracy without: 0.6633
      • Status: 🟢 Netral
   5. activity_days
      • Impact when removed: +0.00%
      • Accuracy without: 0.6700
      • Status: 🟢 Netral

⚠️ POTENTIAL NOISE FEATURES (Bottom 5):
   16. activity_points
      • Impact when removed: +0.33%
      • Accuracy without: 0.6733
      • Status: 🟢 Netral
   17. strava_title_length
      • Impact when removed: +0.67%
      • Accuracy without: 0.6767
      • Status: 🟢 Netral
   18. avg_time_minutes
      • Impact when removed: +0.67%
      • Accuracy without: 0.6767
      • Status: 🟢 Netral
   19. total_distance_km
      • Impact when removed: +1.00%
      • Accuracy without: 0.6800
      • Status: 🟢 Netral
   20. avg_distance_km
      • Impact when removed: +2.00%
      • Accuracy without: 0.6900
      • Status: 🟢 Netral

💡 RECOMMENDATIONS:
   • Consider removing 1 noise features:
     - avg_distance_km (improves by +2.00%)

🎯 OPTIMAL FEATURE SET (12 features):
    1. achievement_rate (+0.00%)
    2. activity_days (+0.00%)
    3. consistency_score (-3.00%)
    4. gamification_balance (-1.67%)
    5. pomokit_title_count (+0.00%)
    6. pomokit_title_length (-0.67%)
    7. pomokit_unique_words (+0.00%)
    8. strava_title_count (+0.00%)
    9. title_balance_ratio (-3.00%)
   10. total_cycles (+0.00%)
   11. total_title_diversity (+0.00%)
   12. work_days (+0.00%)

🚫 FEATURES EXCLUDED FROM OPTIMAL SET (8):
   • activity_points (+0.33%) - Low Impact
   • avg_distance_km (+2.00%) - Noise
   • avg_time_minutes (+0.67%) - Low Impact
   • productivity_points (+0.33%) - Low Impact
   • strava_title_length (+0.67%) - Low Impact
   • strava_unique_words (+0.33%) - Low Impact
   • total_distance_km (+1.00%) - Low Impact
   • total_time_minutes (+0.33%) - Low Impact

🚀 IMPROVEMENT POTENTIAL:
   • Current Accuracy: 0.6700 (67.00%)
   • Potential Accuracy: 0.6900 (69.00%)
   • Potential Improvement: +2.00%

📊 FEATURE SELECTION SUMMARY:
   • Original Features: 20
   • Optimal Features: 12
   • Features Removed: 8
   • Reduction: 40.0%

💻 IMPLEMENTATION GUIDE:
   1. Remove 1 noise features for immediate improvement
   2. Use optimal feature set with 12 features
   3. Expected accuracy improvement: +2.00%
   4. Model complexity reduction: 40.0%
================================================================================