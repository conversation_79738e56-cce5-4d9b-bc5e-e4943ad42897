================================================================================
🔍 SIMPLE SHAP-STYLE ANALYSIS REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/conservative_safe_fatigue_dataset.csv
   • Target Column: external_fatigue_risk
   • Total Features: 21
   • Total Samples: 300
   • Target Distribution: {'medium_risk': 147, 'low_risk': 77, 'high_risk': 76}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Accuracy: 0.5667
     - F1-Score: 0.5254
   • Random Forest:
     - Accuracy: 0.5667
     - F1-Score: 0.5456

🎯 FEATURE IMPORTANCE (Permutation-based):

   📊 Logistic Regression:
      1. 🔥 external_label_confidence: 0.1350
      2. 🔥 pomokit_unique_words: 0.0883
      3. ⭐ strava_title_count: 0.0483
      4. ▫️ strava_unique_words: -0.0350
      5. ⭐ total_cycles: 0.0267
      6. ⭐ pomokit_title_count: 0.0267
      7. ⭐ activity_points: 0.0233
      8. ⭐ title_balance_ratio: 0.0217
      9. ▫️ total_time_minutes: -0.0200
     10. ▫️ total_title_diversity: -0.0200

   📊 Random Forest:
      1. 🔥 external_label_confidence: 0.1517
      2. ▫️ avg_distance_km: -0.0500
      3. ▫️ strava_title_length: -0.0417
      4. ▫️ pomokit_title_length: -0.0300
      5. ▫️ productivity_points: -0.0200
      6. ▫️ total_distance_km: -0.0167
      7. ▫️ total_title_diversity: -0.0167
      8. 🔸 avg_time_minutes: 0.0167
      9. ▫️ strava_title_count: -0.0150
     10. ▫️ strava_unique_words: -0.0117

📊 ANALYSIS SUMMARY:
   • Total models analyzed: 2
   • Total features analyzed: 21
   • Best performing model: Logistic Regression
   • Best accuracy: 0.5667
   • Analysis timestamp: 2025-07-19T19:54:08.513160