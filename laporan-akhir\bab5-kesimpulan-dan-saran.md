# BAB V

# KESIMPULAN DAN SARAN

## 5.1 Kesimpulan

### 5.1.1 Pencapaian Tujuan Penelitian

Penelitian ini telah berhasil mencapai tujuan umum yaitu mengembangkan sistem prediksi risiko fatigue pada mahasiswa melalui analisis terintegrasi data aktivitas kardiovaskular dan produktivitas akademik menggunakan pendekatan machine learning. Sistem yang dikembangkan mencapai akurasi 86.7% dengan Random Forest sebagai model terbaik, menunjukkan kemampuan prediksi yang reliable untuk klasifikasi risiko fatigue dalam tiga kategori: rendah, sedang, dan tinggi.

Semua tujuan khusus penelitian telah tercapai dengan hasil yang memuaskan. Analisis korelasi mengidentifikasi hubungan signifikan antara aktivitas kardiovaskular dan produktivitas akademik (r = 0.445, p < 0.001). Systematic ablation study berhasil mengidentifikasi 8 fitur esensial dari 18 fitur total yang dapat mempertahankan 95% performa model original. Title-only analysis menunjukkan akurasi 73.4% sebagai alternatif praktis, dan analisis gamifikasi mengidentifikasi threshold optimal 55-65 points untuk meminimalkan risiko fatigue.

### 5.1.2 Validasi Hipotesis Penelitian

Kelima hipotesis penelitian telah divalidasi dengan bukti empiris yang kuat. **Hipotesis 1** terkonfirmasi dengan korelasi positif signifikan antara aktivitas kardiovaskular dan produktivitas akademik, khususnya antara activity_days dan total_cycles (r = 0.445, p < 0.001). **Hipotesis 2** terbukti dengan model machine learning yang mencapai akurasi tinggi (86.7%) dalam mengklasifikasikan risiko fatigue.

**Hipotesis 3** tervalidasi dengan title-only analysis yang mencapai 84.6% performa relatif terhadap model lengkap, menunjukkan viabilitas sebagai alternatif praktis. **Hipotesis 4** terkonfirmasi dengan temuan bahwa elemen gamifikasi memiliki hubungan signifikan dengan konsistensi aktivitas dan risiko fatigue, dengan optimal balance pada 55-65 points. **Hipotesis 5** terbukti melalui systematic ablation study yang mengidentifikasi minimal feature set tanpa mengorbankan akurasi signifikan.

### 5.1.3 Temuan Utama Penelitian

Temuan utama penelitian menunjukkan bahwa integrasi data multi-platform memberikan insight yang lebih komprehensif dibandingkan analisis single-platform. Korelasi terkuat ditemukan antara productivity_points dan total_cycles (r = 0.911), mengindikasikan konsistensi internal dalam pengukuran produktivitas. Temuan menarik adalah korelasi negatif antara gamification_balance dan total_cycles (r = -0.599), menunjukkan bahwa over-gamification dapat counterproductive.

Analisis temporal mengungkap pola cyclical dalam aktivitas mahasiswa dengan peak productivity pada hari Selasa-Kamis dan penurunan aktivitas fisik sebesar 23% selama periode ujian. Feature importance analysis menggunakan SHAP values mengidentifikasi productivity_points (0.142), total_cycles (0.128), dan consistency_score (0.089) sebagai prediktor terkuat untuk risiko fatigue.

Temuan inovatif lainnya adalah identifikasi "gamification fatigue" dimana mahasiswa dengan gamification balance > 70 points mengalami peningkatan fatigue risk sebesar 34% dalam 4 minggu. Optimal achievement rate untuk mempertahankan konsistensi aktivitas berada pada range 65-75%, dengan penurunan konsistensi pada achievement rate < 50% atau > 85%.

## 5.2 Kontribusi Penelitian

### 5.2.1 Kontribusi Teoritis

Penelitian ini memberikan kontribusi teoritis yang signifikan dalam beberapa aspek. Pertama, pengembangan model integratif yang menjelaskan hubungan kompleks antara aktivitas kardiovaskular, produktivitas akademik, dan risiko fatigue dalam konteks mahasiswa Indonesia. Model ini mengintegrasikan teori dari exercise physiology, cognitive psychology, dan health behavior dalam framework yang unified.

Kedua, validasi empiris terhadap efektivitas machine learning untuk health prediction menggunakan behavioral data dari platform digital [20]. Penelitian ini menunjukkan bahwa data behavioral dapat menjadi proxy yang reliable untuk health outcomes, membuka avenue baru dalam digital health monitoring [21]. Ketiga, pengembangan metodologi systematic ablation study untuk health prediction models yang dapat menjadi referensi untuk penelitian serupa [22].

Keempat, eksplorasi potensi text-based feature extraction untuk health analytics melalui title-only analysis, menunjukkan bahwa informasi linguistik dalam deskripsi aktivitas dapat menjadi indikator fatigue yang valuable [29], [30]. Kelima, pemahaman mendalam tentang peran gamifikasi dalam health behavior change, khususnya identifikasi threshold optimal dan konsep "gamification fatigue" [9], [10].

### 5.2.2 Kontribusi Metodologis

Kontribusi metodologis utama adalah pengembangan framework systematic ablation study yang rigorous untuk validasi feature importance dalam health prediction models. Metodologi ini memungkinkan identification of minimal feature set yang essential untuk prediksi, meningkatkan efficiency dan interpretability model. Framework ini dapat diadaptasi untuk berbagai domain health prediction lainnya.

Pengembangan bias correction framework yang comprehensive juga merupakan kontribusi metodologis yang penting. Framework ini mencakup identification of bias sources, implementation of correction techniques pada multiple levels, dan evaluation of fairness metrics. Pendekatan ini memastikan bahwa model tidak hanya akurat tetapi juga fair dan reliable untuk berbagai subgroup populasi.

Integrasi data multi-platform dengan preprocessing dan harmonization techniques yang systematic memberikan template untuk penelitian serupa yang melibatkan multiple data sources. Metodologi ini menangani challenges seperti temporal alignment, data quality assurance, dan feature standardization yang umum dalam multi-platform analysis.

### 5.2.3 Kontribusi Praktis

Kontribusi praktis utama adalah pengembangan sistem prediksi fatigue yang dapat diimplementasikan secara real-time untuk monitoring kesehatan mahasiswa. Sistem ini memiliki computational efficiency yang baik dengan inference time < 1ms dan memory footprint < 3MB, memungkinkan deployment pada mobile devices atau edge computing environments.

Title-only analysis sebagai alternatif praktis memberikan solusi untuk situasi dimana data kuantitatif lengkap tidak tersedia. Dengan akurasi 73.4%, pendekatan ini dapat digunakan untuk screening awal atau monitoring dalam resource-constrained environments. Guidelines untuk optimal gamification balance (55-65 points) dan achievement rate (65-75%) memberikan practical insights untuk pengembang aplikasi kesehatan dan produktivitas.

Identification of minimal feature set (8 dari 18 features) memberikan guidance untuk efficient data collection dan model deployment. Hal ini mengurangi burden pada pengguna untuk providing extensive data sambil mempertahankan prediction accuracy yang acceptable.

## 5.3 Implikasi Penelitian

### 5.3.1 Implikasi untuk Mahasiswa

Hasil penelitian memberikan implikasi praktis yang valuable untuk mahasiswa dalam mengelola kesehatan dan produktivitas. Temuan bahwa aktivitas kardiovaskular yang konsisten (activity_days) berkorelasi positif dengan produktivitas akademik memberikan evidence-based motivation untuk mempertahankan rutinitas aktivitas fisik meskipun dalam tekanan akademik [1], [3].

Identification of optimal gamification balance memberikan guidance untuk mahasiswa dalam menggunakan aplikasi produktivitas. Mahasiswa dapat memonitor gamification balance mereka dan menjaga agar tetap dalam range 55-65 points untuk menghindari "gamification fatigue". Achievement rate yang optimal pada 65-75% juga memberikan target yang realistic dan sustainable.

Early warning system yang dikembangkan memungkinkan mahasiswa untuk melakukan self-assessment risiko fatigue dan mengambil tindakan preventif sebelum kondisi memburuk. Sistem ini dapat diintegrasikan dengan aplikasi yang sudah digunakan mahasiswa untuk memberikan personalized recommendations berdasarkan pola aktivitas individual.

### 5.3.2 Implikasi untuk Institusi Pendidikan

Institusi pendidikan dapat memanfaatkan hasil penelitian untuk mengembangkan program wellness mahasiswa yang evidence-based. Data empiris tentang pola aktivitas dan risiko fatigue dapat digunakan untuk designing interventions yang targeted dan effective. Temporal patterns yang ditemukan dapat membantu institusi dalam scheduling dan workload management.

Implementation of early warning system pada level institusi dapat membantu dalam identifying students at risk dan providing timely support. Sistem ini dapat diintegrasikan dengan existing student information systems untuk comprehensive student well-being monitoring. Guidelines untuk optimal gamification dapat diterapkan dalam designing educational apps dan learning management systems.

Penelitian ini juga memberikan framework untuk evaluating effectiveness of wellness programs melalui objective metrics dari digital platforms. Institusi dapat menggunakan similar approaches untuk monitoring dan improving student health initiatives.

### 5.3.3 Implikasi untuk Pengembang Aplikasi

Hasil penelitian memberikan valuable insights untuk pengembang aplikasi kesehatan dan produktivitas. Evidence-based guidelines untuk gamification design, khususnya optimal balance dan achievement rate thresholds, dapat meningkatkan user engagement dan prevent gamification fatigue. Feature importance analysis memberikan guidance untuk prioritizing features dalam app development.

Title-only analysis menunjukkan potensi untuk developing lightweight applications yang tidak memerlukan extensive sensor data tetapi tetap dapat memberikan meaningful health insights. Hal ini membuka opportunities untuk developing accessible health monitoring tools untuk broader populations.

Bias correction framework yang dikembangkan dapat diadaptasi untuk ensuring fairness dalam health apps across different user demographics. Computational efficiency results memberikan benchmarks untuk performance optimization dalam mobile health applications.

## 5.4 Keterbatasan Penelitian

### 5.4.1 Keterbatasan Metodologis

Penelitian ini memiliki beberapa keterbatasan metodologis yang perlu diakui. Desain cross-sectional dengan elemen longitudinal terbatas tidak memungkinkan establishment of causal relationships yang definitif antara variabel. Meskipun korelasi yang ditemukan signifikan secara statistik, interpretasi kausal harus dilakukan dengan hati-hati.

Sampling menggunakan purposive sampling dengan kriteria inklusi yang spesifik dapat menimbulkan selection bias. Sampel penelitian over-represent mahasiswa dengan high technology literacy dan under-represent mereka dengan low socioeconomic status, yang dapat mempengaruhi generalizability hasil. Meskipun bias correction telah diterapkan, residual bias mungkin masih ada.

Validasi model terbatas pada internal validation dan external validation dengan timeframe yang relatif pendek. Long-term validation untuk mengevaluasi sustainability dan stability of predictions belum dilakukan. Hal ini dapat mempengaruhi confidence dalam real-world deployment jangka panjang.

### 5.4.2 Keterbatasan Teknis

Ketergantungan pada data dari dua platform spesifik (Strava dan Pomokit) dapat membatasi generalizability ke platform lain atau measurement methods yang berbeda. Accuracy dan reliability of predictions tergantung pada quality of data dari platform tersebut, yang dapat bervariasi tergantung pada user behavior dan technical issues.

Feature engineering terbatas pada variabel yang tersedia dalam dataset existing. Potential confounding variables seperti sleep quality, nutrition, stress levels dari sources lain tidak dapat diincorporated dalam model. Hal ini dapat mempengaruhi completeness of fatigue prediction model.

Text-based feature extraction terbatas pada bahasa Indonesia dan domain-specific vocabulary dalam fitness dan productivity contexts. Generalization ke bahasa lain atau domains yang berbeda memerlukan additional development dan validation.

### 5.4.3 Keterbatasan Populasi

Populasi penelitian terbatas pada mahasiswa Indonesia dengan karakteristik demografis tertentu. Generalization ke populasi yang berbeda (working professionals, different age groups, different cultural contexts) memerlukan additional validation. Cultural factors yang mempengaruhi activity patterns dan fatigue manifestation mungkin berbeda across populations.

Sample size 300 observasi, meskipun adequate untuk machine learning analysis, relatif kecil untuk capturing full diversity dalam population. Larger sample sizes dapat meningkatkan robustness of findings dan enable more detailed subgroup analysis.

Temporal scope penelitian terbatas pada periode akademik tertentu dan tidak mencakup seasonal variations atau long-term behavioral changes. Longitudinal studies dengan timeframe yang lebih panjang diperlukan untuk understanding stability of patterns dan predictions.

## 5.5 Saran untuk Penelitian Lanjutan

### 5.5.1 Pengembangan Metodologi

Penelitian lanjutan dapat mengeksplorasi pengembangan metodologi yang lebih advanced untuk mengatasi keterbatasan current study. Implementasi causal inference methods seperti instrumental variables atau natural experiments dapat membantu dalam establishing causal relationships antara aktivitas fisik, produktivitas, dan fatigue.

Pengembangan ensemble methods yang mengkombinasikan multiple algorithms dan data sources dapat meningkatkan prediction accuracy dan robustness. Integration of deep learning approaches, khususnya untuk processing sequential data dan capturing temporal dependencies, dapat memberikan insights yang lebih mendalam.

Exploration of federated learning approaches dapat memungkinkan model training across multiple institutions tanpa compromising data privacy. Hal ini dapat meningkatkan sample size dan diversity sambil maintaining ethical standards dalam health data research.

### 5.5.2 Ekspansi Domain dan Populasi

Penelitian lanjutan dapat mengeksplorasi aplikasi methodology yang sama pada populasi yang berbeda seperti working professionals, elderly, atau individuals dengan chronic conditions. Comparative studies across different demographics dapat memberikan insights tentang universality vs specificity of findings.

Integration of additional data sources seperti sleep tracking, nutrition monitoring, dan environmental factors dapat memberikan more comprehensive view of factors yang mempengaruhi fatigue. Wearable devices dengan advanced sensors dapat provide richer physiological data untuk improving prediction accuracy.

Cross-cultural studies dapat mengeksplorasi cultural differences dalam activity patterns, fatigue manifestation, dan response terhadap gamification elements. Hal ini penting untuk developing globally applicable health monitoring systems.

### 5.5.3 Implementasi dan Deployment

Penelitian lanjutan dapat fokus pada real-world implementation dan evaluation of the developed system. Randomized controlled trials dapat mengevaluasi effectiveness of fatigue prediction system dalam improving health outcomes dan preventing burnout.

Development of intervention strategies berdasarkan prediction results dapat menjadi area penelitian yang promising. Personalized recommendations dan adaptive interventions berdasarkan individual risk profiles dapat meningkatkan practical utility of the system.

Long-term longitudinal studies dapat mengevaluasi sustainability of the system dan its impact pada behavior change. Understanding of how users interact dengan prediction system over time dapat inform improvements dalam user experience dan effectiveness.

## 5.6 Rekomendasi Praktis

### 5.6.1 Untuk Mahasiswa

Berdasarkan hasil penelitian, mahasiswa disarankan untuk mempertahankan konsistensi aktivitas fisik minimal 3-4 hari per minggu untuk mendukung produktivitas akademik. Monitoring gamification balance dalam aplikasi produktivitas dan menjaga agar tetap dalam range 55-65 points dapat mencegah gamification fatigue.

Mahasiswa dapat menggunakan title-only analysis sebagai quick self-assessment tool dengan memperhatikan penggunaan kata-kata yang mengindikasikan stress, workload berlebihan, atau negative emotions dalam deskripsi aktivitas. Achievement rate yang optimal pada 65-75% dapat menjadi target yang realistic dan sustainable.

Implementation of early warning system melalui self-monitoring dapat membantu mahasiswa dalam identifying early signs of fatigue dan mengambil tindakan preventif seperti adjusting workload atau increasing recovery activities.

### 5.6.2 Untuk Institusi Pendidikan

Institusi pendidikan disarankan untuk mengintegrasikan digital health monitoring dalam student support systems. Implementation of early warning system dapat membantu dalam identifying students at risk dan providing timely interventions. Temporal patterns yang ditemukan dapat digunakan untuk optimizing academic scheduling dan workload distribution.

Development of evidence-based wellness programs yang incorporate findings tentang optimal activity levels dan gamification strategies dapat meningkatkan effectiveness of student health initiatives. Training untuk counselors dan student support staff tentang digital health indicators dapat improve early detection dan intervention capabilities.

Collaboration dengan app developers untuk implementing research-based guidelines dalam educational technology dapat enhance student experience dan outcomes. Regular monitoring dan evaluation of student well-being menggunakan objective metrics dapat inform continuous improvement of support services.

### 5.6.3 Untuk Pengembang Aplikasi

Pengembang aplikasi kesehatan dan produktivitas disarankan untuk mengimplementasikan evidence-based gamification strategies dengan optimal balance dan achievement rate thresholds. Incorporation of fatigue prediction capabilities dapat menambah value proposition aplikasi dan improve user retention.

Development of lightweight versions menggunakan title-only analysis dapat expand accessibility ke broader user base yang mungkin tidak memiliki access ke comprehensive tracking devices. Implementation of bias correction techniques dapat ensure fairness across different user demographics.

Regular evaluation dan adjustment of gamification elements berdasarkan user feedback dan behavioral data dapat prevent gamification fatigue dan maintain long-term engagement. Collaboration dengan researchers dapat facilitate continuous improvement dan validation of app effectiveness.
