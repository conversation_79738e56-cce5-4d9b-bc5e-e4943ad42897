"""
Complete Analysis Pipeline for Bias Corrected Title Classifier
Combines Physical Activity Analysis + Bias Corrected Fatigue Prediction with Feature Filtering & Model Accuracy

DESIGNED FOR BIAS_CORRECTED_TITLE_CLASSIFIER.PY:
- Integrates bias_corrected_title_classifier.py with feature_filter2.py
- <PERSON><PERSON> bias correction and context features
- Prevents data leakage from bias correction process
- Provides safe datasets for training
- Supports multiple pipeline modes

PIPELINE MODES:
1. Complete Pipeline (default): Data Processing + Bias Corrected Fatigue Prediction + ML
2. Bias Corrected Only (--bias-corrected-only): Bias Corrected Classification + Feature Filtering + ML
3. No ML (--no-ml): Data Processing only
4. ML Only (--ml-only): Advanced ML pipeline only

BIAS CORRECTION FEATURES:
- Language pattern detection (indonesian_dominant, english_dominant, mixed)
- Activity type detection (physical_dominant, work_dominant, balanced)
- Cultural bias adjustment in scoring
- Context-aware fatigue classification

FEATURE FILTERING:
- Automatically removes bias correction features (corrected_*)
- Removes context features (language_pattern, activity_type)
- Prevents data leakage from bias correction process
- Ensures model reliability and generalization
"""

import logging
import sys
import argparse
from pathlib import Path
# pandas will be imported when needed

# Add src to path
sys.path.append('src')

from data_processor import DataProcessor
from visualizer import Visualizer
from bias_corrected_title_classifier import BiasCorrectedTitleClassifier
from feature_filter2 import FeatureFilter2
from clean_ablation_study import AblationStudy


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class BiasCorrectedAnalysisPipeline:
    """
    Complete analysis pipeline for bias-corrected fatigue prediction
    """

    def __init__(self, include_ml=True):
        """Initialize pipeline components"""
        self.include_ml = include_ml
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()

        if include_ml:
            self.bias_corrected_classifier = BiasCorrectedTitleClassifier()
            self.feature_filter = FeatureFilter2()

        # Ensure output directories exist
        Path("results/reports").mkdir(parents=True, exist_ok=True)
        Path("results/visualizations").mkdir(parents=True, exist_ok=True)
        Path("logs").mkdir(parents=True, exist_ok=True)
    
    def run_data_processing(self):
        """Run data processing only"""
        logger.info("="*60)
        logger.info("PHASE 1: DATA PROCESSING")
        logger.info("="*60)

        # Step 1: Data Processing
        logger.info("Step 1: Processing raw data...")
        processed_data = self.data_processor.process_all()
        logger.info(f"✅ Data processing completed. Shape: {processed_data.shape}")

        return processed_data
    
    def run_bias_corrected_prediction(self, processed_data=None):
        """Run bias corrected fatigue prediction with feature filtering and model accuracy"""
        logger.info("="*60)
        logger.info("PHASE 2: BIAS CORRECTED FATIGUE PREDICTION WITH FEATURE FILTERING & ML MODELS")
        logger.info("="*60)

        # Step 1: Bias Corrected Classification
        logger.info("Step 1: Running bias corrected fatigue classification...")
        classified_data = self.bias_corrected_classifier.process_bias_corrected_classification(
            'dataset/processed/weekly_merged_dataset_with_gamification.csv'
        )

        # Save classified data
        classified_output = "dataset/processed/fatigue_classified.csv"
        classified_data.to_csv(classified_output, index=False)

        summary = self._generate_bias_corrected_summary(classified_data)
        logger.info(f"✅ Bias corrected classification completed. Distribution: {summary['fatigue_percentages']}")

        # Step 2: Feature Filtering for ML Safety (Bias Correction Specific)
        logger.info("Step 2: Applying bias correction feature filtering to prevent data leakage...")
        safe_output = "dataset/processed/safe_ml_bias_corrected_dataset.csv"

        safe_path = self.feature_filter.create_safe_dataset_for_bias_corrected_classifier(
            classified_output,
            safe_output,
            target_column='corrected_fatigue_risk'
        )
        logger.info(f"✅ Bias correction feature filtering completed. Safe dataset: {safe_path}")

        # Step 3: Analyze Bias Correction Impact
        logger.info("Step 3: Analyzing bias correction impact...")
        bias_analysis = self.feature_filter.analyze_bias_correction_impact(classified_data)
        logger.info(f"✅ Bias correction analysis completed. "
                   f"Found {len(bias_analysis['bias_correction_features'])} bias correction features")

        # Step 4: Advanced ML Pipeline with Safe Dataset
        logger.info("Step 4: Running advanced ML pipeline with bias correction safe features...")

        # Use safe dataset for ML training with auto-detection
        # Check which safe datasets are available (prioritize current pipeline's output)
        safe_datasets = [
            {
                'path': safe_output,  # Current pipeline output (highest priority)
                'target': 'corrected_fatigue_risk',
                'name': 'Current Pipeline Output (Bias-Corrected)',
                'priority': 1
            },
            {
                'path': 'dataset/processed/safe_ml_fatigue_dataset.csv',
                'target': 'fatigue_risk',
                'name': 'Regular Fatigue Classification',
                'priority': 2
            },
            {
                'path': 'dataset/processed/safe_ml_title_only_dataset.csv',
                'target': 'title_fatigue_risk',
                'name': 'Title-Only Fatigue Classification',
                'priority': 3
            }
        ]

        # Find the first available safe dataset
        selected_dataset = None
        for dataset in sorted(safe_datasets, key=lambda x: x['priority']):
            if Path(dataset['path']).exists():
                selected_dataset = dataset
                break

        if selected_dataset:
            logger.info(f"📁 Using safe dataset: {selected_dataset['path']}")
            logger.info(f"🎯 Target column: {selected_dataset['target']}")
            logger.info(f"📋 Dataset type: {selected_dataset['name']}")

            logger.info("Step 4a: Running ablation study with bias correction safe features...")
            ablation_study = AblationStudy(
                data_path=selected_dataset['path'],
                target_column=selected_dataset['target'],
                random_state=42
            )
            ablation_study.load_data()
            importance_df, optimal_result = ablation_study.run_complete_ablation_study()

            # Save ablation study results (including Feature Impact Report)
            results_file, importance_file = ablation_study.save_results(importance_df, optimal_result)
            logger.info(f"✅ Ablation study results saved:")
            logger.info(f"   • Results: {results_file}")
            logger.info(f"   • Importance: {importance_file}")
            logger.info(f"   • Feature Impact Report: results/clean_ablation_study/feature_impact_report_*.txt")

            logger.info("Step 4b: Advanced model step skipped (clean_advanced_model removed)")
            # Advanced model functionality removed
            advanced_result = {
                'accuracy_mean': 0.0,
                'f1_mean': 0.0,
                'precision_mean': 0.0,
                'recall_mean': 0.0
            }

            return {
                'classification_summary': summary,
                'bias_correction_analysis': bias_analysis,
                'safe_dataset_path': selected_dataset['path'],
                'safe_dataset_type': selected_dataset['name'],
                'target_column': selected_dataset['target'],
                'ablation_result': optimal_result,
                'advanced_result': advanced_result,
                'best_accuracy': optimal_result['accuracy_mean'],
                'best_model': 'Ablation_Study_Optimal_Bias_Corrected_Safe_Features',
                'data_leakage_prevented': True,
                'bias_correction_applied': True
            }
        else:
            logger.warning("❌ No safe dataset found!")
            logger.warning("Available safe datasets to check:")
            for dataset in safe_datasets:
                status = "✅ EXISTS" if Path(dataset['path']).exists() else "❌ NOT FOUND"
                logger.warning(f"   • {dataset['path']} - {status}")
            logger.warning("Please run the pipeline first to generate safe datasets")
            return {
                'classification_summary': summary,
                'bias_correction_analysis': bias_analysis,
                'safe_dataset_path': None,
                'best_accuracy': None,
                'best_model': 'Bias_Corrected_Classification_Only',
                'data_leakage_prevented': False,
                'bias_correction_applied': True
            }

    def run_bias_corrected_only_pipeline(self):
        """Execute bias corrected classification with feature filtering only"""
        logger.info("🚀 STARTING BIAS CORRECTED FATIGUE CLASSIFICATION PIPELINE")
        logger.info("Includes: Bias Corrected Classification + Feature Filtering + ML Models")
        logger.info("="*80)

        try:
            # Check if processed data exists
            processed_data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
            if not Path(processed_data_path).exists():
                logger.error(f"Processed data not found: {processed_data_path}")
                logger.error("Please run data processing first or use --complete flag")
                return None

            # Load processed data for summary
            import pandas as pd
            processed_data = pd.read_csv(processed_data_path)
            logger.info(f"✅ Loaded processed data: {processed_data.shape}")

            # Run bias corrected prediction with feature filtering
            ml_results = self.run_bias_corrected_prediction(processed_data)

            # Print summary
            self._print_bias_corrected_only_summary(processed_data, ml_results)

            logger.info("="*80)
            logger.info("🎉 BIAS CORRECTED CLASSIFICATION PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)

            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }

        except Exception as e:
            logger.error(f"Bias corrected pipeline failed: {str(e)}")
            raise

    def _generate_bias_corrected_summary(self, classified_data):
        """Generate summary for bias-corrected classification results"""
        if 'corrected_fatigue_risk' in classified_data.columns:
            distribution = classified_data['corrected_fatigue_risk'].value_counts(normalize=True) * 100
            return {
                'total_observations': len(classified_data),
                'fatigue_percentages': distribution.to_dict(),
                'target_column': 'corrected_fatigue_risk'
            }
        else:
            return {
                'total_observations': len(classified_data),
                'fatigue_percentages': {},
                'target_column': 'corrected_fatigue_risk'
            }
    
    def run_complete_pipeline(self):
        """Execute complete analysis pipeline"""
        logger.info("🚀 STARTING COMPLETE ANALYSIS PIPELINE WITH BIAS CORRECTION")
        logger.info("Includes: Data Processing + Bias-Corrected Fatigue Prediction + ML Models")
        logger.info("="*80)

        try:
            # Phase 1: Data Processing
            processed_data = self.run_data_processing()

            # Phase 2: Bias-Corrected Fatigue Prediction (if enabled)
            ml_results = None
            if self.include_ml:
                ml_results = self.run_bias_corrected_prediction(processed_data)

            # Final Summary
            self._print_final_summary(processed_data, ml_results)
            
            logger.info("="*80)
            logger.info("🎉 COMPLETE BIAS-CORRECTED PIPELINE FINISHED SUCCESSFULLY!")
            logger.info("="*80)
            
            return {
                'ml_results': ml_results,
                'processed_data': processed_data
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            raise

    def _print_bias_corrected_only_summary(self, data, ml_results):
        """Print summary for bias corrected only pipeline"""

        print("\n" + "="*80)
        print("🎉 BIAS CORRECTED FATIGUE CLASSIFICATION PIPELINE SUMMARY")
        print("="*80)

        # Data Summary
        print(f"\n📊 DATA SUMMARY:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")

        # ML Results Summary
        if ml_results:
            print(f"\n🤖 BIAS CORRECTED FATIGUE PREDICTION & ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Bias Corrected Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Bias correction status
            if ml_results.get('bias_correction_applied'):
                print(f"   • 🔧 Bias Correction: ✅ APPLIED")
                if 'bias_correction_analysis' in ml_results:
                    analysis = ml_results['bias_correction_analysis']
                    print(f"   • Language patterns: {analysis.get('language_distribution', {})}")
                    print(f"   • Activity types: {analysis.get('activity_distribution', {})}")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best ML Model: {ml_results['best_model']}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/fatigue_classified.csv")
        if ml_results and ml_results.get('safe_dataset_path'):
            print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
        if ml_results and ml_results.get('best_accuracy'):
            print(f"   • results/clean_production_model/*.pkl")
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Models trained with bias correction data leakage prevention")

        print(f"\n✅ Bias corrected fatigue classification completed successfully!")
        print("="*80)

    def _print_final_summary(self, data, ml_results):
        """Print comprehensive final summary"""

        print("\n" + "="*80)
        print("🎉 COMPLETE BIAS-CORRECTED ANALYSIS PIPELINE SUMMARY")
        print("="*80)

        # Data Processing Summary
        print(f"\n📊 DATA PROCESSING:")
        print(f"   • {len(data)} weekly observations processed")
        print(f"   • {data['identity'].nunique() if 'identity' in data.columns else 'N/A'} unique participants")
        
        # ML Results Summary
        if ml_results:
            print(f"\n🤖 BIAS-CORRECTED FATIGUE PREDICTION & ML MODELS:")
            if 'classification_summary' in ml_results:
                dist = ml_results['classification_summary']['fatigue_percentages']
                print(f"   • Bias-Corrected Fatigue Risk Distribution:")
                print(f"     - High Risk: {dist.get('high_risk', 0):.1f}%")
                print(f"     - Medium Risk: {dist.get('medium_risk', 0):.1f}%")
                print(f"     - Low Risk: {dist.get('low_risk', 0):.1f}%")

            # Bias correction status
            if ml_results.get('bias_correction_applied'):
                print(f"   • 🔧 Bias Correction: ✅ APPLIED")
                if 'bias_correction_analysis' in ml_results:
                    analysis = ml_results['bias_correction_analysis']
                    print(f"   • Language patterns detected: {len(analysis.get('language_distribution', {}))}")
                    print(f"   • Activity types detected: {len(analysis.get('activity_distribution', {}))}")

            # Feature filtering status
            if ml_results.get('data_leakage_prevented'):
                print(f"   • 🛡️ Data Leakage Prevention: ✅ ENABLED")
                print(f"   • Safe Dataset: {ml_results.get('safe_dataset_path', 'N/A')}")
            else:
                print(f"   • 🛡️ Data Leakage Prevention: ❌ DISABLED")

            if ml_results.get('best_accuracy'):
                print(f"   • Best ML Model: {ml_results['best_model']}")
                print(f"   • Best Accuracy: {ml_results['best_accuracy']:.4f} ({ml_results['best_accuracy']*100:.2f}%)")

                if 'advanced_result' in ml_results:
                    adv = ml_results['advanced_result']
                    print(f"   • Advanced Model: {adv['accuracy_mean']:.4f} ({adv['accuracy_mean']*100:.2f}%)")
                    if 'f1_mean' in adv:
                        print(f"   • F1-Score: {adv['f1_mean']:.4f}")

                # Model safety note
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • ✅ Model trained with bias correction safe features (no data leakage)")
                else:
                    print(f"   • ⚠️ Model may have data leakage risk from bias correction")
        
        # Files Generated
        print(f"\n📁 FILES GENERATED:")
        print(f"   • dataset/processed/weekly_merged_dataset_with_gamification.csv")
        if ml_results:
            print(f"   • dataset/processed/fatigue_classified.csv")
            if ml_results.get('safe_dataset_path'):
                print(f"   • {ml_results['safe_dataset_path']} (safe for ML)")
            if ml_results.get('best_accuracy'):
                print(f"   • results/clean_production_model/*.pkl")
                if ml_results.get('data_leakage_prevented'):
                    print(f"   • 🛡️ Models trained with bias correction data leakage prevention")

        print(f"\n✅ All analyses completed successfully with bias correction!")
        print("="*80)


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Complete Analysis Pipeline with Bias Correction')
    parser.add_argument('--no-ml', action='store_true',
                       help='Skip machine learning components (data processing only)')
    parser.add_argument('--ml-only', action='store_true',
                       help='Run only machine learning pipeline')
    parser.add_argument('--bias-corrected-only', action='store_true',
                       help='Run only bias corrected classification with feature filtering')
    parser.add_argument('--complete', action='store_true',
                       help='Run complete pipeline (default behavior)')

    args = parser.parse_args()
    
    try:
        # Check if raw data exists (unless ml-only)
        if not args.ml_only:
            raw_data_path = Path("dataset/raw")
            if not raw_data_path.exists():
                print("❌ Raw data directory not found!")
                print("Please ensure dataset/raw/ contains strava.csv and pomokit.csv")
                return
            
            strava_file = raw_data_path / "strava.csv"
            pomokit_file = raw_data_path / "pomokit.csv"
            
            if not strava_file.exists() or not pomokit_file.exists():
                print("❌ Required data files not found!")
                print(f"Expected files:")
                print(f"  - {strava_file}")
                print(f"  - {pomokit_file}")
                return
        
        # Run appropriate pipeline
        if args.ml_only:
            # Create safe dataset first
            print("🛡️  Creating safe ML dataset (removing label creation features)...")
            feature_filter = FeatureFilter2()
            safe_ml_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
            feature_filter.create_safe_dataset_for_bias_corrected_classifier(
                input_path='dataset/processed/fatigue_classified.csv',
                output_path=safe_ml_path,
                target_column='corrected_fatigue_risk'
            )

            # Run only ML pipeline with safe dataset
            from src.main_clean_pipeline import FatiguePredictionPipeline
            pipeline = FatiguePredictionPipeline(
                data_path=safe_ml_path,  # Use safe dataset
                target_column='corrected_fatigue_risk',
                random_state=42
            )
            results = pipeline.run_complete_pipeline()
            print(f"\n🎉 ML Pipeline Completed!")
            print(f"📋 Results: {results}")
        elif args.bias_corrected_only:
            # Run only bias corrected classification with feature filtering
            print("🚀 Running Bias Corrected Classification Pipeline with Feature Filtering...")
            pipeline = BiasCorrectedAnalysisPipeline(include_ml=True)
            results = pipeline.run_bias_corrected_only_pipeline()
            print(f"\n🎉 Bias Corrected Classification Pipeline Completed!")
        else:
            # Run complete pipeline (default)
            include_ml = not args.no_ml
            pipeline = BiasCorrectedAnalysisPipeline(include_ml=include_ml)
            pipeline.run_complete_pipeline()
        
    except KeyboardInterrupt:
        print("\n❌ Pipeline interrupted by user")
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
