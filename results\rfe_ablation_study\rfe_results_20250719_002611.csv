algorithm,algorithm_name,n_features_selected,selected_features,accuracy_mean,accuracy_std,f1_macro_mean,f1_macro_std,precision_macro_mean,recall_macro_mean,train_accuracy_mean,overfitting_score,status
logistic_regression,Logistic Regression,4,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'work_days']",0.6399999999999999,0.04027681991198188,0.6284441724801699,0.047671060818102615,0.6427011756733119,0.64789932990494,0.6825,0.04250000000000009,success
logistic_regression,Logistic Regression,5,"['pomokit_title_count', 'productivity_points', 'total_cycles', 'pomokit_unique_words', 'work_days']",0.65,0.05055250296034364,0.6366202126302128,0.05408380167444042,0.6501204317549998,0.6534548854604956,0.685,0.03500000000000003,success
logistic_regression,Logistic Regression,9,"['total_distance_km', 'pomokit_title_count', 'productivity_points', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6566666666666666,0.04546060565661951,0.6355407861335417,0.05663860843876291,0.6463196532384519,0.6467882187938289,0.7158333333333334,0.05916666666666681,success
logistic_regression,Logistic Regression,10,"['total_distance_km', 'pomokit_title_count', 'productivity_points', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6599999999999999,0.054365021434333596,0.6429047164989187,0.07497740945878181,0.6484715362093991,0.6578276453171263,0.7216666666666667,0.06166666666666676,success
logistic_regression,Logistic Regression,13,"['gamification_balance', 'total_distance_km', 'pomokit_title_count', 'total_time_minutes', 'productivity_points', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6666666666666666,0.05055250296034365,0.6477817896498906,0.07312764375585032,0.6517866651245123,0.6622004051737572,0.7275,0.060833333333333406,success
logistic_regression,Logistic Regression,15,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'productivity_points', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6566666666666666,0.04294699575575042,0.6344341343435651,0.05821195325261284,0.6435519335167093,0.6467882187938289,0.73,0.07333333333333336,success
logistic_regression,Logistic Regression,18,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'strava_title_count', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6566666666666666,0.0522812904711937,0.639937588766148,0.07164766871670344,0.6455024000823683,0.6551215521271622,0.7283333333333333,0.07166666666666666,success
random_forest,Random Forest,4,"['total_title_diversity', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio']",0.6766666666666666,0.059254629448770586,0.6233423834770738,0.06401437955122663,0.6765874230202786,0.6088725261025402,1.0,0.32333333333333336,success
random_forest,Random Forest,5,"['pomokit_title_count', 'total_title_diversity', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio']",0.6666666666666667,0.07745966692414831,0.6056446090616783,0.09679140629599516,0.6369502848949009,0.5925642823749417,1.0,0.33333333333333326,success
random_forest,Random Forest,9,"['total_distance_km', 'pomokit_title_count', 'total_time_minutes', 'total_title_diversity', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio']",0.6833333333333333,0.05577733510227169,0.6125564735043619,0.06820890231407593,0.6557602318970385,0.5947685834502103,1.0,0.31666666666666665,success
random_forest,Random Forest,10,"['total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'total_title_diversity', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio']",0.6833333333333332,0.05055250296034368,0.6215762368809656,0.0652694809398221,0.6608285059009696,0.6043338008415147,1.0,0.31666666666666676,success
random_forest,Random Forest,13,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'productivity_points', 'total_title_diversity', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6833333333333333,0.04944132324730444,0.6134134933684432,0.05939540746687604,0.6547222368719494,0.5957589216144615,1.0,0.31666666666666665,success
random_forest,Random Forest,15,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.68,0.06446359868604574,0.6144512555418709,0.0559267773460333,0.6678071254983226,0.5917710768271778,1.0,0.31999999999999995,success
random_forest,Random Forest,18,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'strava_title_count', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6900000000000001,0.07498147919467997,0.6112692805356362,0.08436556449419408,0.6461606872465506,0.5973266323827334,1.0,0.30999999999999994,success
gradient_boosting,Gradient Boosting,4,"['total_time_minutes', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6566666666666667,0.0573488351136175,0.603624079868119,0.06804240937730723,0.6198314749766362,0.5972011843540594,0.99,0.33333333333333326,success
gradient_boosting,Gradient Boosting,5,"['total_time_minutes', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6533333333333333,0.06091888960832356,0.6104883223887432,0.05645963917736785,0.6326094037587777,0.5994732741156302,0.9958333333333333,0.3425,success
gradient_boosting,Gradient Boosting,9,"['total_distance_km', 'avg_time_minutes', 'total_time_minutes', 'total_title_diversity', 'achievement_rate', 'avg_distance_km', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.67,0.02666666666666666,0.632318191121033,0.0528634401863941,0.6783482990296122,0.6110947483247624,1.0,0.32999999999999996,success
gradient_boosting,Gradient Boosting,10,"['total_distance_km', 'avg_time_minutes', 'total_time_minutes', 'total_title_diversity', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6766666666666666,0.042946995755750395,0.6427020628364786,0.053753646024049646,0.730067162369794,0.616166432912576,0.9991666666666668,0.3225000000000001,success
gradient_boosting,Gradient Boosting,13,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6700000000000002,0.04876246279442596,0.638736436774545,0.03664266345933139,0.6772936639639058,0.6201270063892785,1.0,0.32999999999999985,success
gradient_boosting,Gradient Boosting,15,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6599999999999999,0.0429469957557504,0.6193820692337819,0.026929354862566555,0.6522586558990067,0.6034829359513791,0.9991666666666668,0.33916666666666684,success
gradient_boosting,Gradient Boosting,18,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'strava_title_count', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.6533333333333333,0.045215533220835116,0.6066264598135825,0.05704562792256375,0.6456723457677218,0.5902703755649057,0.9991666666666668,0.34583333333333344,success
svm,Support Vector Machine,4,"['achievement_rate', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6233333333333333,0.062003584125794216,0.5974221144958471,0.06821188467561821,0.5964439075355884,0.6525315568022441,0.6741666666666667,0.0508333333333334,success
svm,Support Vector Machine,5,"['gamification_balance', 'achievement_rate', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6233333333333333,0.06960204339273698,0.6070951922627947,0.0762611794667704,0.597427690405533,0.660188561633162,0.6816666666666666,0.05833333333333335,success
svm,Support Vector Machine,9,"['gamification_balance', 'total_distance_km', 'productivity_points', 'consistency_score', 'achievement_rate', 'avg_distance_km', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.6633333333333333,0.049888765156985884,0.6368551051364209,0.05630121126350061,0.6254726564466431,0.684978182951535,0.7083333333333334,0.04500000000000004,success
svm,Support Vector Machine,10,"['gamification_balance', 'total_distance_km', 'productivity_points', 'consistency_score', 'achievement_rate', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'title_balance_ratio', 'work_days']",0.65,0.05055250296034366,0.6242459805756857,0.05386430053480397,0.6130128449604323,0.6754846501480443,0.7058333333333333,0.05583333333333329,success
svm,Support Vector Machine,13,"['gamification_balance', 'total_distance_km', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.65,0.05962847939999438,0.6223242045660032,0.0620510804868598,0.6122929961389891,0.6673437743493844,0.7108333333333333,0.060833333333333295,success
svm,Support Vector Machine,15,"['gamification_balance', 'total_distance_km', 'pomokit_title_count', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.64,0.060184900284225955,0.6126965618550358,0.057014497843119065,0.6034343024283348,0.6601932367149759,0.7091666666666667,0.06916666666666671,success
svm,Support Vector Machine,18,"['gamification_balance', 'total_distance_km', 'avg_time_minutes', 'pomokit_title_count', 'total_time_minutes', 'strava_title_count', 'productivity_points', 'total_title_diversity', 'consistency_score', 'achievement_rate', 'strava_unique_words', 'avg_distance_km', 'total_cycles', 'activity_days', 'pomokit_unique_words', 'activity_points', 'title_balance_ratio', 'work_days']",0.64,0.0646357314322177,0.620656188525899,0.06576705985297739,0.6098080901196449,0.6601932367149759,0.7125000000000001,0.07250000000000012,success
